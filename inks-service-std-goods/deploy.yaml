apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: svc-goods
  name: svc-goods
  namespace: inksoms   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: svc-goods
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: svc-goods
    spec:
      imagePullSecrets:
        - name: aliyun-docker-hub  #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/svc-goods:SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: svc-goods
          ports:
            - containerPort: 8080
              protocol: TCP
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: svc-goods
  name: svc-goods
  namespace: inksoms
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
      nodePort: 30280
  selector:
    app: svc-goods
  sessionAffinity: None
  type: NodePort