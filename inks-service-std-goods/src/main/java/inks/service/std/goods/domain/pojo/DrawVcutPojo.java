package inks.service.std.goods.domain.pojo;

/**
 * PCB画图 pojo
 *
 * <AUTHOR>
 */
public class DrawVcutPojo {
    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String vtype;

    /**
     * 类型
     */
    private String angv;

    /**
     * 类型
     */
    private String res;

    /**
     * 类型
     */
    private String warp;

    /**
     * 类型
     */
    private String angi;

    /**
     * 类型
     */
    private String lon;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVtype() {
        return vtype;
    }

    public void setVtype(String vtype) {
        this.vtype = vtype;
    }

    public String getAngv() {
        return angv;
    }

    public void setAngv(String angv) {
        this.angv = angv;
    }

    public String getRes() {
        return res;
    }

    public void setRes(String res) {
        this.res = res;
    }

    public String getWarp() {
        return warp;
    }

    public void setWarp(String warp) {
        this.warp = warp;
    }

    public String getAngi() {
        return angi;
    }

    public void setAngi(String angi) {
        this.angi = angi;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }
}
