package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatBomorderitemPojo;

import java.util.List;

/**
 * 订单Bom项目(MatBomorderitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-08 10:21:37
 */
public interface MatBomorderitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomorderitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBomorderitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatBomorderitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matBomorderitemPojo 实例对象
     * @return 实例对象
     */
    MatBomorderitemPojo insert(MatBomorderitemPojo matBomorderitemPojo);

    /**
     * 修改数据
     *
     * @param matBomorderitempojo 实例对象
     * @return 实例对象
     */
    MatBomorderitemPojo update(MatBomorderitemPojo matBomorderitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matBomorderitempojo 实例对象
     * @return 实例对象
     */
    MatBomorderitemPojo clearNull(MatBomorderitemPojo matBomorderitempojo);
}
