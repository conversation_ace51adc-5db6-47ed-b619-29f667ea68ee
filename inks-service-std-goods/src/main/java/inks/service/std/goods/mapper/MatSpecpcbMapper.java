package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.common.core.domain.WorkgroupPojo;
import inks.service.std.goods.domain.MatSpecpcbEntity;
import inks.service.std.goods.domain.pojo.MatSpecpcbPojo;
import inks.service.std.goods.domain.pojo.MatSpecpcbitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Pcb工艺(MatSpecpcb)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-16 10:40:34
 */
@Mapper
public interface MatSpecpcbMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecpcbPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecpcbitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecpcbPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSpecpcbEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpecpcbEntity matSpecpcbEntity);


    /**
     * 修改数据
     *
     * @param matSpecpcbEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpecpcbEntity matSpecpcbEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matSpecpcbPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(MatSpecpcbPojo matSpecpcbPojo);

    /**
     * 修改数据
     *
     * @param matSpecpcbEntity 实例对象
     * @return 影响行数
     */
    int approval(MatSpecpcbEntity matSpecpcbEntity);


    /**
     * 查询 被删除的Item
     *
     * @param matSpecpcbPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelDrawIds(MatSpecpcbPojo matSpecpcbPojo);

    /**
     * 查询 被删除的Item
     *
     * @param matSpecpcbPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelDrlIds(MatSpecpcbPojo matSpecpcbPojo);

    int checkGoodsid(@Param("goodsid") String goodsid, @Param("tenantid") String tenantid);

    MatSpecpcbPojo getEntityByGoodsid(String goodsid, String tenantid);

    List<MatSpecpcbPojo> getList();

    WorkgroupPojo getGroupInfo(String groupid, String tid);
}

