package inks.service.std.goods.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatAttributePojo;
import inks.service.std.goods.service.MatAttributeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * SPU属性表(Mat_Attribute)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-14 14:41:37
 */
@RestController
@RequestMapping("D91M01S2")
@Api(tags = "D91M01S2:SPU属性表")
public class D91M01S2Controller extends MatAttributeController {
    /**
     * 服务对象
     */
    @Resource
    private MatAttributeService matAttributeService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "获得显示的属性表 UseDomain(应用域)查spu: s销售\\b采购\\e技术\\w生产\\m仓库\\q品质\\f财务\\h人事", notes = "获得显示的属性表", produces = "application/json")
    @RequestMapping(value = "/getListByShow", method = RequestMethod.GET)
    public R<List<MatAttributePojo>> getListByShow(String ud) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matAttributeService.getListByShow(ud, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
