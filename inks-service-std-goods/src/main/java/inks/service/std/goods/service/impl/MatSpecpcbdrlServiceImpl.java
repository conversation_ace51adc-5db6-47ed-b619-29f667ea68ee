package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecpcbdrlEntity;
import inks.service.std.goods.domain.pojo.MatSpecpcbdrlPojo;
import inks.service.std.goods.mapper.MatSpecpcbdrlMapper;
import inks.service.std.goods.service.MatSpecpcbdrlService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Pcb工艺Drl(MatSpecpcbdrl)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:47
 */
@Service("matSpecpcbdrlService")
public class MatSpecpcbdrlServiceImpl implements MatSpecpcbdrlService {
    @Resource
    private MatSpecpcbdrlMapper matSpecpcbdrlMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecpcbdrlPojo getEntity(String key, String tid) {
        return this.matSpecpcbdrlMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecpcbdrlPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecpcbdrlPojo> lst = matSpecpcbdrlMapper.getPageList(queryParam);
            PageInfo<MatSpecpcbdrlPojo> pageInfo = new PageInfo<MatSpecpcbdrlPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSpecpcbdrlPojo> getList(String Pid, String tid) {
        try {
            List<MatSpecpcbdrlPojo> lst = matSpecpcbdrlMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSpecpcbdrlPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbdrlPojo insert(MatSpecpcbdrlPojo matSpecpcbdrlPojo) {
        //初始化item的NULL
        MatSpecpcbdrlPojo itempojo = this.clearNull(matSpecpcbdrlPojo);
        MatSpecpcbdrlEntity matSpecpcbdrlEntity = new MatSpecpcbdrlEntity();
        BeanUtils.copyProperties(itempojo, matSpecpcbdrlEntity);

        matSpecpcbdrlEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSpecpcbdrlEntity.setRevision(1);  //乐观锁
        this.matSpecpcbdrlMapper.insert(matSpecpcbdrlEntity);
        return this.getEntity(matSpecpcbdrlEntity.getId(), matSpecpcbdrlEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecpcbdrlPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbdrlPojo update(MatSpecpcbdrlPojo matSpecpcbdrlPojo) {
        MatSpecpcbdrlEntity matSpecpcbdrlEntity = new MatSpecpcbdrlEntity();
        BeanUtils.copyProperties(matSpecpcbdrlPojo, matSpecpcbdrlEntity);
        this.matSpecpcbdrlMapper.update(matSpecpcbdrlEntity);
        return this.getEntity(matSpecpcbdrlEntity.getId(), matSpecpcbdrlEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSpecpcbdrlMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSpecpcbdrlPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbdrlPojo clearNull(MatSpecpcbdrlPojo matSpecpcbdrlPojo) {
        //初始化NULL字段
        if (matSpecpcbdrlPojo.getPid() == null) matSpecpcbdrlPojo.setPid("");
        if (matSpecpcbdrlPojo.getSymbol() == null) matSpecpcbdrlPojo.setSymbol("");
        if (matSpecpcbdrlPojo.getHolesize() == null) matSpecpcbdrlPojo.setHolesize(0D);
        if (matSpecpcbdrlPojo.getTolerance() == null) matSpecpcbdrlPojo.setTolerance("");
        if (matSpecpcbdrlPojo.getDrillsize() == null) matSpecpcbdrlPojo.setDrillsize(0D);
        if (matSpecpcbdrlPojo.getPcstotal() == null) matSpecpcbdrlPojo.setPcstotal(0);
        if (matSpecpcbdrlPojo.getSettotal() == null) matSpecpcbdrlPojo.setSettotal(0);
        if (matSpecpcbdrlPojo.getPnltotal() == null) matSpecpcbdrlPojo.setPnltotal(0);
        if (matSpecpcbdrlPojo.getPthmark() == null) matSpecpcbdrlPojo.setPthmark("");
        if (matSpecpcbdrlPojo.getRownum() == null) matSpecpcbdrlPojo.setRownum(0);
        if (matSpecpcbdrlPojo.getRemark() == null) matSpecpcbdrlPojo.setRemark("");
        if (matSpecpcbdrlPojo.getCustom1() == null) matSpecpcbdrlPojo.setCustom1("");
        if (matSpecpcbdrlPojo.getCustom2() == null) matSpecpcbdrlPojo.setCustom2("");
        if (matSpecpcbdrlPojo.getCustom3() == null) matSpecpcbdrlPojo.setCustom3("");
        if (matSpecpcbdrlPojo.getCustom4() == null) matSpecpcbdrlPojo.setCustom4("");
        if (matSpecpcbdrlPojo.getCustom5() == null) matSpecpcbdrlPojo.setCustom5("");
        if (matSpecpcbdrlPojo.getCustom6() == null) matSpecpcbdrlPojo.setCustom6("");
        if (matSpecpcbdrlPojo.getCustom7() == null) matSpecpcbdrlPojo.setCustom7("");
        if (matSpecpcbdrlPojo.getCustom8() == null) matSpecpcbdrlPojo.setCustom8("");
        if (matSpecpcbdrlPojo.getCustom9() == null) matSpecpcbdrlPojo.setCustom9("");
        if (matSpecpcbdrlPojo.getCustom10() == null) matSpecpcbdrlPojo.setCustom10("");
        if (matSpecpcbdrlPojo.getTenantid() == null) matSpecpcbdrlPojo.setTenantid("");
        if (matSpecpcbdrlPojo.getRevision() == null) matSpecpcbdrlPojo.setRevision(0);
        if (matSpecpcbdrlPojo.getTenantname() == null) matSpecpcbdrlPojo.setTenantname("");
        return matSpecpcbdrlPojo;
    }
}
