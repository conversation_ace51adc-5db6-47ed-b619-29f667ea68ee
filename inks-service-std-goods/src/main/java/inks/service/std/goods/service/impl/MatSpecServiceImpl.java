package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecEntity;
import inks.service.std.goods.domain.MatSpecitemEntity;
import inks.service.std.goods.domain.pojo.MatSpecPojo;
import inks.service.std.goods.domain.pojo.MatSpecitemPojo;
import inks.service.std.goods.domain.pojo.MatSpecitemdetailPojo;
import inks.service.std.goods.mapper.MatSpecMapper;
import inks.service.std.goods.mapper.MatSpecitemMapper;
import inks.service.std.goods.service.MatSpecService;
import inks.service.std.goods.service.MatSpecitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工艺流程卡(MatSpec)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-16 10:38:34
 */
@Service("matSpecService")
public class MatSpecServiceImpl implements MatSpecService {
    @Resource
    private MatSpecMapper matSpecMapper;

    @Resource
    private MatSpecitemMapper matSpecitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private MatSpecitemService matSpecitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecPojo getEntity(String key, String tid) {
        return this.matSpecMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecitemdetailPojo> lst = matSpecMapper.getPageList(queryParam);
            PageInfo<MatSpecitemdetailPojo> pageInfo = new PageInfo<MatSpecitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatSpecPojo matSpecPojo = this.matSpecMapper.getEntity(key, tid);
            //读取子表
            matSpecPojo.setItem(matSpecitemMapper.getList(matSpecPojo.getId(), matSpecPojo.getTenantid()));
            return matSpecPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecPojo> lst = matSpecMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matSpecitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatSpecPojo> pageInfo = new PageInfo<MatSpecPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecPojo> lst = matSpecMapper.getPageTh(queryParam);
            PageInfo<MatSpecPojo> pageInfo = new PageInfo<MatSpecPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matSpecPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecPojo insert(MatSpecPojo matSpecPojo) {
//初始化NULL字段
        if (matSpecPojo.getRefno() == null) matSpecPojo.setRefno("");
        if (matSpecPojo.getBilldate() == null) matSpecPojo.setBilldate(new Date());
        if (matSpecPojo.getBilltype() == null) matSpecPojo.setBilltype("");
        if (matSpecPojo.getBilltitle() == null) matSpecPojo.setBilltitle("");
        if (matSpecPojo.getGoodsid() == null) matSpecPojo.setGoodsid("");
        if (matSpecPojo.getItemcode() == null) matSpecPojo.setItemcode("");
        if (matSpecPojo.getItemname() == null) matSpecPojo.setItemname("");
        if (matSpecPojo.getItemspec() == null) matSpecPojo.setItemspec("");
        if (matSpecPojo.getItemunit() == null) matSpecPojo.setItemunit("");
        if (matSpecPojo.getPnlx() == null) matSpecPojo.setPnlx(0D);
        if (matSpecPojo.getPnly() == null) matSpecPojo.setPnly(0D);
        if (matSpecPojo.getPnl2pcs() == null) matSpecPojo.setPnl2pcs(0);
        if (matSpecPojo.getPnlbx() == null) matSpecPojo.setPnlbx(0D);
        if (matSpecPojo.getPnlby() == null) matSpecPojo.setPnlby(0D);
        if (matSpecPojo.getPnlb2pcs() == null) matSpecPojo.setPnlb2pcs(0);
        if (matSpecPojo.getOperator() == null) matSpecPojo.setOperator("");
        if (matSpecPojo.getOperatorid() == null) matSpecPojo.setOperatorid("");
        if (matSpecPojo.getQuickkey() == null) matSpecPojo.setQuickkey("");
        if (matSpecPojo.getAttributejson() == null) matSpecPojo.setAttributejson("{}");
        if (matSpecPojo.getSummary() == null) matSpecPojo.setSummary("");
        if (matSpecPojo.getEnabledmark() == null) matSpecPojo.setEnabledmark(0);
        if (matSpecPojo.getVersionnum() == null) matSpecPojo.setVersionnum("");
        if (matSpecPojo.getCreateby() == null) matSpecPojo.setCreateby("");
        if (matSpecPojo.getCreatebyid() == null) matSpecPojo.setCreatebyid("");
        if (matSpecPojo.getCreatedate() == null) matSpecPojo.setCreatedate(new Date());
        if (matSpecPojo.getLister() == null) matSpecPojo.setLister("");
        if (matSpecPojo.getListerid() == null) matSpecPojo.setListerid("");
        if (matSpecPojo.getModifydate() == null) matSpecPojo.setModifydate(new Date());
        if (matSpecPojo.getAssessor() == null) matSpecPojo.setAssessor("");
        if (matSpecPojo.getAssessorid() == null) matSpecPojo.setAssessorid("");
        if (matSpecPojo.getAssessdate() == null) matSpecPojo.setAssessdate(new Date());
        if (matSpecPojo.getCustom1() == null) matSpecPojo.setCustom1("");
        if (matSpecPojo.getCustom2() == null) matSpecPojo.setCustom2("");
        if (matSpecPojo.getCustom3() == null) matSpecPojo.setCustom3("");
        if (matSpecPojo.getCustom4() == null) matSpecPojo.setCustom4("");
        if (matSpecPojo.getCustom5() == null) matSpecPojo.setCustom5("");
        if (matSpecPojo.getCustom6() == null) matSpecPojo.setCustom6("");
        if (matSpecPojo.getCustom7() == null) matSpecPojo.setCustom7("");
        if (matSpecPojo.getCustom8() == null) matSpecPojo.setCustom8("");
        if (matSpecPojo.getCustom9() == null) matSpecPojo.setCustom9("");
        if (matSpecPojo.getCustom10() == null) matSpecPojo.setCustom10("");
        if (matSpecPojo.getTenantid() == null) matSpecPojo.setTenantid("");
        if (matSpecPojo.getTenantname() == null) matSpecPojo.setTenantname("");
        if (matSpecPojo.getRevision() == null) matSpecPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatSpecEntity matSpecEntity = new MatSpecEntity();
        BeanUtils.copyProperties(matSpecPojo, matSpecEntity);
        //设置id和新建日期
        matSpecEntity.setId(id);
        matSpecEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matSpecMapper.insert(matSpecEntity);
        //Item子表处理
        List<MatSpecitemPojo> lst = matSpecPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatSpecitemPojo itemPojo = this.matSpecitemService.clearNull(lst.get(i));
                MatSpecitemEntity matSpecitemEntity = new MatSpecitemEntity();
                BeanUtils.copyProperties(itemPojo, matSpecitemEntity);
                //设置id和Pid
                matSpecitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matSpecitemEntity.setPid(id);
                matSpecitemEntity.setTenantid(matSpecPojo.getTenantid());
                matSpecitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matSpecitemMapper.insert(matSpecitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(matSpecEntity.getId(), matSpecEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecPojo update(MatSpecPojo matSpecPojo) {
        //主表更改
        MatSpecEntity matSpecEntity = new MatSpecEntity();
        BeanUtils.copyProperties(matSpecPojo, matSpecEntity);
        this.matSpecMapper.update(matSpecEntity);
        if (matSpecPojo.getItem() != null) {
            //Item子表处理
            List<MatSpecitemPojo> lst = matSpecPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = matSpecMapper.getDelItemIds(matSpecPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.matSpecitemMapper.delete(lstDelIds.get(i), matSpecEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    MatSpecitemEntity matSpecitemEntity = new MatSpecitemEntity();
                    if (lst.get(i).getId() == "" || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        MatSpecitemPojo itemPojo = this.matSpecitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, matSpecitemEntity);
                        //设置id和Pid
                        matSpecitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matSpecitemEntity.setPid(matSpecEntity.getId());  // 主表 id
                        matSpecitemEntity.setTenantid(matSpecPojo.getTenantid());   // 租户id
                        matSpecitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matSpecitemMapper.insert(matSpecitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), matSpecitemEntity);
                        matSpecitemEntity.setTenantid(matSpecPojo.getTenantid());
                        this.matSpecitemMapper.update(matSpecitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(matSpecEntity.getId(), matSpecEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        MatSpecPojo matSpecPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatSpecitemPojo> lst = matSpecPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (MatSpecitemPojo matSpecitemPojo : lst) {
                this.matSpecitemMapper.delete(matSpecitemPojo.getId(), tid);
            }
        }
        this.matSpecMapper.delete(key, tid);
        return matSpecPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param matSpecPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecPojo approval(MatSpecPojo matSpecPojo) {
        //主表更改
        MatSpecEntity matSpecEntity = new MatSpecEntity();
        BeanUtils.copyProperties(matSpecPojo, matSpecEntity);
        this.matSpecMapper.approval(matSpecEntity);
        //返回Bill实例
        return this.getBillEntity(matSpecEntity.getId(), matSpecEntity.getTenantid());
    }

}
