package inks.service.std.goods.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatGoodsPojo;
import inks.service.std.goods.service.MatGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 货品信息(Mat_Goods)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 08:36:12
 */
@RestController
@RequestMapping("D91M01B1")
@Api(tags = "D91M01B1:货品信息")
public class D91M01B1Controller extends MatGoodsController {

    /**
     * 服务对象
     */
    @Resource
    private MatGoodsService matGoodsService;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "根据料号获取货品信息", notes = "根据料号获取货品信息", produces = "application/json")
    @RequestMapping(value = "/getMapEntityByGoodsUid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<Map<String, Object>> getMapEntityByGoodsUid(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByGoodsUid(key, loginUser.getTenantid());
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(matGoods);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据料号获取货品信息", notes = "根据料号获取货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByGoodsUid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByGoodsUid(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByGoodsUid(key, loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据goodsname+goodsspec获取货品信息", notes = "根据料号获取货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByNameSpec", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByNameSpec(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatGoodsPojo matGoodsPojo = JSONObject.parseObject(json, MatGoodsPojo.class);
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByNameSpec(matGoodsPojo.getGoodsname(), matGoodsPojo.getGoodsspec(), loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据goodsname+goodsspec+Partid获取货品信息", notes = "根据料号获取货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByNameSpecPart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByNameSpecPart(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatGoodsPojo matGoodsPojo = JSONObject.parseObject(json, MatGoodsPojo.class);
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByNameSpecPart(matGoodsPojo.getGoodsname(), matGoodsPojo.getGoodsspec(), matGoodsPojo.getPartid(), loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "根据外部编码获取最新货品信息", notes = "根据外部编码获取最新货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByPartid", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByPartid(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Map<String, String> map = JSON.parseObject(json, new TypeReference<Map<String, String>>() {
        });
        String partid = map.get("partid");
        try {
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByPartid(partid.trim(), loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "XML转动态对象接口", notes = "解析xmld文件并返回MatGoodsPojo对象", produces = "application/json")
    @RequestMapping(value = "/getEntityByCsXml", method = RequestMethod.POST)
    public R<MatGoodsPojo> getEntityByCsXml(@RequestParam("file") MultipartFile file) {
        LoginUser loginUser = tokenService.getLoginUser();
        try {
            if (file.isEmpty()) {
                return R.fail("上传的文件为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".xmld")) {
                return R.fail("不支持的文件类型，请上传.xmld文件");
            }

            // 读取XML内容
            String xmlContent = new String(file.getBytes(), "UTF-8");
            
            // 调用Service层解析XML并转换为MatGoodsPojo
            MatGoodsPojo result = matGoodsService.parseXmlToMatGoodsPojo(xmlContent, loginUser);
            
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("处理XML文件时发生错误: " + e.getMessage());
        }
    }

    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public void exportList(@RequestBody String json, String groupid, HttpServletRequest request, HttpServletResponse response) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "") {
                queryParam.setOrderBy("CreateDate");
            }
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.deletemark=0";
            if (groupid != null) {
                qpfilter += " and Mat_Goods.Groupid='" + groupid + "'";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("货品案档", ""),
                    MatGoodsPojo.class, this.matGoodsService.getPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "货品案档");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询不包括指定货品", notes = "按条件分页查询不包括指定货品", produces = "application/json")
    @RequestMapping(value = "/getPageListUseBom", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getPageListUseBom(@RequestBody String json, String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), ""))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and Mat_Goods.GoodsState<>'成品' and Mat_Goods.id<>'" + key + "'";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询 安全库存预警", notes = "按条件分页查询 安全库存预警", produces = "application/json")
    @RequestMapping(value = "/getSafetyPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getSafetyPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "") {
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and Mat_Goods.IvQuantity<Mat_Goods.SafeStock and Mat_Goods.enabledmark=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 预设数量预警", notes = "按条件分页查询 安全库存预警", produces = "application/json")
    @RequestMapping(value = "/getAlertsQtyPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getAlertsQtyPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "") {
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            }
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and Mat_Goods.IvQuantity<Mat_Goods.AlertsQty and Mat_Goods.enabledmark=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据快速码获取最新货品信息", notes = "根据快速码获取最新货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByCode(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByQuickCode(key, loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询货品", notes = "按条件分页查询货品,state m物料,s半成品,p为成品", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getPageList(@RequestBody String json, String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), ""))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "没有Bomid的:按条件分页查询货品", notes = "按条件分页查询货品,state m物料,s半成品,p为成品", produces = "application/json")
    @RequestMapping(value = "/getNoBomPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getNoBomPageList(@RequestBody String json, String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), ""))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            qpfilter += " and (Mat_Goods.Bomid is null or Mat_Goods.Bomid='')";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询有效货品", notes = "按条件分页查询货品,state m物料,s半成品,p为成品", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getOnlinePageList(@RequestBody String json, String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0 and Mat_Goods.EnabledMark=1";
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "未转作业指示的货品", notes = "按条件分页查询货品,state m物料,s半成品,p为成品", produces = "application/json")
    @RequestMapping(value = "/getOnlineSpecPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getOnlineSpecPageList(@RequestBody String json, @RequestParam(required = false) String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), ""))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0 and Mat_Goods.EnabledMark=1";
            qpfilter += " and Mat_Goods.Specid=''"; //未转作业指示的货品
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询 产成品信息", notes = "按条件分页查询 产成品（成品与半成品）", produces = "application/json")
    @RequestMapping(value = "/getProPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getProPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and (Mat_Goods.GoodsState='成品' or Mat_Goods.GoodsState='半成品')";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询 物料信息", notes = "按条件分页查询 物料信息", produces = "application/json")
    @RequestMapping(value = "/getMatPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getMatPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and Mat_Goods.GoodsState='物料'";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "批量更新货品库存总量", notes = "批量更新货品库存总量", produces = "application/json")
    @RequestMapping(value = "/updateIvQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateIvQty() {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateIvQty(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询 虚拟品", notes = "按条件分页查询 虚拟品", produces = "application/json")
    @RequestMapping(value = "/getVirPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getVirPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询 有效虚拟品", notes = "按条件分页查询 虚拟品", produces = "application/json")
    @RequestMapping(value = "/getVirOnlinePageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getVirOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=1 and Mat_Goods.EnabledMark=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 未检验货品", notes = "按条件分页查询 虚拟品", produces = "application/json")
    @RequestMapping(value = "/getInspOnlinPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getInspOnlinPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            // 已检验货品 inspid有值
            String qpfilter = " and (Mat_Goods.Inspid is null or Mat_Goods.Inspid='') and Mat_Goods.EnabledMark=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增虚似品信息", notes = "新增虚似品信息", produces = "application/json")
    @RequestMapping(value = "/createVir", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.Add")
    public R<MatGoodsPojo> createVir(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        MatGoodsPojo matGoodsPojo = JSONArray.parseObject(json, MatGoodsPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        matGoodsPojo.setCreateby(loginUser.getRealname());   // 创建者
        matGoodsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        matGoodsPojo.setCreatedate(new Date());   // 创建时间
        matGoodsPojo.setLister(loginUser.getRealname());   // 制表
        matGoodsPojo.setListerid(loginUser.getUserid());    // 制表id
        matGoodsPojo.setModifydate(new Date());   //修改时间
        matGoodsPojo.setTenantid(loginUser.getTenantid());   //租户id
        matGoodsPojo.setVirtualitem(1);
        // 读取指定系统参数 货品建立是否允许重名 true/false
        String allow = systemFeignService.getConfigValue("module.goods.allowduplicatenames", matGoodsPojo.getTenantid(), loginUser.getToken()).getData();
        return R.ok(this.matGoodsService.insert(matGoodsPojo, allow, warn));
    }

    /**
     * 刷新销售待出数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新销售待出数", notes = "刷新销售待出数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsBusRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsBusRemQty(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsBusRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新收货待入数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新收货待入数", notes = "刷新收货待入数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsBuyRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsBuyRemQty(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsBuyRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新生产待入数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新生产待入数", notes = "刷新生产待入数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsWkWsRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsWkWsRemQty(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsWkWsRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新加工待入数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新加工待入数", notes = "刷新加工待入数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsWkScRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsWkScRemQty(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsWkScRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新领料待出数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新领料待出数", notes = "刷新领料待出数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsRequRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsRequRemQty(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsRequRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新一个货品的当前库存数和库存单价
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新当前库存数和库存单价", notes = "刷新当前库存数和库存单价", produces = "application/json")
    @RequestMapping(value = "/updateGoodsIvQuantity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsIvQuantity(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsIvQuantity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "清空货品 (GoodsState=成品/半成品/物料..不传则删全部)", notes = "", produces = "application/json")
    @RequestMapping(value = "/cleanGoods", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> cleanGoods(@RequestParam(required = false) String goodsstate) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.cleanGoods(goodsstate, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "清空客户 (grouptype=客户/供应商/生产车间/外协厂商/其他部门/潜在客户..不传则删全部)", notes = "", produces = "application/json")
    @RequestMapping(value = "/cleanWorkgroup", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> cleanWorkgroup(@RequestParam(required = false) String grouptype) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.cleanWorkgroup(grouptype, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
