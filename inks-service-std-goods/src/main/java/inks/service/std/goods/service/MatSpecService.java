package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecPojo;
import inks.service.std.goods.domain.pojo.MatSpecitemdetailPojo;

/**
 * 工艺流程卡(MatSpec)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-16 10:38:33
 */
public interface MatSpecService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSpecPojo 实例对象
     * @return 实例对象
     */
    MatSpecPojo insert(MatSpecPojo matSpecPojo);

    /**
     * 修改数据
     *
     * @param matSpecpojo 实例对象
     * @return 实例对象
     */
    MatSpecPojo update(MatSpecPojo matSpecpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matSpecPojo 实例对象
     * @return 实例对象
     */
    MatSpecPojo approval(MatSpecPojo matSpecPojo);
}
