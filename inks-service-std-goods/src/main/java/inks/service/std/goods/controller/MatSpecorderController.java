package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatSpecorderPojo;
import inks.service.std.goods.domain.pojo.MatSpecorderitemPojo;
import inks.service.std.goods.domain.pojo.MatSpecorderitemdetailPojo;
import inks.service.std.goods.mapper.MatSpecorderMapper;
import inks.service.std.goods.service.MatSpecorderService;
import inks.service.std.goods.service.MatSpecorderitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 订单工艺卡(MatSpecorder)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-16 14:42:33
 */

public class MatSpecorderController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(MatSpecorderController.class);
    private final String moduleCode = "D91M10B1";
    /**
     * 服务对象
     */
    @Resource
    private MatSpecorderService matSpecorderService;
    @Resource
    private MatSpecorderMapper matSpecorderMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private MatSpecorderitemService matSpecorderitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取订单工艺卡详细信息", notes = "获取订单工艺卡详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.List")
    public R<MatSpecorderPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matSpecorderService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.List")
    public R<PageInfo<MatSpecorderitemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_SpecOrder.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matSpecorderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取订单工艺卡详细信息", notes = "获取订单工艺卡详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.List")
    public R<MatSpecorderPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matSpecorderService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.List")
    public R<PageInfo<MatSpecorderPojo>> getBillList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_SpecOrder.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.matSpecorderService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.List")
    public R<PageInfo<MatSpecorderPojo>> getPageTh(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_SpecOrder.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matSpecorderService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增订单工艺卡", notes = "新增订单工艺卡", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.Add")
    public R<MatSpecorderPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatSpecorderPojo matSpecorderPojo = JSONArray.parseObject(json, MatSpecorderPojo.class);
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Mat_SpecOrder", null, loginUser.getTenantid());
            matSpecorderPojo.setRefno(refno);
            matSpecorderPojo.setCreateby(loginUser.getRealName());   // 创建者
            matSpecorderPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matSpecorderPojo.setCreatedate(new Date());   // 创建时间
            matSpecorderPojo.setLister(loginUser.getRealname());   // 制表
            matSpecorderPojo.setListerid(loginUser.getUserid());    // 制表id            
            matSpecorderPojo.setModifydate(new Date());   //修改时间
            matSpecorderPojo.setTenantid(loginUser.getTenantid());   //租户id
            MatSpecorderPojo insert = this.matSpecorderService.insert(matSpecorderPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改订单工艺卡", notes = "修改订单工艺卡", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.Edit")
    public R<MatSpecorderPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatSpecorderPojo matSpecorderPojo = JSONArray.parseObject(json, MatSpecorderPojo.class);
            matSpecorderPojo.setLister(loginUser.getRealname());   // 制表
            matSpecorderPojo.setListerid(loginUser.getUserid());    // 制表id   
            matSpecorderPojo.setModifydate(new Date());   //修改时间
            matSpecorderPojo.setAssessor(""); //审核员
            matSpecorderPojo.setAssessorid(""); //审核员
            matSpecorderPojo.setAssessdate(new Date()); //审核时间
            matSpecorderPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matSpecorderService.update(matSpecorderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除订单工艺卡", notes = "删除订单工艺卡", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String refno = this.matSpecorderService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增订单工艺卡Item", notes = "新增订单工艺卡Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.Add")
    public R<MatSpecorderitemPojo> createItem(@RequestBody String json) {
        try {
            MatSpecorderitemPojo matSpecorderitemPojo = JSONArray.parseObject(json, MatSpecorderitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            matSpecorderitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.matSpecorderitemService.insert(matSpecorderitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除订单工艺卡Item", notes = "删除订单工艺卡Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            MatSpecorderPojo matSpecorderPojo = this.matSpecorderService.getEntity(key, loginUser.getTenantid());
            // 同步销售订单子表的SpecStatus字段为0,
            if (StringUtils.isNotBlank(matSpecorderPojo.getMachitemid())) {
                this.matSpecorderMapper.updateMachItemSpecStatus(0, matSpecorderPojo.getMachitemid(), loginUser.getTenantid());
            }
            MatSpecorderitemPojo matSpecorderitemPojo = this.matSpecorderitemService.getEntity(key, loginUser.getTenantid());
            return R.ok(this.matSpecorderitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核订单工艺卡", notes = "审核订单工艺卡", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.Approval")
    public R<MatSpecorderPojo> approval(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            MatSpecorderPojo matSpecorderPojo = this.matSpecorderService.getEntity(key, loginUser.getTenantid());
            // 同步销售订单子表的SpecStatus字段为1,表示订单工艺卡已审核
            if (StringUtils.isNotBlank(matSpecorderPojo.getMachitemid())) {
                this.matSpecorderMapper.updateMachItemSpecStatus(1, matSpecorderPojo.getMachitemid(), loginUser.getTenantid());
            }
            if (matSpecorderPojo.getAssessor().equals("")) {
                matSpecorderPojo.setAssessor(loginUser.getRealname()); //审核员
                matSpecorderPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                matSpecorderPojo.setAssessor(""); //审核员
                matSpecorderPojo.setAssessorid(""); //审核员
            }
            matSpecorderPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matSpecorderService.approval(matSpecorderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecOrder.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatSpecorderPojo matSpecorderPojo = this.matSpecorderService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matSpecorderPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = matSpecorderPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    MatSpecorderitemPojo matSpecorderitemPojo = new MatSpecorderitemPojo();
                    matSpecorderPojo.getItem().add(matSpecorderitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(matSpecorderPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

