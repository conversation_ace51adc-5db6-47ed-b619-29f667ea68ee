package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSubstituteitemEntity;
import inks.service.std.goods.domain.pojo.MatSubstituteitemPojo;
import inks.service.std.goods.mapper.MatSubstituteitemMapper;
import inks.service.std.goods.service.MatSubstituteitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 替代料项目(MatSubstituteitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-06 11:08:38
 */
@Service("matSubstituteitemService")
public class MatSubstituteitemServiceImpl implements MatSubstituteitemService {
    @Resource
    private MatSubstituteitemMapper matSubstituteitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSubstituteitemPojo getEntity(String key, String tid) {
        return this.matSubstituteitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSubstituteitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSubstituteitemPojo> lst = matSubstituteitemMapper.getPageList(queryParam);
            PageInfo<MatSubstituteitemPojo> pageInfo = new PageInfo<MatSubstituteitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSubstituteitemPojo> getList(String Pid, String tid) {
        try {
            List<MatSubstituteitemPojo> lst = matSubstituteitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSubstituteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSubstituteitemPojo insert(MatSubstituteitemPojo matSubstituteitemPojo) {
        //初始化item的NULL
        MatSubstituteitemPojo itempojo = this.clearNull(matSubstituteitemPojo);
        MatSubstituteitemEntity matSubstituteitemEntity = new MatSubstituteitemEntity();
        BeanUtils.copyProperties(itempojo, matSubstituteitemEntity);

        matSubstituteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSubstituteitemEntity.setRevision(1);  //乐观锁
        this.matSubstituteitemMapper.insert(matSubstituteitemEntity);
        return this.getEntity(matSubstituteitemEntity.getId(), matSubstituteitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSubstituteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSubstituteitemPojo update(MatSubstituteitemPojo matSubstituteitemPojo) {
        MatSubstituteitemEntity matSubstituteitemEntity = new MatSubstituteitemEntity();
        BeanUtils.copyProperties(matSubstituteitemPojo, matSubstituteitemEntity);
        this.matSubstituteitemMapper.update(matSubstituteitemEntity);
        return this.getEntity(matSubstituteitemEntity.getId(), matSubstituteitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSubstituteitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSubstituteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSubstituteitemPojo clearNull(MatSubstituteitemPojo matSubstituteitemPojo) {
        //初始化NULL字段
        if (matSubstituteitemPojo.getPid() == null) matSubstituteitemPojo.setPid("");
        if (matSubstituteitemPojo.getGoodsid() == null) matSubstituteitemPojo.setGoodsid("");
        if (matSubstituteitemPojo.getItemcode() == null) matSubstituteitemPojo.setItemcode("");
        if (matSubstituteitemPojo.getItemname() == null) matSubstituteitemPojo.setItemname("");
        if (matSubstituteitemPojo.getItemspec() == null) matSubstituteitemPojo.setItemspec("");
        if (matSubstituteitemPojo.getItemunit() == null) matSubstituteitemPojo.setItemunit("");
        if (matSubstituteitemPojo.getMainqty() == null) matSubstituteitemPojo.setMainqty(0D);
        if (matSubstituteitemPojo.getSubqty() == null) matSubstituteitemPojo.setSubqty(0D);
        if (matSubstituteitemPojo.getSubrate() == null) matSubstituteitemPojo.setSubrate(0D);
        if (matSubstituteitemPojo.getStratdate() == null) matSubstituteitemPojo.setStratdate(new Date());
        if (matSubstituteitemPojo.getEnddate() == null) matSubstituteitemPojo.setEnddate(new Date());
        if (matSubstituteitemPojo.getPriority() == null) matSubstituteitemPojo.setPriority(0);
        if (matSubstituteitemPojo.getClosed() == null) matSubstituteitemPojo.setClosed(0);
        if (matSubstituteitemPojo.getRownum() == null) matSubstituteitemPojo.setRownum(0);
        if (matSubstituteitemPojo.getRemark() == null) matSubstituteitemPojo.setRemark("");
        if (matSubstituteitemPojo.getCustom1() == null) matSubstituteitemPojo.setCustom1("");
        if (matSubstituteitemPojo.getCustom2() == null) matSubstituteitemPojo.setCustom2("");
        if (matSubstituteitemPojo.getCustom3() == null) matSubstituteitemPojo.setCustom3("");
        if (matSubstituteitemPojo.getCustom4() == null) matSubstituteitemPojo.setCustom4("");
        if (matSubstituteitemPojo.getCustom5() == null) matSubstituteitemPojo.setCustom5("");
        if (matSubstituteitemPojo.getCustom6() == null) matSubstituteitemPojo.setCustom6("");
        if (matSubstituteitemPojo.getCustom7() == null) matSubstituteitemPojo.setCustom7("");
        if (matSubstituteitemPojo.getCustom8() == null) matSubstituteitemPojo.setCustom8("");
        if (matSubstituteitemPojo.getCustom9() == null) matSubstituteitemPojo.setCustom9("");
        if (matSubstituteitemPojo.getCustom10() == null) matSubstituteitemPojo.setCustom10("");
        if (matSubstituteitemPojo.getTenantid() == null) matSubstituteitemPojo.setTenantid("");
        if (matSubstituteitemPojo.getRevision() == null) matSubstituteitemPojo.setRevision(0);
        return matSubstituteitemPojo;
    }
}
