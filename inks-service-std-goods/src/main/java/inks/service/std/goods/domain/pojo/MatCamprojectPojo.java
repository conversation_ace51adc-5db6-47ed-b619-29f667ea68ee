package inks.service.std.goods.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 工程事务(MatCamproject)实体类
 *
 * <AUTHOR>
 * @since 2024-08-21 09:17:15
 */
public class MatCamprojectPojo implements Serializable {
    private static final long serialVersionUID = 365583734355060265L;
     // id
    @Excel(name = "id") 
    private String id;
     // 编码
    @Excel(name = "编码") 
    private String refno;
     // 单据类型
    @Excel(name = "单据类型") 
    private String billtype;
     // 单据标题
    @Excel(name = "单据标题") 
    private String billtitle;
     // 单据日期
    @Excel(name = "单据日期") 
    private Date billdate;
     // 分类
    @Excel(name = "分类") 
    private String jobtype;
     // 商品ID
    @Excel(name = "商品ID") 
    private String goodsid;
     // 商品编码
    @Excel(name = "商品编码") 
    private String goodsuid;
     // 商品名称
    @Excel(name = "商品名称") 
    private String goodsname;
     // 商品规格
    @Excel(name = "商品规格") 
    private String goodsspec;
     // 接受者
    @Excel(name = "接受者") 
    private String accepter;
     // 目标日期
    @Excel(name = "目标日期") 
    private Date aimdate;
     // 开始日期
    @Excel(name = "开始日期") 
    private Date startdate;
     // 结束日期
    @Excel(name = "结束日期") 
    private Date enddate;
     // 处理者
    @Excel(name = "处理者") 
    private String handler;
     // 引用UID
    @Excel(name = "引用UID") 
    private String citeuid;
     // 引用ItemID
    @Excel(name = "引用ItemID") 
    private String citeitemid;
     // 状态文本
    @Excel(name = "状态文本") 
    private String statetext;
     // 状态日期
    @Excel(name = "状态日期") 
    private Date statedate;
     // 版本信息
    @Excel(name = "版本信息") 
    private String editioninfo;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 审核员
    @Excel(name = "审核员") 
    private String assessor;
     // 审核员id
    @Excel(name = "审核员id") 
    private String assessorid;
     // 审核日期
    @Excel(name = "审核日期") 
    private Date assessdate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 编码
    public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
   // 单据类型
    public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
   // 单据标题
    public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
   // 单据日期
    public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
   // 分类
    public String getJobtype() {
        return jobtype;
    }
    
    public void setJobtype(String jobtype) {
        this.jobtype = jobtype;
    }
        
   // 商品ID
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 商品编码
    public String getGoodsuid() {
        return goodsuid;
    }
    
    public void setGoodsuid(String goodsuid) {
        this.goodsuid = goodsuid;
    }
        
   // 商品名称
    public String getGoodsname() {
        return goodsname;
    }
    
    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }
        
   // 商品规格
    public String getGoodsspec() {
        return goodsspec;
    }
    
    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }
        
   // 接受者
    public String getAccepter() {
        return accepter;
    }
    
    public void setAccepter(String accepter) {
        this.accepter = accepter;
    }
        
   // 目标日期
    public Date getAimdate() {
        return aimdate;
    }
    
    public void setAimdate(Date aimdate) {
        this.aimdate = aimdate;
    }
        
   // 开始日期
    public Date getStartdate() {
        return startdate;
    }
    
    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }
        
   // 结束日期
    public Date getEnddate() {
        return enddate;
    }
    
    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }
        
   // 处理者
    public String getHandler() {
        return handler;
    }
    
    public void setHandler(String handler) {
        this.handler = handler;
    }
        
   // 引用UID
    public String getCiteuid() {
        return citeuid;
    }
    
    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }
        
   // 引用ItemID
    public String getCiteitemid() {
        return citeitemid;
    }
    
    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }
        
   // 状态文本
    public String getStatetext() {
        return statetext;
    }
    
    public void setStatetext(String statetext) {
        this.statetext = statetext;
    }
        
   // 状态日期
    public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
   // 版本信息
    public String getEditioninfo() {
        return editioninfo;
    }
    
    public void setEditioninfo(String editioninfo) {
        this.editioninfo = editioninfo;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 审核员
    public String getAssessor() {
        return assessor;
    }
    
    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
        
   // 审核员id
    public String getAssessorid() {
        return assessorid;
    }
    
    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
        
   // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }
    
    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

