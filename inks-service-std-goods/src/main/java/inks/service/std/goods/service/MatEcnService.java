package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatEcnPojo;

/**
 * 物料变更(MatEcn)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-29 15:02:15
 */
public interface MatEcnService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatEcnPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatEcnPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matEcnPojo 实例对象
     * @return 实例对象
     */
    MatEcnPojo insert(MatEcnPojo matEcnPojo);

    /**
     * 修改数据
     *
     * @param matEcnpojo 实例对象
     * @return 实例对象
     */
    MatEcnPojo update(MatEcnPojo matEcnpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matEcnPojo 实例对象
     * @return 实例对象
     */
    MatEcnPojo approval(MatEcnPojo matEcnPojo);
}
