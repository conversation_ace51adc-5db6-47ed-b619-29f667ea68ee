package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecorderdrawPojo;

import java.util.List;

/**
 * 工艺流程排版(MatSpecorderdraw)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-16 14:44:46
 */
public interface MatSpecorderdrawService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecorderdrawPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecorderdrawPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecorderdrawPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSpecorderdrawPojo 实例对象
     * @return 实例对象
     */
    MatSpecorderdrawPojo insert(MatSpecorderdrawPojo matSpecorderdrawPojo);

    /**
     * 修改数据
     *
     * @param matSpecorderdrawpojo 实例对象
     * @return 实例对象
     */
    MatSpecorderdrawPojo update(MatSpecorderdrawPojo matSpecorderdrawpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSpecorderdrawpojo 实例对象
     * @return 实例对象
     */
    MatSpecorderdrawPojo clearNull(MatSpecorderdrawPojo matSpecorderdrawpojo);
}
