package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatBrandPojo;

/**
 * 品牌列表(MatBrand)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-22 13:32:33
 */
public interface MatBrandService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBrandPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBrandPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matBrandPojo 实例对象
     * @return 实例对象
     */
    MatBrandPojo insert(MatBrandPojo matBrandPojo);

    /**
     * 修改数据
     *
     * @param matBrandpojo 实例对象
     * @return 实例对象
     */
    MatBrandPojo update(MatBrandPojo matBrandpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
