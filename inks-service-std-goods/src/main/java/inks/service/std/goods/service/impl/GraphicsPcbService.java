package inks.service.std.goods.service.impl;

import com.alibaba.fastjson.JSONArray;
import inks.service.std.goods.domain.pojo.*;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
public class GraphicsPcbService {
    /**
     * 格式化数字：
     * - 如果是整数(如 1000.0), 显示为 "1000"
     * - 如果是小数(如 480.26), 显示为 "480.26"，保留所有小数位
     */
    private String formatDouble(double value) {
        if (value == (int) value) {
            // 如果是整数，转换为 int 再变为 String，去除 ".0"
            return String.valueOf((int) value);
        } else {
            // 变更：如果是小数，直接转换为字符串以保留所有小数位
            return String.valueOf(value);
        }
    }

    public BufferedImage getMapDrew(DrawPnlMapPojo mapPojo) {
        // --- 1. 初始化参数 ---
        double originalWidth = mapPojo.getPnlx();
        double originalHeight = mapPojo.getPnly();

        // --- 变更：动态计算样式尺寸，以适应不同大小的画布 ---
        // 以 PNL 宽度和高度中较小的一方作为计算基准
        double baseDimension = Math.min(originalWidth, originalHeight);

        // 动态计算字体大小
        int outerFontSize = (int) (baseDimension * 0.18); // 外部字体为基准的18%
        int innerFontSize = (int) (baseDimension * 0.15); // 内部字体为基准的15%

        // 动态计算线条粗细和边距
        float strokeWidth = Math.max(2.0f, outerFontSize / 20f); // 最小为2px
        int padding = outerFontSize / 2;

        // 使用动态计算的尺寸创建样式对象
        Font outerLabelFont = new Font("Arial", Font.BOLD, outerFontSize);
        Font innerLabelFont = new Font("Arial", Font.BOLD, innerFontSize);
        Stroke borderStroke = new BasicStroke(strokeWidth);

        // --- 2. 动态计算自适应边框 ---
        BufferedImage tempImg = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics2D tempG = tempImg.createGraphics();
        tempG.setFont(outerLabelFont);
        FontMetrics outerFm = tempG.getFontMetrics();

        int borderTop = outerFm.getHeight() + padding;
        int borderRight = outerFm.getHeight() + padding;
        int borderLeft = padding;
        int borderBottom = padding;
        tempG.dispose();

        int totalWidth = (int) Math.round(originalWidth + borderLeft + borderRight);
        int totalHeight = (int) Math.round(originalHeight + borderTop + borderBottom);

        // --- 3. 创建画布并设置基础属性 ---
        BufferedImage image = new BufferedImage(totalWidth, totalHeight, BufferedImage.TYPE_4BYTE_ABGR);
        Graphics2D g2d = image.createGraphics();

        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, totalWidth, totalHeight);

        // ... 后续绘图逻辑不变，会自动应用上面动态计算出的样式 ...
        // --- 4. 绘制核心内容 ---
        AffineTransform contentTransform = g2d.getTransform();
        g2d.translate(borderLeft, borderTop);

        g2d.setColor(Color.BLACK);
        g2d.setStroke(borderStroke);
        g2d.drawRect(0, 0, (int) Math.round(originalWidth), (int) Math.round(originalHeight));

        g2d.setFont(innerLabelFont);
        FontMetrics innerFm = g2d.getFontMetrics();
        for (DrawPnlMapItemPojo item : mapPojo.getItem()) {
            double w = item.getSetx(), h = item.getSety();
            double x = item.getOrgx(), y = item.getOrgy();
            int xc = item.getCpx(), yc = item.getCpy();
            double dx = item.getSpx(), dy = item.getSpy();

            String textWidth = formatDouble(w);
            String textHeight = formatDouble(h);
            int textWidthPx = innerFm.stringWidth(textWidth);
            int textHeightPx = innerFm.stringWidth(textHeight);

            for (int i = 0; i < xc; i++) {
                double currentY = y;
                for (int j = 0; j < yc; j++) {
                    g2d.setStroke(borderStroke);
                    g2d.drawRect((int) Math.round(x), (int) Math.round(currentY), (int) Math.round(w), (int) Math.round(h));

                    g2d.setStroke(new BasicStroke(1.0f));
                    g2d.drawString(textWidth,
                            (float) (x + (w - textWidthPx) / 2),
                            (float) (currentY + innerFm.getAscent() + (strokeWidth*2))
                    );

                    AffineTransform oldT = g2d.getTransform();
                    float rotX = (float) (x + innerFm.getHeight() / 2.0);
                    float rotY = (float) (currentY + h / 2.0);
                    g2d.translate(rotX, rotY);
                    g2d.rotate(Math.PI / 2);
                    g2d.drawString(textHeight, -textHeightPx / 2.0f, innerFm.getAscent() / 2.0f);
                    g2d.setTransform(oldT);

                    currentY += h + dy;
                }
                x += w + dx;
            }
        }
        g2d.setTransform(contentTransform);

        // --- 5. 绘制外部智能标注 ---
        g2d.setFont(outerLabelFont);
        g2d.setColor(Color.RED);

        String pnlWidthStr = formatDouble(originalWidth);
        int pnlWidthPx = outerFm.stringWidth(pnlWidthStr);
        g2d.drawString(pnlWidthStr,
                borderLeft + ((float) originalWidth - pnlWidthPx) / 2,
                (float) (borderTop - padding)
        );

        String pnlHeightStr = formatDouble(originalHeight);
        int pnlHeightPx = outerFm.stringWidth(pnlHeightStr);
        AffineTransform oldT = g2d.getTransform();
        float rotX = (float) (borderLeft + originalWidth + borderRight / 2.0);
        float rotY = (float) (borderTop + originalHeight / 2.0);
        g2d.translate(rotX, rotY);
        g2d.rotate(Math.PI / 2);
        g2d.drawString(pnlHeightStr, -pnlHeightPx / 2.0f, outerFm.getAscent() / 2.0f);
        g2d.setTransform(oldT);

        // --- 6. 清理资源并返回 ---
        g2d.dispose();
        return image;
    }

    /**
     * 画PCB版
     *
     * @param mapPojo
     * @return
     */
    public BufferedImage getMapDrew2(DrawPnlMapPojo mapPojo) {
        /*
         *1、绘制背景
         */
        double originalWidth = mapPojo.getPnlx();
        double originalHeight = mapPojo.getPnly();

        // 计算边框尺寸 - 为尺寸标注预留足够空间
        int borderTop = 80;    // 顶部边框（增加空间用于显示宽度标注）
        int borderBottom = 10; // 底部边框（缩小）
        int borderLeft = 10;   // 左侧边框（缩小）
        int borderRight = 80;  // 右侧边框（增加空间用于显示高度标注）

        // 计算包含边框的总尺寸
        int totalWidth = (int)(originalWidth + borderLeft + borderRight);
        int totalHeight = (int)(originalHeight + borderTop + borderBottom);

        //创建包含边框的图片对象
        BufferedImage image = new BufferedImage(totalWidth, totalHeight, BufferedImage.TYPE_4BYTE_ABGR);
        //基于图片对象打开绘图
        Graphics2D graphics = image.createGraphics();

        // 设置抗锯齿
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 填充整个画布为白色背景
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, totalWidth, totalHeight);

        // 保存原始变换，用于后续偏移绘制
        AffineTransform originalTransform = graphics.getTransform();

        // 设置偏移量，使PCB内容绘制在边框内部
        graphics.translate(borderLeft, borderTop);
        //绘图逻辑 START （基于业务逻辑进行绘图处理）……
        // 移除PCB外围边框线，直接绘制内容

        // 去除黑白格子背景，改为纯白色背景
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, (int)originalWidth, (int)originalHeight);

        // 设置线条宽度为3像素，使线条更粗更清晰
        graphics.setStroke(new BasicStroke(3.0f));

        // 绘制PCB外层边框（372x1000的框）
        graphics.setColor(Color.BLACK);
        graphics.drawRect(0, 0, (int)originalWidth, (int)originalHeight);

        /*
         * 2、绘制内容
         */

        List<DrawPnlMapItemPojo> itemList = mapPojo.getItem();
        for (DrawPnlMapItemPojo itemPojo : itemList) {
            double w = itemPojo.getSetx(), h = itemPojo.getSety();
            double x = itemPojo.getOrgx(), y = itemPojo.getOrgy();
            int xc = itemPojo.getCpx(), yc = itemPojo.getCpy();
            double dx = itemPojo.getSpx(), dy = itemPojo.getSpy();
            // 格式化数字：如果是整数显示整数，否则显示一位小数
            String text1 = (w == (int)w) ? String.valueOf((int)w) : String.format("%.1f", w);
            String text2 = (h == (int)h) ? String.valueOf((int)h) : String.format("%.1f", h);
            String text3 = itemPojo.getLabel();
            for (int i = 0; i < xc; i++) {
                // graphics = image.createGraphics();
                double ytemp = y;
                for (int j = 0; j < yc; j++) {
                    graphics.setColor(Color.white);
                    graphics.fillRect((int)x, (int)ytemp, (int)w, (int)h);//画线框
                    graphics.setColor(Color.black);
                    graphics.drawRect((int)x, (int)ytemp, (int)w, (int)h);//画线框

                    // 增大字体到70号，使其在A4纸上打印更清晰
                    Font font = new Font("Arial", Font.BOLD, 70);
                    graphics.setFont(font);

                    FontMetrics fm = graphics.getFontMetrics();

                    int width1 = fm.stringWidth(text1);
                    int height1 = fm.getHeight();
                    // 注意每次颜色都要重新赋值，否则默认延用上一个颜色
                    graphics.setColor(Color.black);
                    graphics.drawString(text1, (float)(x + (w - width1) / 2.0), (float)(ytemp + fm.getAscent()));
                    // graphics.drawRect(10 + (200 - width1) / 2, 10, width1, 41);//画线框


                    int width3 = fm.stringWidth(text3);
                    int height3 = fm.getHeight();
                    graphics.setColor(Color.black);
                    graphics.drawString(text3, (float)(w + x + 5 - width3 - 20), (float)(h + ytemp - 5));

                    int width2 = fm.stringWidth(text1);
                    int height2 = fm.getHeight();
                    graphics.rotate(Math.PI / 2, (float)(x + 5), (float)((h - fm.getAscent()) / 2 + (ytemp - y) + y));
                    graphics.setColor(Color.black);
                    graphics.drawString(text2, (float)(x + 5), (float)((h - fm.getAscent()) / 2 + (ytemp - y) + y));
                    //System.out.println("height2-" + fm.getAscent());

                    graphics.rotate(-(Math.PI / 2), (float)(x + 5), (float)((h - fm.getAscent()) / 2 + (ytemp - y) + y));
                    ytemp = ytemp + h + dy;
                }
                x = x + w + dx;
            }
        }
        // 恢复原始变换，准备在边框区域绘制尺寸标注
        graphics.setTransform(originalTransform);

        // 在边框区域显示宽度和高度标注
        graphics.setColor(Color.RED);
        graphics.setFont(new Font("Arial", Font.BOLD, 60));
        FontMetrics fm = graphics.getFontMetrics();

        // 宽度：显示在顶部边框中央，确保不覆盖线条
        // 格式化数字：如果是整数显示整数，否则显示一位小数
        String pnlWidthStr = (originalWidth == (int)originalWidth) ?
            String.valueOf((int)originalWidth) : String.format("%.1f", originalWidth);
        int pnlWidthPx = fm.stringWidth(pnlWidthStr);
        int widthTextX = (int)(borderLeft + originalWidth / 2 - pnlWidthPx / 2);
        int widthTextY = borderTop / 2 + fm.getAscent() / 2; // 在顶部边框中央显示
        graphics.drawString(pnlWidthStr, widthTextX, widthTextY);

        // 高度：显示在右侧边框中央（垂直文本），确保不覆盖线条
        // 格式化数字：如果是整数显示整数，否则显示一位小数
        String pnlHeightStr = (originalHeight == (int)originalHeight) ?
            String.valueOf((int)originalHeight) : String.format("%.1f", originalHeight);
        int pnlHeightPx = fm.stringWidth(pnlHeightStr);
        // 调整位置：让文字位于右侧边框中央
        int heightTextX = (int)(borderLeft + originalWidth + borderRight / 2);
        int heightTextY = (int)(borderTop + originalHeight / 2 - pnlHeightPx / 2);

        // 保存当前变换
        AffineTransform currentTransform = graphics.getTransform();
        // 旋转90度绘制垂直文本（和280一样的方向）
        graphics.rotate(Math.PI / 2, heightTextX, heightTextY);
        graphics.drawString(pnlHeightStr, heightTextX, heightTextY + pnlHeightPx / 2);
        // 恢复变换
        graphics.setTransform(currentTransform);

        //处理绘图
        graphics.dispose();
        //将绘制好的图片写入到图片
        //ImageIO.write(image, "png", new File("D:/temp/abc.png"));
        return image;
    }

    public BufferedImage getVcutDoubleMap(DrawVcutPojo mapPojo) {

        String strline = "{\n" +
                "    \"name\": \"测试\",\n" +
                "    \"sizex\": 500,\n" +
                "    \"sizey\": 500,\n" +
                "    \"line\": [\n" +
                "        {\n" +
                "            \"orgx\": 88,\n" +
                "            \"orgy\": 171,\n" +
                "            \"endx\": 157,\n" +
                "            \"endy\": 171\n" +
                "        },      \n" +
                "        {\n" +
                "            \"orgx\": 157,\n" +
                "            \"orgy\": 171,\n" +
                "            \"endx\": 181,\n" +
                "            \"endy\": 235\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 181,\n" +
                "            \"orgy\": 235,\n" +
                "            \"endx\": 206,\n" +
                "            \"endy\": 171\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 206,\n" +
                "            \"orgy\": 171,\n" +
                "            \"endx\": 298,\n" +
                "            \"endy\": 171\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 88,\n" +
                "            \"orgy\": 313,\n" +
                "            \"endx\": 145,\n" +
                "            \"endy\": 313\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 195,\n" +
                "            \"orgy\": 313,\n" +
                "            \"endx\": 300,\n" +
                "            \"endy\": 313\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 145,\n" +
                "            \"orgy\": 313,\n" +
                "            \"endx\": 170,\n" +
                "            \"endy\": 260\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 195,\n" +
                "            \"orgy\": 313,\n" +
                "            \"endx\": 170,\n" +
                "            \"endy\": 260\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 170,\n" +
                "            \"orgy\": 290,\n" +
                "            \"endx\": 170,\n" +
                "            \"endy\": 370\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 185,\n" +
                "            \"orgy\": 260,\n" +
                "            \"endx\": 185,\n" +
                "            \"endy\": 370\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 165,\n" +
                "            \"orgy\": 350,\n" +
                "            \"endx\": 125,\n" +
                "            \"endy\": 350\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 165,\n" +
                "            \"orgy\": 350,\n" +
                "            \"endx\": 151,\n" +
                "            \"endy\": 360\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 165,\n" +
                "            \"orgy\": 350,\n" +
                "            \"endx\": 151,\n" +
                "            \"endy\": 340\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 185,\n" +
                "            \"orgy\": 350,\n" +
                "            \"endx\": 225,\n" +
                "            \"endy\": 350\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 185,\n" +
                "            \"orgy\": 350,\n" +
                "            \"endx\": 199,\n" +
                "            \"endy\": 340\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 185,\n" +
                "            \"orgy\": 350,\n" +
                "            \"endx\": 199,\n" +
                "            \"endy\": 360\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 353,\n" +
                "            \"orgy\": 111,\n" +
                "            \"endx\": 353,\n" +
                "            \"endy\": 257\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 353,\n" +
                "            \"orgy\": 257,\n" +
                "            \"endx\": 372,\n" +
                "            \"endy\": 301\n" +
                "        }        \n" +
                "        {\n" +
                "            \"orgx\": 372,\n" +
                "            \"orgy\": 301,\n" +
                "            \"endx\": 402,\n" +
                "            \"endy\": 301\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 402,\n" +
                "            \"orgy\": 301,\n" +
                "            \"endx\": 422,\n" +
                "            \"endy\": 257\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 422,\n" +
                "            \"orgy\": 257,\n" +
                "            \"endx\": 422,\n" +
                "            \"endy\": 111\n" +
                "        }\n" +
                "                \n" +
                "        {\n" +
                "            \"orgx\": 130,\n" +
                "            \"orgy\": 97,\n" +
                "            \"endx\": 152,\n" +
                "            \"endy\": 162\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 231,\n" +
                "            \"orgy\": 94,\n" +
                "            \"endx\": 207,\n" +
                "            \"endy\": 162\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 16,\n" +
                "            \"orgy\": 234,\n" +
                "            \"endx\": 153,\n" +
                "            \"endy\": 234\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 16,\n" +
                "            \"orgy\": 260,\n" +
                "            \"endx\": 153,\n" +
                "            \"endy\": 260\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 28,\n" +
                "            \"orgy\": 316,\n" +
                "            \"endx\": 28,\n" +
                "            \"endy\": 260\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 28,\n" +
                "            \"orgy\": 260,\n" +
                "            \"endx\": 18,\n" +
                "            \"endy\": 274\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 28,\n" +
                "            \"orgy\": 260,\n" +
                "            \"endx\": 38,\n" +
                "            \"endy\": 274\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 468,\n" +
                "            \"orgy\": 167,\n" +
                "            \"endx\": 429,\n" +
                "            \"endy\": 242\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 28,\n" +
                "            \"orgy\": 183,\n" +
                "            \"endx\": 28,\n" +
                "            \"endy\": 232\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 38,\n" +
                "            \"orgy\": 218,\n" +
                "            \"endx\": 28,\n" +
                "            \"endy\": 232\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 18,\n" +
                "            \"orgy\": 218,\n" +
                "            \"endx\": 28,\n" +
                "            \"endy\": 232\n" +
                "        }\n" +
                "           {\n" +
                "            \"orgx\": 275,\n" +
                "            \"orgy\": 173,\n" +
                "            \"endx\": 275,\n" +
                "            \"endy\": 225\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 275,\n" +
                "            \"orgy\": 173,\n" +
                "            \"endx\": 265,\n" +
                "            \"endy\": 187\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 275,\n" +
                "            \"orgy\": 173,\n" +
                "            \"endx\": 285,\n" +
                "            \"endy\": 187\n" +
                "        }\n" +
                "          {\n" +
                "            \"orgx\": 275,\n" +
                "            \"orgy\": 262,\n" +
                "            \"endx\": 275,\n" +
                "            \"endy\": 312\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 265,\n" +
                "            \"orgy\": 298,\n" +
                "            \"endx\": 275,\n" +
                "            \"endy\": 312\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 285,\n" +
                "            \"orgy\": 298,\n" +
                "            \"endx\": 275,\n" +
                "            \"endy\": 312\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 375,\n" +
                "            \"orgy\": 320,\n" +
                "            \"endx\": 375,\n" +
                "            \"endy\": 365\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 405,\n" +
                "            \"orgy\": 320,\n" +
                "            \"endx\": 405,\n" +
                "            \"endy\": 365\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 322,\n" +
                "            \"orgy\": 347,\n" +
                "            \"endx\": 374,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "       {\n" +
                "            \"orgx\": 360,\n" +
                "            \"orgy\": 337,\n" +
                "            \"endx\": 374,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 360,\n" +
                "            \"orgy\": 357,\n" +
                "            \"endx\": 374,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 458,\n" +
                "            \"orgy\": 347,\n" +
                "            \"endx\": 406,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 420,\n" +
                "            \"orgy\": 337,\n" +
                "            \"endx\": 406,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 420,\n" +
                "            \"orgy\": 357,\n" +
                "            \"endx\": 406,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "        \n" +
                "    ]\n" +
                "}";

        DrawVcutMapPojo drawVcutMapPojo = JSONArray.parseObject(strline, DrawVcutMapPojo.class);



        /*
         *1、绘制背景
         */
        int width = drawVcutMapPojo.getSizex();
        int height = drawVcutMapPojo.getSizey();
        //创建图片对象
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_4BYTE_ABGR);
        //基于图片对象打开绘图
        Graphics2D graphics = image.createGraphics();
        //绘图逻辑 START （基于业务逻辑进行绘图处理）……
        graphics.setColor(Color.white);
        graphics.fillRect(0, 0, width, height);//画线框


        /*
         * 2、绘制内容lINE
         */
        List<DrawVcutMapLinePojo> lstline = drawVcutMapPojo.getLine();
        // 设置线的宽度
        float lineWidth = 4F;
        graphics.setStroke(new BasicStroke(lineWidth));
        graphics.setColor(Color.black);
        for (DrawVcutMapLinePojo line : lstline) {
            graphics.drawLine(line.getOrgx(), line.getOrgy(), line.getEndx(), line.getEndy());
        }
        /*
         * 3、绘制内容TEXT
         */
        List<DrawVcutMapTextPojo> lsttext = new ArrayList<>();
        if (mapPojo.getAngv() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getAngv());
            vcuttext.setOrgx(160);
            vcuttext.setOrgy(82);
            lsttext.add(vcuttext);
        }
        if (mapPojo.getRes() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getRes());
            vcuttext.setOrgx(45);
            vcuttext.setOrgy(210);
            lsttext.add(vcuttext);
        }
        if (mapPojo.getWarp() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getWarp());
            vcuttext.setOrgx(209);
            vcuttext.setOrgy(340);
            lsttext.add(vcuttext);
        }
        if (mapPojo.getAngi() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getAngi());
            vcuttext.setOrgx(434);
            vcuttext.setOrgy(150);
            lsttext.add(vcuttext);
        }
        if (mapPojo.getLon() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getLon());
            vcuttext.setOrgx(418);
            vcuttext.setOrgy(334);
            lsttext.add(vcuttext);
        }

        if (lsttext != null) {
            Font font = new Font("Arial", Font.PLAIN, 25);
            graphics.setFont(font);
            for (DrawVcutMapTextPojo textPojo : lsttext) {
                graphics.drawString(textPojo.getText(), textPojo.getOrgx(), textPojo.getOrgy());
            }
        }
        //处理绘图
        graphics.dispose();
        //将绘制好的图片写入到图片
        //ImageIO.write(image, "png", new File("D:/temp/abc.png"));
        return image;
    }

    public BufferedImage getVcutSingleMap(DrawVcutPojo mapPojo) throws IOException {

        String strline = "{\n" +
                "    \"name\": \"测试\",\n" +
                "    \"sizex\": 500,\n" +
                "    \"sizey\": 500,\n" +
                "    \"line\": [\n" +
                "        {\n" +
                "            \"orgx\": 88,\n" +
                "            \"orgy\": 171,\n" +
                "            \"endx\": 157,\n" +
                "            \"endy\": 171\n" +
                "        },      \n" +
                "        {\n" +
                "            \"orgx\": 157,\n" +
                "            \"orgy\": 171,\n" +
                "            \"endx\": 181,\n" +
                "            \"endy\": 235\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 181,\n" +
                "            \"orgy\": 235,\n" +
                "            \"endx\": 206,\n" +
                "            \"endy\": 171\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 206,\n" +
                "            \"orgy\": 171,\n" +
                "            \"endx\": 298,\n" +
                "            \"endy\": 171\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 88,\n" +
                "            \"orgy\": 313,\n" +
                "            \"endx\": 298,\n" +
                "            \"endy\": 313\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 353,\n" +
                "            \"orgy\": 111,\n" +
                "            \"endx\": 353,\n" +
                "            \"endy\": 257\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 353,\n" +
                "            \"orgy\": 257,\n" +
                "            \"endx\": 372,\n" +
                "            \"endy\": 301\n" +
                "        }        \n" +
                "        {\n" +
                "            \"orgx\": 372,\n" +
                "            \"orgy\": 301,\n" +
                "            \"endx\": 402,\n" +
                "            \"endy\": 301\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 402,\n" +
                "            \"orgy\": 301,\n" +
                "            \"endx\": 422,\n" +
                "            \"endy\": 257\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 422,\n" +
                "            \"orgy\": 257,\n" +
                "            \"endx\": 422,\n" +
                "            \"endy\": 111\n" +
                "        }\n" +
                "                \n" +
                "        {\n" +
                "            \"orgx\": 130,\n" +
                "            \"orgy\": 97,\n" +
                "            \"endx\": 152,\n" +
                "            \"endy\": 162\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 231,\n" +
                "            \"orgy\": 94,\n" +
                "            \"endx\": 207,\n" +
                "            \"endy\": 162\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 16,\n" +
                "            \"orgy\": 234,\n" +
                "            \"endx\": 153,\n" +
                "            \"endy\": 234\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 22,\n" +
                "            \"orgy\": 313,\n" +
                "            \"endx\": 64,\n" +
                "            \"endy\": 313\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 468,\n" +
                "            \"orgy\": 167,\n" +
                "            \"endx\": 429,\n" +
                "            \"endy\": 242\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 28,\n" +
                "            \"orgy\": 183,\n" +
                "            \"endx\": 28,\n" +
                "            \"endy\": 232\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 38,\n" +
                "            \"orgy\": 218,\n" +
                "            \"endx\": 28,\n" +
                "            \"endy\": 232\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 18,\n" +
                "            \"orgy\": 218,\n" +
                "            \"endx\": 28,\n" +
                "            \"endy\": 232\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 31,\n" +
                "            \"orgy\": 316,\n" +
                "            \"endx\": 31,\n" +
                "            \"endy\": 366\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 31,\n" +
                "            \"orgy\": 316,\n" +
                "            \"endx\": 21,\n" +
                "            \"endy\": 330\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 31,\n" +
                "            \"orgy\": 316,\n" +
                "            \"endx\": 41,\n" +
                "            \"endy\": 330\n" +
                "        }\n" +
                "           {\n" +
                "            \"orgx\": 275,\n" +
                "            \"orgy\": 173,\n" +
                "            \"endx\": 275,\n" +
                "            \"endy\": 225\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 275,\n" +
                "            \"orgy\": 173,\n" +
                "            \"endx\": 265,\n" +
                "            \"endy\": 187\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 275,\n" +
                "            \"orgy\": 173,\n" +
                "            \"endx\": 285,\n" +
                "            \"endy\": 187\n" +
                "        }\n" +
                "          {\n" +
                "            \"orgx\": 275,\n" +
                "            \"orgy\": 262,\n" +
                "            \"endx\": 275,\n" +
                "            \"endy\": 312\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 265,\n" +
                "            \"orgy\": 298,\n" +
                "            \"endx\": 275,\n" +
                "            \"endy\": 312\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 285,\n" +
                "            \"orgy\": 298,\n" +
                "            \"endx\": 275,\n" +
                "            \"endy\": 312\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 375,\n" +
                "            \"orgy\": 320,\n" +
                "            \"endx\": 375,\n" +
                "            \"endy\": 365\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 405,\n" +
                "            \"orgy\": 320,\n" +
                "            \"endx\": 405,\n" +
                "            \"endy\": 365\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 322,\n" +
                "            \"orgy\": 347,\n" +
                "            \"endx\": 374,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "       {\n" +
                "            \"orgx\": 360,\n" +
                "            \"orgy\": 337,\n" +
                "            \"endx\": 374,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 360,\n" +
                "            \"orgy\": 357,\n" +
                "            \"endx\": 374,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 458,\n" +
                "            \"orgy\": 347,\n" +
                "            \"endx\": 406,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "        {\n" +
                "            \"orgx\": 420,\n" +
                "            \"orgy\": 337,\n" +
                "            \"endx\": 406,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "                {\n" +
                "            \"orgx\": 420,\n" +
                "            \"orgy\": 357,\n" +
                "            \"endx\": 406,\n" +
                "            \"endy\": 347\n" +
                "        }\n" +
                "        \n" +
                "    ],\n" +
                "        \"text\": [\n" +
                "        {\n" +
                "            \"orgx\": 160,\n" +
                "            \"orgy\": 82,\n" +
                "            \"text\": \"30\"\n" +
                "        }\n" +
                "         {\n" +
                "            \"orgx\": 45,\n" +
                "            \"orgy\": 210,\n" +
                "            \"text\": \"0.2\"\n" +
                "        }\n" +
                "                 {\n" +
                "            \"orgx\": 434,\n" +
                "            \"orgy\": 150,\n" +
                "            \"text\": \"22\"\n" +
                "        }\n" +
                "                         {\n" +
                "            \"orgx\": 418,\n" +
                "            \"orgy\": 334,\n" +
                "            \"text\": \"33\"\n" +
                "        } \n" +
                "         {\n" +
                "            \"orgx\": 209,\n" +
                "            \"orgy\": 340,\n" +
                "            \"text\": \"11\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        DrawVcutMapPojo drawVcutMapPojo = JSONArray.parseObject(strline, DrawVcutMapPojo.class);




        /*
         *1、绘制背景
         */
        int width = drawVcutMapPojo.getSizex();
        int height = drawVcutMapPojo.getSizey();
        //创建图片对象
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_4BYTE_ABGR);
        //基于图片对象打开绘图
        Graphics2D graphics = image.createGraphics();
        //绘图逻辑 START （基于业务逻辑进行绘图处理）……
        graphics.setColor(Color.white);
        graphics.fillRect(0, 0, width, height);//画线框

        /*
         * 2、绘制内容lINE
         */
        List<DrawVcutMapLinePojo> lstline = drawVcutMapPojo.getLine();
        // 设置线的宽度
        float lineWidth = 4F;
        graphics.setStroke(new BasicStroke(lineWidth));
        graphics.setColor(Color.black);
        for (DrawVcutMapLinePojo line : lstline) {
            graphics.drawLine(line.getOrgx(), line.getOrgy(), line.getEndx(), line.getEndy());
        }
        /*
         * 3、绘制内容TEXT
         */
        List<DrawVcutMapTextPojo> lsttext = new ArrayList<>();
        if (mapPojo.getAngv() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getAngv());
            vcuttext.setOrgx(160);
            vcuttext.setOrgy(82);
            lsttext.add(vcuttext);
        }
        if (mapPojo.getRes() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getRes());
            vcuttext.setOrgx(45);
            vcuttext.setOrgy(210);
            lsttext.add(vcuttext);
        }
        if (mapPojo.getAngi() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getAngi());
            vcuttext.setOrgx(434);
            vcuttext.setOrgy(150);
            lsttext.add(vcuttext);
        }
        if (mapPojo.getLon() != null) {
            DrawVcutMapTextPojo vcuttext = new DrawVcutMapTextPojo();
            vcuttext.setText(mapPojo.getLon());
            vcuttext.setOrgx(418);
            vcuttext.setOrgy(334);
            lsttext.add(vcuttext);
        }
        if (lsttext != null) {
            Font font = new Font("Arial", Font.PLAIN, 25);
            graphics.setFont(font);
            for (DrawVcutMapTextPojo textPojo : lsttext) {
                graphics.drawString(textPojo.getText(), textPojo.getOrgx(), textPojo.getOrgy());
            }
        }
        //处理绘图
        graphics.dispose();
        //将绘制好的图片写入到图片
        //ImageIO.write(image, "png", new File("D:/temp/abc.png"));
        return image;
    }

    //    public BufferedImage getVcutMapDrew(DrawVcutMapPojo mapPojo) throws IOException {
//        /*
//         *1、绘制背景
//         */
//        int width = mapPojo.getSizex();
//        int height = mapPojo.getSizey();
//        //创建图片对象
//        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_4BYTE_ABGR);
//        //基于图片对象打开绘图
//        Graphics2D graphics = image.createGraphics();
//        //绘图逻辑 START （基于业务逻辑进行绘图处理）……
//        graphics.setColor(Color.white);
//        graphics.fillRect(0, 0, width, height);//画线框
//
////        int wh = width > height ? width : height;
////        boolean ok1 = false;
////        for (int i = 0; i < wh; i = i + 6) {
////            boolean ok = ok1;
////            for (int j = 0; j < wh; j = j + 6) {
////                if (ok) {
////                    graphics.setColor(Color.LIGHT_GRAY);
////                } else {
////                    graphics.setColor(Color.white);
////                }
////                ok = !ok;
////                graphics.fillRect(i, j, 6, 6);//画线框
////            }
////            ok1 = !ok1;
////        }
//        /*
//         * 2、绘制内容lINE
//         */
//        List<DrawVcutMapLinePojo> lstline = mapPojo.getLine();
//        // 设置线的宽度
//        float lineWidth = 4F;
//        graphics.setStroke(new BasicStroke(lineWidth));
//        graphics.setColor(Color.black);
//        for (DrawVcutMapLinePojo line : lstline) {
//            graphics.drawLine(line.getOrgx(), line.getOrgy(), line.getEndx(), line.getEndy());
//        }
//        /*
//         * 3、绘制内容TEXT
//         */
//        List<DrawVcutMapTextPojo> lsttext = mapPojo.getText();
//        Font font = new Font("Arial", Font.PLAIN, 25);
//        graphics.setFont(font);
//        for (DrawVcutMapTextPojo textPojo : lsttext) {
//            graphics.drawString(textPojo.getText(), textPojo.getOrgx(), textPojo.getOrgy());
//        }
//        //处理绘图
//        graphics.dispose();
//        //将绘制好的图片写入到图片
//        //ImageIO.write(image, "png", new File("D:/temp/abc.png"));
//        return image;
//    }


}
