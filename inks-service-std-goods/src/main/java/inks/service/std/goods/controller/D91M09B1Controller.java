package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSON;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.export.SpecDrlVO;
import inks.service.std.goods.domain.pojo.DrawPnlMapPojo;
import inks.service.std.goods.domain.pojo.DrawVcutPojo;
import inks.service.std.goods.domain.pojo.MatSpecpcbPojo;
import inks.service.std.goods.service.impl.DRLFileProcessorService;
import inks.service.std.goods.service.impl.GraphicsPcbService;
import inks.service.std.goods.service.MatSpecpcbService;
import inks.service.std.goods.utils.PrintColor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Base64;
import java.util.List;

/**
 * Pcb工艺(Mat_SpecPcb)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-23 08:25:17
 */
@RestController
@RequestMapping("D91M09B1")
@Api(tags = "D91M09B1:PCB规格书")
public class D91M09B1Controller extends MatSpecpcbController {
    @Resource
    private GraphicsPcbService graphicsPcbService;
    @Resource
    private MatSpecpcbService matSpecpcbService;
    @Resource
    private DRLFileProcessorService drlFileProcessorService;
    @Resource
    private TokenService tokenService;

    // image转Base64
    public static String bufferedImageToBase64(BufferedImage bufferedImage) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream(); // IO 流
        try {
            ImageIO.write(bufferedImage, "png", baos); // 写入流中，格式为 PNG
        } catch (IOException e) {
            e.printStackTrace();
            return null; // 异常时返回 null
        }
        byte[] bytes = baos.toByteArray(); // 转换成字节数组

        // 使用 Base64 编码
        String pngBase64 = Base64.getEncoder().encodeToString(bytes);
        pngBase64 = pngBase64.replaceAll("\n", "").replaceAll("\r", ""); // 删除 \r\n

        // 拼接 Base64 图片头部信息
        String result = "data:image/png;base64," + pngBase64;
        System.out.println("值为：" + result);
        return result;
    }

    @ApiOperation(value = "上传DRL文件并处理 返回symbol，drill，pnltotal", notes = "上传DRL文件并返回处理后的钻孔实体列表", produces = "application/json")
    @RequestMapping(value = "/processDRLFile", method = RequestMethod.POST)
    public R<List<SpecDrlVO>> processDRLFile(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return R.fail("上传的文件为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".drl") && !fileName.endsWith(".d") && !fileName.endsWith(".txt"))) {
                return R.fail("不支持的文件类型，请上传.drl、.d或.txt文件");
            }

            // 调用DRLFileProcessor的处理方法
            drlFileProcessorService.processDRLFile(file);
            List<SpecDrlVO> drlList = drlFileProcessorService.getDrlList();

            if (drlList.isEmpty()) {
                return R.fail("文件处理完成，但未找到有效的钻孔数据");
            }
            for (SpecDrlVO specDrlVO : drlList) {
                PrintColor.zi(specDrlVO.toString());
            }
            return R.ok(drlList);
        } catch (Exception e) {
            return R.fail("处理文件时发生错误: " + e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "通json获取拼板的image", notes = "通json获取拼板的image", produces = "application/json")
    @RequestMapping(value = "/getPnlImage", method = RequestMethod.POST)
    public void getPnlImage(@RequestBody String json, HttpServletResponse response) throws IOException {

        DrawPnlMapPojo mapPojo = JSONArray.parseObject(json, DrawPnlMapPojo.class);
        BufferedImage image = this.graphicsPcbService.getMapDrew(mapPojo);
        response.setContentType("image/png");
        OutputStream os = response.getOutputStream();
        ImageIO.write(image, "png", os);
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "通json获取拼板的image", notes = "通json获取拼板的image", produces = "application/json")
    @RequestMapping(value = "/getVcutImage", method = RequestMethod.POST)
    public void getVcutImage(@RequestBody String json, HttpServletResponse response) throws IOException {
        // jsonString转json对象
        //  JSONObject mapJson = JSON.parseObject(json);
        DrawVcutPojo mapPojo = JSONArray.parseObject(json, DrawVcutPojo.class);

        if ("double".equals(mapPojo.getVtype())) {
            BufferedImage image = this.graphicsPcbService.getVcutDoubleMap(mapPojo);
            response.setContentType("image/png");
            OutputStream os = response.getOutputStream();
            ImageIO.write(image, "png", os);
        } else if ("single".equals(mapPojo.getVtype())) {
            BufferedImage image = this.graphicsPcbService.getVcutSingleMap(mapPojo);
            response.setContentType("image/png");
            OutputStream os = response.getOutputStream();
            ImageIO.write(image, "png", os);
        }

    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "通json获取拼板的image", notes = "通json获取拼板的image", produces = "application/json")
    @RequestMapping(value = "/getPnlImageString", method = RequestMethod.POST)
    public R<String> getPnlImageString(@RequestBody String json) {
        try {
            DrawPnlMapPojo mapPojo = JSONArray.parseObject(json, DrawPnlMapPojo.class);
            BufferedImage image = this.graphicsPcbService.getMapDrew(mapPojo);
            return R.ok(bufferedImageToBase64(image));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "通json获取拼板的image", notes = "通json获取拼板的image", produces = "application/json")
    @RequestMapping(value = "/getVcutImageString", method = RequestMethod.POST)
    public R<String> getVcutImageString(@RequestBody String json) {
        try {
            // 获得用户数据
            // jsonString转json对象
            //  JSONObject mapJson = JSON.parseObject(json);
            DrawVcutPojo mapPojo = JSONArray.parseObject(json, DrawVcutPojo.class);

            if ("double".equals(mapPojo.getVtype())) {
                BufferedImage image = this.graphicsPcbService.getVcutDoubleMap(mapPojo);
                return R.ok(bufferedImageToBase64(image));

            } else if ("single".equals(mapPojo.getVtype())) {
                BufferedImage image = this.graphicsPcbService.getVcutSingleMap(mapPojo);
                return R.ok(bufferedImageToBase64(image));
            } else {
                return R.fail("类型错误");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "XML转动态对象接口", notes = "解析xmld文件并返回MatSpecpcbPojo对象", produces = "application/json")
    @RequestMapping(value = "/getEntityByCsXml", method = RequestMethod.POST)
    public R<MatSpecpcbPojo> getEntityByCsXml(@RequestParam("file") MultipartFile file) {
        LoginUser loginUser = tokenService.getLoginUser();
        try {
            if (file.isEmpty()) {
                return R.fail("上传的文件为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".xmld")) {
                return R.fail("不支持的文件类型，请上传.xmld文件");
            }

            // 读取XML内容
            String xmlContent = new String(file.getBytes(), "UTF-8");
            
            // 调用Service层解析XML并转换为MatSpecpcbPojo
            MatSpecpcbPojo result = matSpecpcbService.parseXmlToMatSpecpcbPojo(xmlContent,loginUser);
            
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("处理XML文件时发生错误: " + e.getMessage());
        }
    }
}
