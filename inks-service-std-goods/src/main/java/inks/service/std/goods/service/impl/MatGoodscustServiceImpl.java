package inks.service.std.goods.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.goods.domain.pojo.MatAttributePojo;
import inks.service.std.goods.domain.pojo.MatGoodscustPojo;
import inks.service.std.goods.domain.MatGoodscustEntity;
import inks.service.std.goods.mapper.MatGoodscustMapper;
import inks.service.std.goods.service.MatGoodscustService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 货品自定义(MatGoodscust)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-15 16:56:59
 */
@Service("matGoodscustService")
public class MatGoodscustServiceImpl implements MatGoodscustService {
    @Resource
    private MatGoodscustMapper matGoodscustMapper;

    @Override
    public MatGoodscustPojo getEntity(String key, String tid) {
        return this.matGoodscustMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<MatGoodscustPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGoodscustPojo> lst = matGoodscustMapper.getPageList(queryParam);
            PageInfo<MatGoodscustPojo> pageInfo = new PageInfo<MatGoodscustPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public MatGoodscustPojo insert(MatGoodscustPojo matGoodscustPojo) {
        //初始化NULL字段
        cleanNull(matGoodscustPojo);
        MatGoodscustEntity matGoodscustEntity = new MatGoodscustEntity();
        BeanUtils.copyProperties(matGoodscustPojo,matGoodscustEntity);
          //生成雪花id
          matGoodscustEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matGoodscustEntity.setRevision(1);  //乐观锁
          this.matGoodscustMapper.insert(matGoodscustEntity);
        return this.getEntity(matGoodscustEntity.getId(),matGoodscustEntity.getTenantid());
    }


    @Override
    public MatGoodscustPojo update(MatGoodscustPojo matGoodscustPojo) {
        MatGoodscustEntity matGoodscustEntity = new MatGoodscustEntity();
        BeanUtils.copyProperties(matGoodscustPojo,matGoodscustEntity);
        this.matGoodscustMapper.update(matGoodscustEntity);
        return this.getEntity(matGoodscustEntity.getId(),matGoodscustEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.matGoodscustMapper.delete(key,tid) ;
    }


    private static void cleanNull(MatGoodscustPojo matGoodscustPojo) {
        if(matGoodscustPojo.getCustgroupid()==null) matGoodscustPojo.setCustgroupid("");
        if(matGoodscustPojo.getCustkey()==null) matGoodscustPojo.setCustkey("");
        if(matGoodscustPojo.getCustname()==null) matGoodscustPojo.setCustname("");
        if(matGoodscustPojo.getValuetype()==null) matGoodscustPojo.setValuetype("");
        if(matGoodscustPojo.getDefvalue()==null) matGoodscustPojo.setDefvalue("");
        if(matGoodscustPojo.getValuejson()==null) matGoodscustPojo.setValuejson("");
        if(matGoodscustPojo.getShowmark()==null) matGoodscustPojo.setShowmark(0);
        if(matGoodscustPojo.getEnabledmark()==null) matGoodscustPojo.setEnabledmark(0);
        if(matGoodscustPojo.getRemark()==null) matGoodscustPojo.setRemark("");
        if(matGoodscustPojo.getRownum()==null) matGoodscustPojo.setRownum(0);
        if(matGoodscustPojo.getCreateby()==null) matGoodscustPojo.setCreateby("");
        if(matGoodscustPojo.getCreatebyid()==null) matGoodscustPojo.setCreatebyid("");
        if(matGoodscustPojo.getCreatedate()==null) matGoodscustPojo.setCreatedate(new Date());
        if(matGoodscustPojo.getLister()==null) matGoodscustPojo.setLister("");
        if(matGoodscustPojo.getListerid()==null) matGoodscustPojo.setListerid("");
        if(matGoodscustPojo.getModifydate()==null) matGoodscustPojo.setModifydate(new Date());
        if(matGoodscustPojo.getCustom1()==null) matGoodscustPojo.setCustom1("");
        if(matGoodscustPojo.getCustom2()==null) matGoodscustPojo.setCustom2("");
        if(matGoodscustPojo.getCustom3()==null) matGoodscustPojo.setCustom3("");
        if(matGoodscustPojo.getCustom4()==null) matGoodscustPojo.setCustom4("");
        if(matGoodscustPojo.getCustom5()==null) matGoodscustPojo.setCustom5("");
        if(matGoodscustPojo.getCustom6()==null) matGoodscustPojo.setCustom6("");
        if(matGoodscustPojo.getCustom7()==null) matGoodscustPojo.setCustom7("");
        if(matGoodscustPojo.getCustom8()==null) matGoodscustPojo.setCustom8("");
        if(matGoodscustPojo.getCustom9()==null) matGoodscustPojo.setCustom9("");
        if(matGoodscustPojo.getCustom10()==null) matGoodscustPojo.setCustom10("");
        if(matGoodscustPojo.getTenantid()==null) matGoodscustPojo.setTenantid("");
        if(matGoodscustPojo.getTenantname()==null) matGoodscustPojo.setTenantname("");
        if(matGoodscustPojo.getRevision()==null) matGoodscustPojo.setRevision(0);
   }

    @Override
    public List<MatGoodscustPojo> getListByShow(String tenantid) {
        return this.matGoodscustMapper.getListByShow(tenantid);
    }
    @Override
    public List<MatGoodscustPojo> getList(String tenantid) {
        return this.matGoodscustMapper.getList(tenantid);
    }
}
