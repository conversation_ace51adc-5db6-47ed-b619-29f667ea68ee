package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpectempPojo;

/**
 * MI模板(MatSpectemp)表服务接口
 *
 * <AUTHOR>
 * @since 2024-04-10 16:32:06
 */
public interface MatSpectempService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpectempPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpectempPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSpectempPojo 实例对象
     * @return 实例对象
     */
    MatSpectempPojo insert(MatSpectempPojo matSpectempPojo);

    /**
     * 修改数据
     *
     * @param matSpectemppojo 实例对象
     * @return 实例对象
     */
    MatSpectempPojo update(MatSpectempPojo matSpectemppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matSpectempPojo 实例对象
     * @return 实例对象
     */
    MatSpectempPojo approval(MatSpectempPojo matSpectempPojo);
}
