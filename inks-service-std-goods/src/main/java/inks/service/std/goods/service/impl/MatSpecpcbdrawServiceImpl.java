package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecpcbdrawEntity;
import inks.service.std.goods.domain.pojo.MatSpecpcbdrawPojo;
import inks.service.std.goods.mapper.MatSpecpcbdrawMapper;
import inks.service.std.goods.service.MatSpecpcbdrawService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Pcb工艺画(MatSpecpcbdraw)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:26
 */
@Service("matSpecpcbdrawService")
public class MatSpecpcbdrawServiceImpl implements MatSpecpcbdrawService {
    @Resource
    private MatSpecpcbdrawMapper matSpecpcbdrawMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecpcbdrawPojo getEntity(String key, String tid) {
        return this.matSpecpcbdrawMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecpcbdrawPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecpcbdrawPojo> lst = matSpecpcbdrawMapper.getPageList(queryParam);
            PageInfo<MatSpecpcbdrawPojo> pageInfo = new PageInfo<MatSpecpcbdrawPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSpecpcbdrawPojo> getList(String Pid, String tid) {
        try {
            List<MatSpecpcbdrawPojo> lst = matSpecpcbdrawMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSpecpcbdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbdrawPojo insert(MatSpecpcbdrawPojo matSpecpcbdrawPojo) {
        //初始化item的NULL
        MatSpecpcbdrawPojo itempojo = this.clearNull(matSpecpcbdrawPojo);
        MatSpecpcbdrawEntity matSpecpcbdrawEntity = new MatSpecpcbdrawEntity();
        BeanUtils.copyProperties(itempojo, matSpecpcbdrawEntity);

        matSpecpcbdrawEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSpecpcbdrawEntity.setRevision(1);  //乐观锁
        this.matSpecpcbdrawMapper.insert(matSpecpcbdrawEntity);
        return this.getEntity(matSpecpcbdrawEntity.getId(), matSpecpcbdrawEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecpcbdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbdrawPojo update(MatSpecpcbdrawPojo matSpecpcbdrawPojo) {
        MatSpecpcbdrawEntity matSpecpcbdrawEntity = new MatSpecpcbdrawEntity();
        BeanUtils.copyProperties(matSpecpcbdrawPojo, matSpecpcbdrawEntity);
        this.matSpecpcbdrawMapper.update(matSpecpcbdrawEntity);
        return this.getEntity(matSpecpcbdrawEntity.getId(), matSpecpcbdrawEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSpecpcbdrawMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSpecpcbdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbdrawPojo clearNull(MatSpecpcbdrawPojo matSpecpcbdrawPojo) {
        //初始化NULL字段
        if (matSpecpcbdrawPojo.getPid() == null) matSpecpcbdrawPojo.setPid("");
        if (matSpecpcbdrawPojo.getDrawtype() == null) matSpecpcbdrawPojo.setDrawtype("");
        if (matSpecpcbdrawPojo.getDrawtitle() == null) matSpecpcbdrawPojo.setDrawtitle("");
        if (matSpecpcbdrawPojo.getDrawimage() == null) matSpecpcbdrawPojo.setDrawimage("");
        if (matSpecpcbdrawPojo.getDrawjson() == null) matSpecpcbdrawPojo.setDrawjson("");
        if (matSpecpcbdrawPojo.getDrawurl() == null) matSpecpcbdrawPojo.setDrawurl("");
        if (matSpecpcbdrawPojo.getInsidemark() == null) matSpecpcbdrawPojo.setInsidemark(0);
        if (matSpecpcbdrawPojo.getRownum() == null) matSpecpcbdrawPojo.setRownum(0);
        if (matSpecpcbdrawPojo.getCreateby() == null) matSpecpcbdrawPojo.setCreateby("");
        if (matSpecpcbdrawPojo.getCreatebyid() == null) matSpecpcbdrawPojo.setCreatebyid("");
        if (matSpecpcbdrawPojo.getCreatedate() == null) matSpecpcbdrawPojo.setCreatedate(new Date());
        if (matSpecpcbdrawPojo.getLister() == null) matSpecpcbdrawPojo.setLister("");
        if (matSpecpcbdrawPojo.getListerid() == null) matSpecpcbdrawPojo.setListerid("");
        if (matSpecpcbdrawPojo.getModifydate() == null) matSpecpcbdrawPojo.setModifydate(new Date());
        if (matSpecpcbdrawPojo.getCustom1() == null) matSpecpcbdrawPojo.setCustom1("");
        if (matSpecpcbdrawPojo.getCustom2() == null) matSpecpcbdrawPojo.setCustom2("");
        if (matSpecpcbdrawPojo.getCustom3() == null) matSpecpcbdrawPojo.setCustom3("");
        if (matSpecpcbdrawPojo.getCustom4() == null) matSpecpcbdrawPojo.setCustom4("");
        if (matSpecpcbdrawPojo.getCustom5() == null) matSpecpcbdrawPojo.setCustom5("");
        if (matSpecpcbdrawPojo.getCustom6() == null) matSpecpcbdrawPojo.setCustom6("");
        if (matSpecpcbdrawPojo.getCustom7() == null) matSpecpcbdrawPojo.setCustom7("");
        if (matSpecpcbdrawPojo.getCustom8() == null) matSpecpcbdrawPojo.setCustom8("");
        if (matSpecpcbdrawPojo.getCustom9() == null) matSpecpcbdrawPojo.setCustom9("");
        if (matSpecpcbdrawPojo.getCustom10() == null) matSpecpcbdrawPojo.setCustom10("");
        if (matSpecpcbdrawPojo.getTenantid() == null) matSpecpcbdrawPojo.setTenantid("");
        if (matSpecpcbdrawPojo.getTenantname() == null) matSpecpcbdrawPojo.setTenantname("");
        if (matSpecpcbdrawPojo.getRevision() == null) matSpecpcbdrawPojo.setRevision(0);
        return matSpecpcbdrawPojo;
    }
}
