package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatEcnEntity;
import inks.service.std.goods.domain.pojo.MatEcnPojo;
import inks.service.std.goods.mapper.MatEcnMapper;
import inks.service.std.goods.service.MatEcnService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 物料变更(MatEcn)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-29 15:02:16
 */
@Service("matEcnService")
public class MatEcnServiceImpl implements MatEcnService {
    @Resource
    private MatEcnMapper matEcnMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatEcnPojo getEntity(String key, String tid) {
        return this.matEcnMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatEcnPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatEcnPojo> lst = matEcnMapper.getPageList(queryParam);
            PageInfo<MatEcnPojo> pageInfo = new PageInfo<MatEcnPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matEcnPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatEcnPojo insert(MatEcnPojo matEcnPojo) {
        //初始化NULL字段
        if (matEcnPojo.getRefno() == null) matEcnPojo.setRefno("");
        if (matEcnPojo.getBilldate() == null) matEcnPojo.setBilldate(new Date());
        if (matEcnPojo.getBilltype() == null) matEcnPojo.setBilltype("");
        if (matEcnPojo.getBilltitle() == null) matEcnPojo.setBilltitle("");
        if (matEcnPojo.getCausation() == null) matEcnPojo.setCausation("");
        if (matEcnPojo.getEclcode() == null) matEcnPojo.setEclcode("");
        if (matEcnPojo.getDescription() == null) matEcnPojo.setDescription("");
        if (matEcnPojo.getGoodsid() == null) matEcnPojo.setGoodsid("");
        if (matEcnPojo.getItemcode() == null) matEcnPojo.setItemcode("");
        if (matEcnPojo.getItemname() == null) matEcnPojo.setItemname("");
        if (matEcnPojo.getItemspec() == null) matEcnPojo.setItemspec("");
        if (matEcnPojo.getItemunit() == null) matEcnPojo.setItemunit("");
        if (matEcnPojo.getGroupid() == null) matEcnPojo.setGroupid("");
        if (matEcnPojo.getGoodsuidold() == null) matEcnPojo.setGoodsuidold("");
        if (matEcnPojo.getGoodsuidnew() == null) matEcnPojo.setGoodsuidnew("");
        if (matEcnPojo.getDisposeold() == null) matEcnPojo.setDisposeold("");
        if (matEcnPojo.getDisposenew() == null) matEcnPojo.setDisposenew("");
        if (matEcnPojo.getDisposesto() == null) matEcnPojo.setDisposesto("");
        if (matEcnPojo.getDisposewk() == null) matEcnPojo.setDisposewk("");
        if (matEcnPojo.getPhotoold() == null) matEcnPojo.setPhotoold("");
        if (matEcnPojo.getPhotonew() == null) matEcnPojo.setPhotonew("");
        if (matEcnPojo.getDetailjson() == null) matEcnPojo.setDetailjson("");
        if (matEcnPojo.getRemark() == null) matEcnPojo.setRemark("");
        if (matEcnPojo.getCreateby() == null) matEcnPojo.setCreateby("");
        if (matEcnPojo.getCreatebyid() == null) matEcnPojo.setCreatebyid("");
        if (matEcnPojo.getCreatedate() == null) matEcnPojo.setCreatedate(new Date());
        if (matEcnPojo.getLister() == null) matEcnPojo.setLister("");
        if (matEcnPojo.getListerid() == null) matEcnPojo.setListerid("");
        if (matEcnPojo.getModifydate() == null) matEcnPojo.setModifydate(new Date());
        if (matEcnPojo.getAssessor() == null) matEcnPojo.setAssessor("");
        if (matEcnPojo.getAssessorid() == null) matEcnPojo.setAssessorid("");
        if (matEcnPojo.getAssessdate() == null) matEcnPojo.setAssessdate(new Date());
        if (matEcnPojo.getCustom1() == null) matEcnPojo.setCustom1("");
        if (matEcnPojo.getCustom2() == null) matEcnPojo.setCustom2("");
        if (matEcnPojo.getCustom3() == null) matEcnPojo.setCustom3("");
        if (matEcnPojo.getCustom4() == null) matEcnPojo.setCustom4("");
        if (matEcnPojo.getCustom5() == null) matEcnPojo.setCustom5("");
        if (matEcnPojo.getCustom6() == null) matEcnPojo.setCustom6("");
        if (matEcnPojo.getCustom7() == null) matEcnPojo.setCustom7("");
        if (matEcnPojo.getCustom8() == null) matEcnPojo.setCustom8("");
        if (matEcnPojo.getCustom9() == null) matEcnPojo.setCustom9("");
        if (matEcnPojo.getCustom10() == null) matEcnPojo.setCustom10("");
        if (matEcnPojo.getTenantid() == null) matEcnPojo.setTenantid("");
        if (matEcnPojo.getRevision() == null) matEcnPojo.setRevision(0);
        MatEcnEntity matEcnEntity = new MatEcnEntity();
        BeanUtils.copyProperties(matEcnPojo, matEcnEntity);

        matEcnEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matEcnEntity.setRevision(1);  //乐观锁
        this.matEcnMapper.insert(matEcnEntity);
        return this.getEntity(matEcnEntity.getId(), matEcnEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matEcnPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatEcnPojo update(MatEcnPojo matEcnPojo) {
        MatEcnEntity matEcnEntity = new MatEcnEntity();
        BeanUtils.copyProperties(matEcnPojo, matEcnEntity);
        this.matEcnMapper.update(matEcnEntity);
        return this.getEntity(matEcnEntity.getId(), matEcnEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matEcnMapper.delete(key, tid);
    }

    /**
     * 审核数据
     *
     * @param matEcnPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatEcnPojo approval(MatEcnPojo matEcnPojo) {
        //主表更改
        MatEcnEntity matEcnEntity = new MatEcnEntity();
        BeanUtils.copyProperties(matEcnPojo, matEcnEntity);
        this.matEcnMapper.approval(matEcnEntity);
        //返回Bill实例
        return this.getEntity(matEcnEntity.getId(), matEcnEntity.getTenantid());
    }

}
