package inks.service.std.goods.mapper;

import inks.common.core.domain.MatBomdetailPojo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatBomitemEntity;
import inks.service.std.goods.domain.pojo.MatBomitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * BOM子表(MatBomitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-08 10:21:20
 */
 @Mapper
public interface MatBomitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatBomitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatBomitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matBomitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatBomitemEntity matBomitemEntity);

    
    /**
     * 修改数据
     *
     * @param matBomitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatBomitemEntity matBomitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<MatBomitemPojo> getListByGoodsid(@Param("key") String key,@Param("pid") String pid,@Param("tid") String tid);


    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatBomdetailPojo> getDetailList(@Param("pid") String Pid, @Param("treeid") String Parentid,@Param("tid") String tid);

    List<String> getPidsByGoodsid(@Param("key")String key, @Param("tid")String tid);
}

