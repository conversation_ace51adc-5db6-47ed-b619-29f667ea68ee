package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSupplierEntity;
import inks.service.std.goods.domain.pojo.MatSupplierPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货品供应(MatSupplier)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-03 19:51:11
 */
@Mapper
public interface MatSupplierMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSupplierPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSupplierPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param matSupplierEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSupplierEntity matSupplierEntity);

    
    /**
     * 修改数据
     *
     * @param matSupplierEntity 实例对象
     * @return 影响行数
     */
    int update(MatSupplierEntity matSupplierEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                                                          }

