package inks.service.std.goods.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.BillCodeUtil;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatGoodsPojo;
import inks.service.std.goods.domain.pojo.MatGroupPojo;
import inks.service.std.goods.service.MatGoodsService;
import inks.service.std.goods.service.MatGroupService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 货品信息(Mat_Goods)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 08:36:12
 */

public class MatGoodsController {
    /**
     * 服务对象
     */
    @Resource
    private MatGoodsService matGoodsService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 服务对象
     */
    @Resource
    private MatGroupService matGroupService;
    @Resource
    private UtilsFeignService utilsFeignService;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取货品信息详细信息", notes = "获取货品信息详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matGoodsService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增货品信息", notes = "新增货品信息", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @NoRepeatSubmit(timeout = 4000)
    @PreAuthorize(hasPermi = "Mat_Goods.Add")
    public R<MatGoodsPojo> create(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        MatGoodsPojo matGoodsPojo = JSONArray.parseObject(json, MatGoodsPojo.class);
        matGoodsPojo.setCreateby(loginUser.getRealname());   // 创建者
        matGoodsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        matGoodsPojo.setCreatedate(new Date());   // 创建时间
        matGoodsPojo.setLister(loginUser.getRealname());   // 制表
        matGoodsPojo.setListerid(loginUser.getUserid());    // 制表id
        matGoodsPojo.setModifydate(new Date());   //修改时间
        matGoodsPojo.setTenantid(loginUser.getTenantid());   //租户id
        // 读取指定系统参数 货品建立是否允许重名 true/false
        String allow = systemFeignService.getConfigValue("module.goods.allowduplicatenames", matGoodsPojo.getTenantid(), loginUser.getToken()).getData();
        return R.ok(this.matGoodsService.insert(matGoodsPojo, allow, warn));
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改货品信息", notes = "修改货品信息", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<MatGoodsPojo> update(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        MatGoodsPojo matGoodsPojo = JSONArray.parseObject(json, MatGoodsPojo.class);
        matGoodsPojo.setLister(loginUser.getRealname());   // 制表
        matGoodsPojo.setListerid(loginUser.getUserid());    // 制表id
        matGoodsPojo.setTenantid(loginUser.getTenantid());   //租户id
        matGoodsPojo.setModifydate(new Date());   //修改时间
        // 读取指定系统参数 货品建立是否允许重名 true/false
        String allow = systemFeignService.getConfigValue("module.goods.allowduplicatenames", matGoodsPojo.getTenantid(), loginUser.getToken()).getData();
        return R.ok(this.matGoodsService.update(matGoodsPojo, allow, warn));
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除货品信息", notes = "删除货品信息", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Delete")
    @OperLog(title = "删除货品信息")
    public R<MatGoodsPojo> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //检查引用
            List<String> lstcite = this.matGoodsService.getCiteBillName(key, loginUser.getTenantid());
            if (!lstcite.isEmpty()) {
                return R.fail(403, key + "(货品id)被以下单据引用禁止删除:" + lstcite);
            }
            MatGoodsPojo matGoodsPojo = this.matGoodsService.getEntity(key, loginUser.getTenantid());
            this.matGoodsService.delete(key, loginUser.getTenantid());
            return R.ok(matGoodsPojo, matGoodsPojo.getGoodsuid() + "删除完成");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatGoodsPojo matGoodsPojo = this.matGoodsService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matGoodsPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /*
     * esay poi导入导出 时间2021-12-13 song
     * */
    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        List<MatGoodsPojo> list = new ArrayList<>();
        //创建表格
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("货品信息", ""),
                MatGoodsPojo.class, list);
        try {
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "货品信息模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<MatGoodsPojo>> importExecl(String groupid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<MatGoodsPojo> list = POIUtil.importExcel(file.getInputStream(), MatGoodsPojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "识别编码", notes = "识别编码", produces = "application/json")
    @RequestMapping(value = "/getEntityBynsp", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityBynsp(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatGoodsPojo matGoodsPojo = JSONArray.parseObject(json, MatGoodsPojo.class);
            if (matGoodsPojo.getGoodsuid() == null) matGoodsPojo.setGoodsuid("");
            if (matGoodsPojo.getGoodsname() == null) matGoodsPojo.setGoodsname("");
            if (matGoodsPojo.getGoodsspec() == null) matGoodsPojo.setGoodsspec("");
            if (matGoodsPojo.getPartid() == null) matGoodsPojo.setPartid("");
            MatGoodsPojo matGoods = matGoodsService.getEntityByNameSpecPart(matGoodsPojo.getGoodsname().trim(), matGoodsPojo.getGoodsspec().trim(), matGoodsPojo.getPartid().trim(), loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "导入货品信息", notes = "导入货品信息", produces = "application/json")
    @RequestMapping(value = "/importEntity", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.Add")
    public R<MatGoodsPojo> importEntity(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        MatGoodsPojo matGoodsPojo = JSONArray.parseObject(json, MatGoodsPojo.class);
        matGoodsPojo.setCreateby(loginUser.getRealname());   //创建者
        matGoodsPojo.setCreatedate(new Date());   //创建时间
        matGoodsPojo.setLister(loginUser.getRealname());   //用户名
        matGoodsPojo.setModifydate(new Date());   //修改时间
        matGoodsPojo.setTenantid(loginUser.getTenantid());   //租户id
        //查询货品信息是否存在
        MatGoodsPojo pojo = matGoodsService.getEntityByNameSpecPart(matGoodsPojo.getGoodsname(), matGoodsPojo.getGoodsspec(), matGoodsPojo.getPartid(), loginUser.getTenantid());
        //如果存在不做操作
        if (pojo != null) {
            return R.ok(pojo);
        } else {
            //读取分组信息，取前缀、后缀、序号
            MatGroupPojo matGroupPojo = this.matGroupService.getEntity(matGoodsPojo.getUidgroupguid(), loginUser.getTenantid());
            if (matGroupPojo != null) {
                if (matGroupPojo.getSncode() != null && !matGroupPojo.getSncode().equals("")) {
                    //否则查询当前分组序号Uidgroupnum最大 货品编码
                    MatGoodsPojo goodsPojo = matGoodsService.getEntityByGroup(matGoodsPojo.getUidgroupguid(), loginUser.getTenantid());
                    String goodsuid = "";
                    if (goodsPojo != null) goodsuid = goodsPojo.getGoodsuid();
                    //取出最近一条货品编码+1
                    String snCode = BillCodeUtil.getSnNumByPs(matGroupPojo.getPrefix(), matGroupPojo.getSuffix(), matGroupPojo.getSncode(), goodsuid);
                    //拼接料号
                    matGoodsPojo.setGoodsuid(matGroupPojo.getPrefix() + snCode + matGroupPojo.getSuffix());
                    //转Int存序号中
                    matGoodsPojo.setUidgroupnum(Integer.parseInt(snCode));
                } else {
                    if (matGoodsPojo.getGoodsuid() == null || matGoodsPojo.getGoodsuid().equals("")) {
                        return R.fail("分组中未设定料号组合方式,导入数报中需带料号");
                    }
                }
                matGoodsPojo.setUidgroupcode(matGroupPojo.getGroupcode());
                matGoodsPojo.setUidgroupname(matGroupPojo.getGroupname());
                matGoodsPojo.setUidgroupguid(matGroupPojo.getId());
                // 读取指定系统参数 货品建立是否允许重名 true/false
                String allow = systemFeignService.getConfigValue("module.goods.allowduplicatenames", matGoodsPojo.getTenantid(), loginUser.getToken()).getData();
                matGoodsPojo = this.matGoodsService.insert(matGoodsPojo, allow, warn);
            } else {
                return R.fail("未找到相应分组信息");
            }
        }
        return R.ok(matGoodsPojo);
    }

    @ApiOperation(value = "导入货品信息", notes = "导入货品信息", produces = "application/json")
    @RequestMapping(value = "/getNewUidByGroup", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getNewUidByGroup(String key) {
        try {
            MatGoodsPojo matGoodsPojo = new MatGoodsPojo();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //读取分组信息，取前缀、后缀、序号
            MatGroupPojo matGroupPojo = this.matGroupService.getEntity(key, loginUser.getTenantid());
            if (matGroupService != null) {
                //否则查询当前分组序号Uidgroupnum最大 货品编码
                MatGoodsPojo goodsPojo = matGoodsService.getEntityByGroup(key, loginUser.getTenantid());
                //取出最近一条货品编码+1
                String goodsuid = "";
                if (goodsPojo != null) goodsuid = goodsPojo.getGoodsuid();
                String snCode = BillCodeUtil.getSnNumByPs(matGroupPojo.getPrefix(), matGroupPojo.getSuffix(), matGroupPojo.getSncode(), goodsuid);

                //拼接料号
                matGoodsPojo.setGoodsuid(matGroupPojo.getPrefix() + snCode + matGroupPojo.getSuffix());
                //转Int存序号中
                matGoodsPojo.setUidgroupnum(Integer.parseInt(snCode));
                matGoodsPojo.setUidgroupcode(matGroupPojo.getGroupcode());
                matGoodsPojo.setUidgroupname(matGroupPojo.getGroupname());
                matGoodsPojo.setUidgroupguid(matGroupPojo.getId());
                //返回的分组的batchmg、batchonly、packsnmark、skumark
                matGoodsPojo.setBatchmg(matGroupPojo.getBatchmg());
                matGoodsPojo.setBatchonly(matGroupPojo.getBatchonly());
                matGoodsPojo.setPacksnmark(matGroupPojo.getPacksnmark());
                matGoodsPojo.setSkumark(matGroupPojo.getSkumark());

            } else {
                return R.fail("未找到相应分组信息");
            }

            return R.ok(matGoodsPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印MatGoods明细报表(分页PageList)", notes = "打印明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Mat_Goods.CreateDate");
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Mat_Goods.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<MatGoodsPojo> lst = this.matGoodsService.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    MatGoodsPojo pojo = new MatGoodsPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "批量云打印报表(List<MatGoodsPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.Print")
    public R<String> printWebPageList(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<MatGoodsPojo> lstTh = this.matGoodsService.getPageList(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "MatGoods批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "云打印报表(拆原图+缩略图GoodsPhoto1,GoodsPhoto2)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            MatGoodsPojo matGoodsPojo = this.matGoodsService.getEntity(key, loginUser.getTenantid());
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(matGoodsPojo);

            // 拆图片：GoodsPhoto1格式为 {"originUrl": "","thumbUrl": ""}
            processPhotoInfo(matGoodsPojo.getGoodsphoto1(), "photo1", map);
            processPhotoInfo(matGoodsPojo.getGoodsphoto2(), "photo2", map);

            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
//                    List<MatGoodsitemPojo> lstitem = this.matGoodsitemService.getList(key, loginUser.getTenantid());
//                    // 单据Item. 带属性List转为Map  EricRen 20220427
//                    List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(lstitem);
            List<Map<String, Object>> lst = new ArrayList<>();
            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "MatGoods" + matGoodsPojo.getGoodsuid());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限

            // 刷入打印Num++


            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 封装处理商品照片信息的方法
    // 拆图片：Goodsphoto1格式为 {"originUrl": "http://inks.tpddns.net:9080/utils/pic/20231218/4088b83b1d041c1a5dec.jpg","thumbUrl": "http://inks.tpddns.net:9080/utils/pic/20231218/0400ec2b96e4bf19817e.jpg"}
    private void processPhotoInfo(String goodsPhoto, String photoKey, Map<String, Object> map) {
        if (StringUtils.isNotBlank(goodsPhoto)) {
            Map<String, String> goodsPhotoMap = JSON.parseObject(goodsPhoto, new TypeReference<Map<String, String>>() {
            });
            // 获取originUrl和thumbUrl并添加到map中
            if (goodsPhotoMap != null) {
                map.put(photoKey + "origin", goodsPhotoMap.get("originUrl"));
                map.put(photoKey + "thumb", goodsPhotoMap.get("thumbUrl"));
            }
        }
    }


}

