package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecpcbdrlPojo;

import java.util.List;

/**
 * Pcb工艺Drl(MatSpecpcbdrl)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:47
 */
public interface MatSpecpcbdrlService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecpcbdrlPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecpcbdrlPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecpcbdrlPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSpecpcbdrlPojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbdrlPojo insert(MatSpecpcbdrlPojo matSpecpcbdrlPojo);

    /**
     * 修改数据
     *
     * @param matSpecpcbdrlpojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbdrlPojo update(MatSpecpcbdrlPojo matSpecpcbdrlpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSpecpcbdrlpojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbdrlPojo clearNull(MatSpecpcbdrlPojo matSpecpcbdrlpojo);
}
