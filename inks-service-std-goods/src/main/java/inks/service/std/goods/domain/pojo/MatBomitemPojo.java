package inks.service.std.goods.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;

/**
 * BOM子表(MatBomitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-04-22 20:21:47
 */
public class MatBomitemPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = -49566951102699329L;
    //+是否有BOM了
    private String bomid;

    @Excel(name = "")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 商品ID
    @Excel(name = "商品ID")
    private String goodsid;
    // 产品编码
    @Excel(name = "产品编码")
    private String itemcode;
    // 产品名称
    @Excel(name = "产品名称")
    private String itemname;
    // 产品规格
    @Excel(name = "产品规格")
    private String itemspec;
    // 产品单位
    @Excel(name = "产品单位")
    private String itemunit;
    // 主件数量
    @Excel(name = "主件数量")
    private Double mainqty;
    // 子件数量
    @Excel(name = "子件数量")
    private Double subqty;
    // 损耗率
    @Excel(name = "损耗率")
    private Double lossrate;
    // 属性 厂制/委制/外购/客供
    @Excel(name = "属性 厂制/委制/外购/客供")
    private String attrcode;
    // 流程编码
    @Excel(name = "流程编码")
    private String flowcode;
    // 描述
    @Excel(name = "描述")
    private String description;
    // 标签
    @Excel(name = "标签")
    private String itemlabel;
    // 替代主料id
    @Excel(name = "替代主料id")
    private String parentid;
    // 主料商品ID
    @Excel(name = "主料商品ID")
    private String parentgoodsid;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 替代品数
    @Excel(name = "替代品数")
    private Integer subcount;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 单件含损量
    @Excel(name = "单件含损量")
  private Double sublossqty;
     // 物料类型
  @Excel(name = "物料类型")
  private Integer mattype;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 计算的bom数量
    private Double bomqty;
    //可用量：仓库+在途-待出
    private Double avaiqty;
    //MRP应需量
    private Double needqty;
    //计划领用量
    private Double stoplanqty;
    //实际需求量
    private Double realqty;
    // 采购在订
    @Excel(name = "采购在订")
    private Double buyremqty;
    // 生产在制
    @Excel(name = "生产在制")
    private Double wkwsremqty;
    // 加工在制
    @Excel(name = "加工在制")
    private Double wkscremqty;
    // 销售待出
    @Excel(name = "销售待出")
    private Double busremqty;
    // 运算占用
    @Excel(name = "运算占用")
    private Double mrpremqty;
    // 领料待出
    @Excel(name = "领料待出")
    private Double requremqty;
    // 库存数量
    @Excel(name = "库存数量")
    private Double ivquantity;
    //GoodsState
    private String goodsstate;

    public Double getBomqty() {
        return bomqty;
    }

    public void setBomqty(Double bomqty) {
        this.bomqty = bomqty;
    }

    public String getGoodsstate() {
        return goodsstate;
    }

    public void setGoodsstate(String goodsstate) {
        this.goodsstate = goodsstate;
    }

    public Double getAvaiqty() {
        return avaiqty;
    }

    public void setAvaiqty(Double avaiqty) {
        this.avaiqty = avaiqty;
    }

    public Double getBuyremqty() {
        return buyremqty;
    }

    public void setBuyremqty(Double buyremqty) {
        this.buyremqty = buyremqty;
    }

    public Double getWkwsremqty() {
        return wkwsremqty;
    }

    public void setWkwsremqty(Double wkwsremqty) {
        this.wkwsremqty = wkwsremqty;
    }


    public Double getWkscremqty() {
        return wkscremqty;
    }

    public void setWkscremqty(Double wkscremqty) {
        this.wkscremqty = wkscremqty;
    }

    public Double getBusremqty() {
        return busremqty;
    }

    public void setBusremqty(Double busremqty) {
        this.busremqty = busremqty;
    }

    public Double getMrpremqty() {
        return mrpremqty;
    }

    public void setMrpremqty(Double mrpremqty) {
        this.mrpremqty = mrpremqty;
    }

    public Double getRequremqty() {
        return requremqty;
    }

    public void setRequremqty(Double requremqty) {
        this.requremqty = requremqty;
    }

    public Double getIvquantity() {
        return ivquantity;
    }

    public void setIvquantity(Double ivquantity) {
        this.ivquantity = ivquantity;
    }

    public Double getNeedqty() {
        return needqty;
    }

    public void setNeedqty(Double needqty) {
        this.needqty = needqty;
    }

    public Double getStoplanqty() {
        return stoplanqty;
    }

    public void setStoplanqty(Double stoplanqty) {
        this.stoplanqty = stoplanqty;
    }

    public Double getRealqty() {
        return realqty;
    }

    public void setRealqty(Double realqty) {
        this.realqty = realqty;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBomid() {
        return bomid;
    }

    public void setBomid(String bomid) {
        this.bomid = bomid;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 商品ID
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 产品编码
    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    // 产品名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 产品规格
    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    // 产品单位
    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    // 主件数量
    public Double getMainqty() {
        return mainqty;
    }

    public void setMainqty(Double mainqty) {
        this.mainqty = mainqty;
    }

    // 子件数量
    public Double getSubqty() {
        return subqty;
    }

    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }

    // 损耗率
    public Double getLossrate() {
        return lossrate;
    }

    public void setLossrate(Double lossrate) {
        this.lossrate = lossrate;
    }

    // 属性 厂制/委制/外购/客供
    public String getAttrcode() {
        return attrcode;
    }

    public void setAttrcode(String attrcode) {
        this.attrcode = attrcode;
    }

    // 流程编码
    public String getFlowcode() {
        return flowcode;
    }

    public void setFlowcode(String flowcode) {
        this.flowcode = flowcode;
    }

    // 描述
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // 标签
    public String getItemlabel() {
        return itemlabel;
    }

    public void setItemlabel(String itemlabel) {
        this.itemlabel = itemlabel;
    }

    // 替代主料id
    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    // 主料商品ID
    public String getParentgoodsid() {
        return parentgoodsid;
    }

    public void setParentgoodsid(String parentgoodsid) {
        this.parentgoodsid = parentgoodsid;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 替代品数
    public Integer getSubcount() {
        return subcount;
    }

    public void setSubcount(Integer subcount) {
        this.subcount = subcount;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

   // 单件含损量
    public Double getSublossqty() {
        return sublossqty;
    }

    public void setSublossqty(Double sublossqty) {
        this.sublossqty = sublossqty;
    }

   // 物料类型
    public Integer getMattype() {
        return mattype;
    }

    public void setMattype(Integer mattype) {
        this.mattype = mattype;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

