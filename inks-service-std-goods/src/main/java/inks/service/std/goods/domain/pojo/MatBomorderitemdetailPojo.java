package inks.service.std.goods.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;

/**
 * 订单Bom项目(MatBomorderitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-02-08 10:21:37
 */
public class MatBomorderitemdetailPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = 743547461365284235L;
    @Excel(name = "")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 商品id
    @Excel(name = "商品id")
    private String goodsid;
    // 产品编码
    @Excel(name = "产品编码")
    private String itemcode;
    // 产品名称
    @Excel(name = "产品名称")
    private String itemname;
    // 产品规格
    @Excel(name = "产品规格")
    private String itemspec;
    // 产品单位
    @Excel(name = "产品单位")
    private String itemunit;
    // 主件数量
    @Excel(name = "主件数量")
    private Double mainqty;
    // 子件数量
    @Excel(name = "子件数量")
    private Double subqty;
    // 应需数量
    @Excel(name = "应需数量")
    private Double needqty;
    // 损耗率
    @Excel(name = "损耗率")
    private Double lossrate;
    // 属性 厂制/委制/外购/客供
    @Excel(name = "属性 厂制/委制/外购/客供")
    private String attrcode;
    // 流程
    @Excel(name = "流程")
    private String flowcode;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    private String bomtype;

    private String itemgoodsid;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 商品id
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 产品编码
    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    // 产品名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 产品规格
    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    // 产品单位
    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    // 主件数量
    public Double getMainqty() {
        return mainqty;
    }

    public void setMainqty(Double mainqty) {
        this.mainqty = mainqty;
    }

    // 子件数量
    public Double getSubqty() {
        return subqty;
    }

    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }

    // 应需数量
    public Double getNeedqty() {
        return needqty;
    }

    public void setNeedqty(Double needqty) {
        this.needqty = needqty;
    }

    // 损耗率
    public Double getLossrate() {
        return lossrate;
    }

    public void setLossrate(Double lossrate) {
        this.lossrate = lossrate;
    }

    // 属性 厂制/委制/外购/客供
    public String getAttrcode() {
        return attrcode;
    }

    public void setAttrcode(String attrcode) {
        this.attrcode = attrcode;
    }

    // 流程
    public String getFlowcode() {
        return flowcode;
    }

    public void setFlowcode(String flowcode) {
        this.flowcode = flowcode;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getBomtype() {
        return bomtype;
    }

    public void setBomtype(String bomtype) {
        this.bomtype = bomtype;
    }

    public String getItemgoodsid() {
        return itemgoodsid;
    }

    public void setItemgoodsid(String itemgoodsid) {
        this.itemgoodsid = itemgoodsid;
    }
}

