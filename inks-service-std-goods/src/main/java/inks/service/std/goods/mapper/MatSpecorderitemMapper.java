package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSpecorderitemEntity;
import inks.service.std.goods.domain.pojo.MatSpecorderitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工艺项目(MatSpecorderitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-16 14:43:48
 */
 @Mapper
public interface MatSpecorderitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecorderitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecorderitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecorderitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matSpecorderitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpecorderitemEntity matSpecorderitemEntity);

    
    /**
     * 修改数据
     *
     * @param matSpecorderitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpecorderitemEntity matSpecorderitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

