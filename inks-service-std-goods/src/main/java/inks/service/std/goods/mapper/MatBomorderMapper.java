package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatBomorderEntity;
import inks.service.std.goods.domain.pojo.MatBomorderPojo;
import inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单Bom(MatBomorder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-08 11:21:20
 */
@Mapper
public interface MatBomorderMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomorderPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatBomorderitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatBomorderPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matBomorderEntity 实例对象
     * @return 影响行数
     */
    int insert(MatBomorderEntity matBomorderEntity);


    /**
     * 修改数据
     *
     * @param matBomorderEntity 实例对象
     * @return 影响行数
     */
    int update(MatBomorderEntity matBomorderEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matBomorderPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(MatBomorderPojo matBomorderPojo);

    /**
     * 修改数据
     *
     * @param matBomorderEntity 实例对象
     * @return 影响行数
     */
    int approval(MatBomorderEntity matBomorderEntity);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomorderPojo getEntityByGoodsid(@Param("key") String key, @Param("tid") String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<MatBomorderitemdetailPojo> getListByItemGoodsid(@Param("key") String key, @Param("tid") String tid);

}

