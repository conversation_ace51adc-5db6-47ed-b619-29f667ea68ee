package inks.service.std.goods.domain.export;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 货品信息(MatGoods)实体类
 *
 * <AUTHOR>
 * @since 2022-06-10 09:02:52
 */
public class MatGoodsPojo implements Serializable {
    private static final long serialVersionUID = 531924494969770160L;
    // ID
    @Excel(name = "ID")
    private String id;
    // 货品编码
    @Excel(name = "货品编码")
    private String goodsuid;
    // 名称
    @Excel(name = "名称")
    private String goodsname;
    // 规格
    @Excel(name = "规格")
    private String goodsspec;
    // 货品单位
    @Excel(name = "货品单位")
    private String goodsunit;
    // 货品状态
    @Excel(name = "货品状态")
    private String goodsstate;
    // 拼音码
    @Excel(name = "拼音码")
    private String goodspinyin;
    // 版本号
    @Excel(name = "版本号")
    private String versionnum;
    // 材质
    @Excel(name = "材质")
    private String material;
    // 表面处理
    @Excel(name = "表面处理")
    private String surface;
    // 条形码
    @Excel(name = "条形码")
    private String barcode;
    // 安全库存
    @Excel(name = "安全库存")
    private Double safestock;
    // 建议进价
    @Excel(name = "建议进价")
    private Double inprice;
    // 建议售价
    @Excel(name = "建议售价")
    private Double outprice;
    // 关联厂商
    @Excel(name = "关联厂商")
    private String groupid;
    // 相关附件
    @Excel(name = "相关附件")
    private String fileguid;
    // 图档名称
    @Excel(name = "图档名称")
    private String drawing;
    // 默认仓库
    @Excel(name = "默认仓库")
    private String storeid;
    // 受权仓库
    @Excel(name = "受权仓库")
    private String storelistname;
    // 受权仓库GUID
    @Excel(name = "受权仓库GUID")
    private String storelistguid;
    // 库存数量
    @Excel(name = "库存数量")
    private Double ivquantity;
    // 库存单价
    @Excel(name = "库存单价")
    private Double ageprice;
    // 分组Guid
    @Excel(name = "分组Guid")
    private String uidgroupguid;
    // 分组编码
    @Excel(name = "分组编码")
    private String uidgroupcode;
    // 分组名称
    @Excel(name = "分组名称")
    private String uidgroupname;
    // 分组序号
    @Excel(name = "分组序号")
    private Integer uidgroupnum;
    // 外部编码
    @Excel(name = "外部编码")
    private String partid;
    // 主料号id
    @Excel(name = "主料号id")
    private String pid;
    // 主料号
    @Excel(name = "主料号")
    private String puid;
    // 有效标识
    @Excel(name = "有效标识")
    private Integer enabledmark;
    // 附图1
    @Excel(name = "附图1")
    private String goodsphoto1;
    // 附图2
    @Excel(name = "附图2")
    private String goodsphoto2;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 删除标识
    @Excel(name = "删除标识")
    private Integer deletemark;
    // 删除人员
    @Excel(name = "删除人员")
    private String deletelister;
    // 删除人员id
    @Excel(name = "删除人员id")
    private String deletelisterid;
    // 删除日期
    @Excel(name = "删除日期")
    private Date deletedate;
    // 批次管理
    @Excel(name = "批次管理")
    private Integer batchmg;
    // 批次独立
    @Excel(name = "批次独立")
    private Integer batchonly;
    // Sku库存
    @Excel(name = "Sku库存")
    private Integer skumark;
    // SN包装
    @Excel(name = "SN包装")
    private Integer packsnmark;
    // 虚拟货品
    @Excel(name = "虚拟货品")
    private Integer virtualitem;
    // 标准Bomid
    @Excel(name = "标准Bomid")
    private String bomid;
    // 快速码
    @Excel(name = "快速码")
    private String quickcode;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 部门id
    @Excel(name = "部门id")
    private String deptid;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 仓库编码
    private String storecode;
    // 仓库名称
    private String storename;


    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 货品编码
    public String getGoodsuid() {
        return goodsuid;
    }

    public void setGoodsuid(String goodsuid) {
        this.goodsuid = goodsuid;
    }

    // 名称
    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    // 规格
    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    // 货品单位
    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    // 货品状态
    public String getGoodsstate() {
        return goodsstate;
    }

    public void setGoodsstate(String goodsstate) {
        this.goodsstate = goodsstate;
    }

    // 拼音码
    public String getGoodspinyin() {
        return goodspinyin;
    }

    public void setGoodspinyin(String goodspinyin) {
        this.goodspinyin = goodspinyin;
    }

    // 版本号
    public String getVersionnum() {
        return versionnum;
    }

    public void setVersionnum(String versionnum) {
        this.versionnum = versionnum;
    }

    // 材质
    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    // 表面处理
    public String getSurface() {
        return surface;
    }

    public void setSurface(String surface) {
        this.surface = surface;
    }

    // 条形码
    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    // 安全库存
    public Double getSafestock() {
        return safestock;
    }

    public void setSafestock(Double safestock) {
        this.safestock = safestock;
    }

    // 建议进价
    public Double getInprice() {
        return inprice;
    }

    public void setInprice(Double inprice) {
        this.inprice = inprice;
    }

    // 建议售价
    public Double getOutprice() {
        return outprice;
    }

    public void setOutprice(Double outprice) {
        this.outprice = outprice;
    }

    // 关联厂商
    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    // 相关附件
    public String getFileguid() {
        return fileguid;
    }

    public void setFileguid(String fileguid) {
        this.fileguid = fileguid;
    }

    // 图档名称
    public String getDrawing() {
        return drawing;
    }

    public void setDrawing(String drawing) {
        this.drawing = drawing;
    }

    // 默认仓库
    public String getStoreid() {
        return storeid;
    }

    public void setStoreid(String storeid) {
        this.storeid = storeid;
    }

    // 受权仓库
    public String getStorelistname() {
        return storelistname;
    }

    public void setStorelistname(String storelistname) {
        this.storelistname = storelistname;
    }

    // 受权仓库GUID
    public String getStorelistguid() {
        return storelistguid;
    }

    public void setStorelistguid(String storelistguid) {
        this.storelistguid = storelistguid;
    }

    // 库存数量
    public Double getIvquantity() {
        return ivquantity;
    }

    public void setIvquantity(Double ivquantity) {
        this.ivquantity = ivquantity;
    }

    // 库存单价
    public Double getAgeprice() {
        return ageprice;
    }

    public void setAgeprice(Double ageprice) {
        this.ageprice = ageprice;
    }

    // 分组Guid
    public String getUidgroupguid() {
        return uidgroupguid;
    }

    public void setUidgroupguid(String uidgroupguid) {
        this.uidgroupguid = uidgroupguid;
    }

    // 分组编码
    public String getUidgroupcode() {
        return uidgroupcode;
    }

    public void setUidgroupcode(String uidgroupcode) {
        this.uidgroupcode = uidgroupcode;
    }

    // 分组名称
    public String getUidgroupname() {
        return uidgroupname;
    }

    public void setUidgroupname(String uidgroupname) {
        this.uidgroupname = uidgroupname;
    }

    // 分组序号
    public Integer getUidgroupnum() {
        return uidgroupnum;
    }

    public void setUidgroupnum(Integer uidgroupnum) {
        this.uidgroupnum = uidgroupnum;
    }

    // 外部编码
    public String getPartid() {
        return partid;
    }

    public void setPartid(String partid) {
        this.partid = partid;
    }

    // 主料号id
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 主料号
    public String getPuid() {
        return puid;
    }

    public void setPuid(String puid) {
        this.puid = puid;
    }

    // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 附图1
    public String getGoodsphoto1() {
        return goodsphoto1;
    }

    public void setGoodsphoto1(String goodsphoto1) {
        this.goodsphoto1 = goodsphoto1;
    }

    // 附图2
    public String getGoodsphoto2() {
        return goodsphoto2;
    }

    public void setGoodsphoto2(String goodsphoto2) {
        this.goodsphoto2 = goodsphoto2;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }

    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }

    // 删除人员
    public String getDeletelister() {
        return deletelister;
    }

    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }

    // 删除人员id
    public String getDeletelisterid() {
        return deletelisterid;
    }

    public void setDeletelisterid(String deletelisterid) {
        this.deletelisterid = deletelisterid;
    }

    // 删除日期
    public Date getDeletedate() {
        return deletedate;
    }

    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }

    // 批次管理
    public Integer getBatchmg() {
        return batchmg;
    }

    public void setBatchmg(Integer batchmg) {
        this.batchmg = batchmg;
    }

    // 批次独立
    public Integer getBatchonly() {
        return batchonly;
    }

    public void setBatchonly(Integer batchonly) {
        this.batchonly = batchonly;
    }

    // Sku库存
    public Integer getSkumark() {
        return skumark;
    }

    public void setSkumark(Integer skumark) {
        this.skumark = skumark;
    }

    // SN包装
    public Integer getPacksnmark() {
        return packsnmark;
    }

    public void setPacksnmark(Integer packsnmark) {
        this.packsnmark = packsnmark;
    }

    // 虚拟货品
    public Integer getVirtualitem() {
        return virtualitem;
    }

    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }

    // 标准Bomid
    public String getBomid() {
        return bomid;
    }

    public void setBomid(String bomid) {
        this.bomid = bomid;
    }

    // 快速码
    public String getQuickcode() {
        return quickcode;
    }

    public void setQuickcode(String quickcode) {
        this.quickcode = quickcode;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 部门id
    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getStorecode() {
        return storecode;
    }

    public void setStorecode(String storecode) {
        this.storecode = storecode;
    }

    public String getStorename() {
        return storename;
    }

    public void setStorename(String storename) {
        this.storename = storename;
    }
}

