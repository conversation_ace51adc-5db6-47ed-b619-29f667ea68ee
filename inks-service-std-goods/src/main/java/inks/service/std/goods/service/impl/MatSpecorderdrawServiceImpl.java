package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecorderdrawEntity;
import inks.service.std.goods.domain.pojo.MatSpecorderdrawPojo;
import inks.service.std.goods.mapper.MatSpecorderdrawMapper;
import inks.service.std.goods.service.MatSpecorderdrawService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工艺流程排版(MatSpecorderdraw)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-16 14:44:46
 */
@Service("matSpecorderdrawService")
public class MatSpecorderdrawServiceImpl implements MatSpecorderdrawService {
    @Resource
    private MatSpecorderdrawMapper matSpecorderdrawMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecorderdrawPojo getEntity(String key, String tid) {
        return this.matSpecorderdrawMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecorderdrawPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecorderdrawPojo> lst = matSpecorderdrawMapper.getPageList(queryParam);
            PageInfo<MatSpecorderdrawPojo> pageInfo = new PageInfo<MatSpecorderdrawPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSpecorderdrawPojo> getList(String Pid, String tid) {
        try {
            List<MatSpecorderdrawPojo> lst = matSpecorderdrawMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSpecorderdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecorderdrawPojo insert(MatSpecorderdrawPojo matSpecorderdrawPojo) {
        //初始化item的NULL
        MatSpecorderdrawPojo itempojo = this.clearNull(matSpecorderdrawPojo);
        MatSpecorderdrawEntity matSpecorderdrawEntity = new MatSpecorderdrawEntity();
        BeanUtils.copyProperties(itempojo, matSpecorderdrawEntity);
        //生成雪花id
        matSpecorderdrawEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSpecorderdrawEntity.setRevision(1);  //乐观锁
        this.matSpecorderdrawMapper.insert(matSpecorderdrawEntity);
        return this.getEntity(matSpecorderdrawEntity.getId(), matSpecorderdrawEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecorderdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecorderdrawPojo update(MatSpecorderdrawPojo matSpecorderdrawPojo) {
        MatSpecorderdrawEntity matSpecorderdrawEntity = new MatSpecorderdrawEntity();
        BeanUtils.copyProperties(matSpecorderdrawPojo, matSpecorderdrawEntity);
        this.matSpecorderdrawMapper.update(matSpecorderdrawEntity);
        return this.getEntity(matSpecorderdrawEntity.getId(), matSpecorderdrawEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSpecorderdrawMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSpecorderdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecorderdrawPojo clearNull(MatSpecorderdrawPojo matSpecorderdrawPojo) {
        //初始化NULL字段
        if (matSpecorderdrawPojo.getPid() == null) matSpecorderdrawPojo.setPid("");
        if (matSpecorderdrawPojo.getWpid() == null) matSpecorderdrawPojo.setWpid("");
        if (matSpecorderdrawPojo.getWpcode() == null) matSpecorderdrawPojo.setWpcode("");
        if (matSpecorderdrawPojo.getWpname() == null) matSpecorderdrawPojo.setWpname("");
        if (matSpecorderdrawPojo.getDrawtype() == null) matSpecorderdrawPojo.setDrawtype("");
        if (matSpecorderdrawPojo.getDrawtitle() == null) matSpecorderdrawPojo.setDrawtitle("");
        if (matSpecorderdrawPojo.getDrawimage() == null) matSpecorderdrawPojo.setDrawimage("");
        if (matSpecorderdrawPojo.getDrawjson() == null) matSpecorderdrawPojo.setDrawjson("");
        if (matSpecorderdrawPojo.getDrawurl() == null) matSpecorderdrawPojo.setDrawurl("");
        if (matSpecorderdrawPojo.getInsidemark() == null) matSpecorderdrawPojo.setInsidemark(0);
        if (matSpecorderdrawPojo.getRownum() == null) matSpecorderdrawPojo.setRownum(0);
        if (matSpecorderdrawPojo.getRemark() == null) matSpecorderdrawPojo.setRemark("");
        if (matSpecorderdrawPojo.getCustom1() == null) matSpecorderdrawPojo.setCustom1("");
        if (matSpecorderdrawPojo.getCustom2() == null) matSpecorderdrawPojo.setCustom2("");
        if (matSpecorderdrawPojo.getCustom3() == null) matSpecorderdrawPojo.setCustom3("");
        if (matSpecorderdrawPojo.getCustom4() == null) matSpecorderdrawPojo.setCustom4("");
        if (matSpecorderdrawPojo.getCustom5() == null) matSpecorderdrawPojo.setCustom5("");
        if (matSpecorderdrawPojo.getCustom6() == null) matSpecorderdrawPojo.setCustom6("");
        if (matSpecorderdrawPojo.getCustom7() == null) matSpecorderdrawPojo.setCustom7("");
        if (matSpecorderdrawPojo.getCustom8() == null) matSpecorderdrawPojo.setCustom8("");
        if (matSpecorderdrawPojo.getCustom9() == null) matSpecorderdrawPojo.setCustom9("");
        if (matSpecorderdrawPojo.getCustom10() == null) matSpecorderdrawPojo.setCustom10("");
        if (matSpecorderdrawPojo.getTenantid() == null) matSpecorderdrawPojo.setTenantid("");
        if (matSpecorderdrawPojo.getRevision() == null) matSpecorderdrawPojo.setRevision(0);
        return matSpecorderdrawPojo;
    }
}
