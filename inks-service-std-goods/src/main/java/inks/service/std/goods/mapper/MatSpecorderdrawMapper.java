package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSpecorderdrawEntity;
import inks.service.std.goods.domain.pojo.MatSpecorderdrawPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工艺流程排版(MatSpecorderdraw)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-16 14:44:46
 */
 @Mapper
public interface MatSpecorderdrawMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecorderdrawPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecorderdrawPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecorderdrawPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matSpecorderdrawEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpecorderdrawEntity matSpecorderdrawEntity);

    
    /**
     * 修改数据
     *
     * @param matSpecorderdrawEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpecorderdrawEntity matSpecorderdrawEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

