package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecorderitemEntity;
import inks.service.std.goods.domain.pojo.MatSpecorderitemPojo;
import inks.service.std.goods.mapper.MatSpecorderitemMapper;
import inks.service.std.goods.service.MatSpecorderitemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工艺项目(MatSpecorderitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-16 14:43:48
 */
@Service("matSpecorderitemService")
public class MatSpecorderitemServiceImpl implements MatSpecorderitemService {
    @Resource
    private MatSpecorderitemMapper matSpecorderitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecorderitemPojo getEntity(String key, String tid) {
        return this.matSpecorderitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecorderitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecorderitemPojo> lst = matSpecorderitemMapper.getPageList(queryParam);
            PageInfo<MatSpecorderitemPojo> pageInfo = new PageInfo<MatSpecorderitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSpecorderitemPojo> getList(String Pid, String tid) {
        try {
            List<MatSpecorderitemPojo> lst = matSpecorderitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSpecorderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecorderitemPojo insert(MatSpecorderitemPojo matSpecorderitemPojo) {
        //初始化item的NULL
        MatSpecorderitemPojo itempojo = this.clearNull(matSpecorderitemPojo);
        MatSpecorderitemEntity matSpecorderitemEntity = new MatSpecorderitemEntity();
        BeanUtils.copyProperties(itempojo, matSpecorderitemEntity);
        //生成雪花id
        matSpecorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSpecorderitemEntity.setRevision(1);  //乐观锁
        this.matSpecorderitemMapper.insert(matSpecorderitemEntity);
        return this.getEntity(matSpecorderitemEntity.getId(), matSpecorderitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecorderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecorderitemPojo update(MatSpecorderitemPojo matSpecorderitemPojo) {
        MatSpecorderitemEntity matSpecorderitemEntity = new MatSpecorderitemEntity();
        BeanUtils.copyProperties(matSpecorderitemPojo, matSpecorderitemEntity);
        this.matSpecorderitemMapper.update(matSpecorderitemEntity);
        return this.getEntity(matSpecorderitemEntity.getId(), matSpecorderitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSpecorderitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSpecorderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecorderitemPojo clearNull(MatSpecorderitemPojo matSpecorderitemPojo) {
        //初始化NULL字段
        if (matSpecorderitemPojo.getPid() == null) matSpecorderitemPojo.setPid("");
        if (matSpecorderitemPojo.getWpid() == null) matSpecorderitemPojo.setWpid("");
        if (matSpecorderitemPojo.getWpcode() == null) matSpecorderitemPojo.setWpcode("");
        if (matSpecorderitemPojo.getWpname() == null) matSpecorderitemPojo.setWpname("");
        if (matSpecorderitemPojo.getDescription() == null) matSpecorderitemPojo.setDescription("");
        if (StringUtils.isBlank(matSpecorderitemPojo.getDetailjson())) matSpecorderitemPojo.setDetailjson("{}");
        if (matSpecorderitemPojo.getFlowcode() == null) matSpecorderitemPojo.setFlowcode("");
        if (matSpecorderitemPojo.getToolscode() == null) matSpecorderitemPojo.setToolscode("");
        if (matSpecorderitemPojo.getRemark() == null) matSpecorderitemPojo.setRemark("");
        if (matSpecorderitemPojo.getRownum() == null) matSpecorderitemPojo.setRownum(0);
        if (matSpecorderitemPojo.getCustom1() == null) matSpecorderitemPojo.setCustom1("");
        if (matSpecorderitemPojo.getCustom2() == null) matSpecorderitemPojo.setCustom2("");
        if (matSpecorderitemPojo.getCustom3() == null) matSpecorderitemPojo.setCustom3("");
        if (matSpecorderitemPojo.getCustom4() == null) matSpecorderitemPojo.setCustom4("");
        if (matSpecorderitemPojo.getCustom5() == null) matSpecorderitemPojo.setCustom5("");
        if (matSpecorderitemPojo.getCustom6() == null) matSpecorderitemPojo.setCustom6("");
        if (matSpecorderitemPojo.getCustom7() == null) matSpecorderitemPojo.setCustom7("");
        if (matSpecorderitemPojo.getCustom8() == null) matSpecorderitemPojo.setCustom8("");
        if (matSpecorderitemPojo.getCustom9() == null) matSpecorderitemPojo.setCustom9("");
        if (matSpecorderitemPojo.getCustom10() == null) matSpecorderitemPojo.setCustom10("");
        if (matSpecorderitemPojo.getTenantid() == null) matSpecorderitemPojo.setTenantid("");
        if (matSpecorderitemPojo.getRevision() == null) matSpecorderitemPojo.setRevision(0);
        return matSpecorderitemPojo;
    }
}
