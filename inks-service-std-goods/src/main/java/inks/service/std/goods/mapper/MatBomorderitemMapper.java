package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatBomorderitemEntity;
import inks.service.std.goods.domain.pojo.MatBomorderitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单Bom项目(MatBomorderitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-08 10:21:37
 */
 @Mapper
public interface MatBomorderitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomorderitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatBomorderitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatBomorderitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matBomorderitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatBomorderitemEntity matBomorderitemEntity);

    
    /**
     * 修改数据
     *
     * @param matBomorderitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatBomorderitemEntity matBomorderitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

