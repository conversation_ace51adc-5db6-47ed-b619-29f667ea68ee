package inks.service.std.goods.constant;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import inks.service.std.goods.domain.pojo.MatSpecpcbPojo;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.StringWriter;
import java.util.Map;

public class SpecVelocityUtils {

    // 创建静态的 ScriptEngineManager 和 ScriptEngine
    private static final ScriptEngine engine;

    static {
        // 初始化静态 ScriptEngine 实例
        ScriptEngineManager manager = new ScriptEngineManager();
        engine = manager.getEngineByName("JavaScript");
    }

    public static void main(String[] args) {
        // 模拟数据
        MatSpecpcbPojo matSpecpcbPojo = new MatSpecpcbPojo();
        matSpecpcbPojo.setPnlx(1450D);
        matSpecpcbPojo.setPnly(25D);
        matSpecpcbPojo.setPnl2pcs(5);
        String objJson = JSON.toJSONString(matSpecpcbPojo);

        // 模板格式
        String template = "{\"areaqty\":  \"${data.pnlx} * ${data.pnly} / ${data.pnl2pcs} / 1000000 \", \"areaunit\": \"m²\"}";

        // 调用渲染并计算方法
        String renderedResult = renderAndCalculate(template, objJson);
        System.out.println("计算vm: " + renderedResult);
    }

    /**
     * 渲染模板并计算数学表达式
     */
    public static String renderAndCalculate(String template, String msgtext) {
        System.out.println("模板: " + template);
        // 创建 VelocityContext
        VelocityContext context = new VelocityContext();
        JSONObject jsonData = JSON.parseObject(msgtext);
        context.put("data", jsonData);

        // 渲染模板
        VelocityEngine ve = new VelocityEngine();
        ve.init();
        StringWriter writer = new StringWriter();
        ve.evaluate(context, writer, "VelocityUtils", template);

        // 打印渲染后的结果
        String renderedResult = writer.toString();
        System.out.println("替换vm: " + renderedResult);

        // 解析渲染结果
        JSONObject result = JSON.parseObject(renderedResult);

        // 遍历键值对并计算数学表达式
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().toString();
            // 判断值是否是数学表达式
            if (isMathExpression(value)) {
                // 计算表达式
                double calculatedValue = evaluateExpression(value);
                result.put(key, calculatedValue);  // 更新计算结果
            }
        }

        // 返回计算后的 JSON 字符串
        return result.toString();
    }

    /**
     * 计算数学表达式
     */
    public static double evaluateExpression(String expression) {
        try {
            // 使用 ScriptEngine 计算表达式
            Object result = engine.eval(expression);
            return Double.parseDouble(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 判断字符串是否是数学表达式
     * 使用 ScriptEngine 尝试执行表达式
     */
    public static boolean isMathExpression(String value) {
        try {
            // 尝试通过 ScriptEngine 执行表达式，判断其是否有效
            engine.eval(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}


