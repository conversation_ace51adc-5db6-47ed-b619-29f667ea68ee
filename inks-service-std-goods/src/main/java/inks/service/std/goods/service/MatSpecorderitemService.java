package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecorderitemPojo;

import java.util.List;

/**
 * 工艺项目(MatSpecorderitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-16 14:43:48
 */
public interface MatSpecorderitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecorderitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecorderitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecorderitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSpecorderitemPojo 实例对象
     * @return 实例对象
     */
    MatSpecorderitemPojo insert(MatSpecorderitemPojo matSpecorderitemPojo);

    /**
     * 修改数据
     *
     * @param matSpecorderitempojo 实例对象
     * @return 实例对象
     */
    MatSpecorderitemPojo update(MatSpecorderitemPojo matSpecorderitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSpecorderitempojo 实例对象
     * @return 实例对象
     */
    MatSpecorderitemPojo clearNull(MatSpecorderitemPojo matSpecorderitempojo);
}
