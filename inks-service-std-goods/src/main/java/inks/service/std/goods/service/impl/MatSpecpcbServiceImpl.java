package inks.service.std.goods.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.WorkgroupPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.constant.SpecVelocityUtils;
import inks.service.std.goods.controller.D91M09B1Controller;
import inks.service.std.goods.domain.MatSpecpcbEntity;
import inks.service.std.goods.domain.MatSpecpcbdrawEntity;
import inks.service.std.goods.domain.MatSpecpcbdrlEntity;
import inks.service.std.goods.domain.MatSpecpcbitemEntity;
import inks.service.std.goods.domain.pojo.*;
import inks.service.std.goods.mapper.*;
import inks.service.std.goods.service.MatSpecpcbService;
import inks.service.std.goods.service.MatSpecpcbdrawService;
import inks.service.std.goods.service.MatSpecpcbdrlService;
import inks.service.std.goods.service.MatSpecpcbitemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Pcb工艺(MatSpecpcb)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-16 10:40:34
 */
@Service("matSpecpcbService")
public class MatSpecpcbServiceImpl implements MatSpecpcbService {
    @Resource
    private MatSpecpcbMapper matSpecpcbMapper;

    @Resource
    private MatSpecpcbitemMapper matSpecpcbitemMapper;

    @Resource
    private MatSpecpcbdrawMapper matSpecpcbdrawMapper;

    @Resource
    private MatSpecpcbdrlMapper matSpecpcbdrlMapper;
    @Resource
    private MatGoodsMapper matGoodsMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private MatSpecpcbitemService matSpecpcbitemService;


    /**
     * 服务对象Item
     */
    @Resource
    private MatSpecpcbdrawService matSpecpcbdrawService;


    /**
     * 服务对象Item
     */
    @Resource
    private MatSpecpcbdrlService matSpecpcbdrlService;

    @Resource
    private GraphicsPcbService graphicsPcbService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecpcbPojo getEntity(String key, String tid) {
        return this.matSpecpcbMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecpcbitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecpcbitemdetailPojo> lst = matSpecpcbMapper.getPageList(queryParam);
            PageInfo<MatSpecpcbitemdetailPojo> pageInfo = new PageInfo<MatSpecpcbitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecpcbPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatSpecpcbPojo matSpecpcbPojo = this.matSpecpcbMapper.getEntity(key, tid);
            //读取子表
            matSpecpcbPojo.setItem(matSpecpcbitemMapper.getList(matSpecpcbPojo.getId(), matSpecpcbPojo.getTenantid()));
            //读取子表
            matSpecpcbPojo.setDraw(matSpecpcbdrawMapper.getList(matSpecpcbPojo.getId(), matSpecpcbPojo.getTenantid()));

            //读取子表
            matSpecpcbPojo.setDrl(matSpecpcbdrlMapper.getList(matSpecpcbPojo.getId(), matSpecpcbPojo.getTenantid()));

            return matSpecpcbPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecpcbPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecpcbPojo> lst = matSpecpcbMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matSpecpcbitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setDraw(matSpecpcbdrawMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }

            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setDrl(matSpecpcbdrlMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatSpecpcbPojo> pageInfo = new PageInfo<MatSpecpcbPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecpcbPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecpcbPojo> lst = matSpecpcbMapper.getPageTh(queryParam);
            PageInfo<MatSpecpcbPojo> pageInfo = new PageInfo<MatSpecpcbPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matSpecpcbPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecpcbPojo insert(MatSpecpcbPojo matSpecpcbPojo, String misizetoareaqty) {
        String tid = matSpecpcbPojo.getTenantid();
//初始化NULL字段
        if (matSpecpcbPojo.getRefno() == null) matSpecpcbPojo.setRefno("");
        if (matSpecpcbPojo.getBilldate() == null) matSpecpcbPojo.setBilldate(new Date());
        if (matSpecpcbPojo.getBilltype() == null) matSpecpcbPojo.setBilltype("");
        if (matSpecpcbPojo.getBilltitle() == null) matSpecpcbPojo.setBilltitle("");
        if (matSpecpcbPojo.getGoodsid() == null) matSpecpcbPojo.setGoodsid("");
        if (matSpecpcbPojo.getItemcode() == null) matSpecpcbPojo.setItemcode("");
        if (matSpecpcbPojo.getItemname() == null) matSpecpcbPojo.setItemname("");
        if (matSpecpcbPojo.getItemspec() == null) matSpecpcbPojo.setItemspec("");
        if (matSpecpcbPojo.getItemunit() == null) matSpecpcbPojo.setItemunit("");
        if (matSpecpcbPojo.getVersionnum() == null) matSpecpcbPojo.setVersionnum("");
        if (matSpecpcbPojo.getGoodsclass() == null) matSpecpcbPojo.setGoodsclass("");
        if (matSpecpcbPojo.getMaterial() == null) matSpecpcbPojo.setMaterial("");
        if (matSpecpcbPojo.getSurface() == null) matSpecpcbPojo.setSurface("");
        if (matSpecpcbPojo.getGroupid() == null) matSpecpcbPojo.setGroupid("");
        if (matSpecpcbPojo.getPcsx() == null) matSpecpcbPojo.setPcsx(0D);
        if (matSpecpcbPojo.getPcsy() == null) matSpecpcbPojo.setPcsy(0D);
        if (matSpecpcbPojo.getSizeunit() == null) matSpecpcbPojo.setSizeunit("");
        if (matSpecpcbPojo.getSetx() == null) matSpecpcbPojo.setSetx(0D);
        if (matSpecpcbPojo.getSety() == null) matSpecpcbPojo.setSety(0D);
        if (matSpecpcbPojo.getSet2pcs() == null) matSpecpcbPojo.setSet2pcs(0);
        if (matSpecpcbPojo.getAllowng() == null) matSpecpcbPojo.setAllowng(0);
        if (matSpecpcbPojo.getPnlx() == null) matSpecpcbPojo.setPnlx(0D);
        if (matSpecpcbPojo.getPnly() == null) matSpecpcbPojo.setPnly(0D);
        if (matSpecpcbPojo.getPnl2pcs() == null) matSpecpcbPojo.setPnl2pcs(0);
        if (matSpecpcbPojo.getPnlbx() == null) matSpecpcbPojo.setPnlbx(0D);
        if (matSpecpcbPojo.getPnlby() == null) matSpecpcbPojo.setPnlby(0D);
        if (matSpecpcbPojo.getPnlb2pcs() == null) matSpecpcbPojo.setPnlb2pcs(0);
        if (matSpecpcbPojo.getPnlcx() == null) matSpecpcbPojo.setPnlcx(0D);
        if (matSpecpcbPojo.getPnlcy() == null) matSpecpcbPojo.setPnlcy(0D);
        if (matSpecpcbPojo.getPnlc2pcs() == null) matSpecpcbPojo.setPnlc2pcs(0);
        if (matSpecpcbPojo.getPnldx() == null) matSpecpcbPojo.setPnldx(0D);
        if (matSpecpcbPojo.getPnldy() == null) matSpecpcbPojo.setPnldy(0D);
        if (matSpecpcbPojo.getPnld2pcs() == null) matSpecpcbPojo.setPnld2pcs(0);
        if (matSpecpcbPojo.getOperator() == null) matSpecpcbPojo.setOperator("");
        if (matSpecpcbPojo.getParentid() == null) matSpecpcbPojo.setParentid(0);
        if (matSpecpcbPojo.getLayernum() == null) matSpecpcbPojo.setLayernum(0);
        if (matSpecpcbPojo.getMachtype() == null) matSpecpcbPojo.setMachtype("");
        if (matSpecpcbPojo.getMachclass() == null) matSpecpcbPojo.setMachclass("");
        if (matSpecpcbPojo.getEnabledmark() == null) matSpecpcbPojo.setEnabledmark(0);
        if (matSpecpcbPojo.getStatecode() == null) matSpecpcbPojo.setStatecode("");
        if (matSpecpcbPojo.getTechnics() == null) matSpecpcbPojo.setTechnics("");
        if (matSpecpcbPojo.getMat2pcs() == null) matSpecpcbPojo.setMat2pcs(0);
        if (matSpecpcbPojo.getMatthick() == null) matSpecpcbPojo.setMatthick("");
        if (matSpecpcbPojo.getMatcuthick() == null) matSpecpcbPojo.setMatcuthick("");
        if (matSpecpcbPojo.getMatname() == null) matSpecpcbPojo.setMatname("");
        if (matSpecpcbPojo.getMatcode() == null) matSpecpcbPojo.setMatcode("");
        if (matSpecpcbPojo.getMatfactory() == null) matSpecpcbPojo.setMatfactory("");
        if (matSpecpcbPojo.getMatcolor() == null) matSpecpcbPojo.setMatcolor("");
        if (matSpecpcbPojo.getProductcode() == null) matSpecpcbPojo.setProductcode("");
        if (matSpecpcbPojo.getProductthick() == null) matSpecpcbPojo.setProductthick("");
        if (matSpecpcbPojo.getProductcuthick() == null) matSpecpcbPojo.setProductcuthick("");
        if (matSpecpcbPojo.getProductweight() == null) matSpecpcbPojo.setProductweight(0D);
        if (matSpecpcbPojo.getPrintlayer() == null) matSpecpcbPojo.setPrintlayer(0);
        if (matSpecpcbPojo.getPnluserate() == null) matSpecpcbPojo.setPnluserate(0D);
        if (matSpecpcbPojo.getCutuserate() == null) matSpecpcbPojo.setCutuserate(0D);
        if (matSpecpcbPojo.getSummary() == null) matSpecpcbPojo.setSummary("");
        if (matSpecpcbPojo.getCreateby() == null) matSpecpcbPojo.setCreateby("");
        if (matSpecpcbPojo.getCreatebyid() == null) matSpecpcbPojo.setCreatebyid("");
        if (matSpecpcbPojo.getCreatedate() == null) matSpecpcbPojo.setCreatedate(new Date());
        if (matSpecpcbPojo.getLister() == null) matSpecpcbPojo.setLister("");
        if (matSpecpcbPojo.getListerid() == null) matSpecpcbPojo.setListerid("");
        if (matSpecpcbPojo.getModifydate() == null) matSpecpcbPojo.setModifydate(new Date());
        if (matSpecpcbPojo.getAssessor() == null) matSpecpcbPojo.setAssessor("");
        if (matSpecpcbPojo.getAssessorid() == null) matSpecpcbPojo.setAssessorid("");
        if (matSpecpcbPojo.getAssessdate() == null) matSpecpcbPojo.setAssessdate(new Date());
        if (matSpecpcbPojo.getCustom1() == null) matSpecpcbPojo.setCustom1("");
        if (matSpecpcbPojo.getCustom2() == null) matSpecpcbPojo.setCustom2("");
        if (matSpecpcbPojo.getCustom3() == null) matSpecpcbPojo.setCustom3("");
        if (matSpecpcbPojo.getCustom4() == null) matSpecpcbPojo.setCustom4("");
        if (matSpecpcbPojo.getCustom5() == null) matSpecpcbPojo.setCustom5("");
        if (matSpecpcbPojo.getCustom6() == null) matSpecpcbPojo.setCustom6("");
        if (matSpecpcbPojo.getCustom7() == null) matSpecpcbPojo.setCustom7("");
        if (matSpecpcbPojo.getCustom8() == null) matSpecpcbPojo.setCustom8("");
        if (matSpecpcbPojo.getCustom9() == null) matSpecpcbPojo.setCustom9("");
        if (matSpecpcbPojo.getCustom10() == null) matSpecpcbPojo.setCustom10("");
        if (tid == null) matSpecpcbPojo.setTenantid("");
        if (matSpecpcbPojo.getTenantname() == null) matSpecpcbPojo.setTenantname("");
        if (matSpecpcbPojo.getRevision() == null) matSpecpcbPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatSpecpcbEntity matSpecpcbEntity = new MatSpecpcbEntity();
        BeanUtils.copyProperties(matSpecpcbPojo, matSpecpcbEntity);
        //设置id和新建日期
        matSpecpcbEntity.setId(id);
        matSpecpcbEntity.setRevision(1);  //乐观锁
        matSpecpcbPojo.setId(id);
        //插入主表
        this.matSpecpcbMapper.insert(matSpecpcbEntity);
        //Item子表处理
        List<MatSpecpcbitemPojo> lst = matSpecpcbPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (MatSpecpcbitemPojo matSpecpcbitemPojo : lst) {
                //初始化item的NULL
                MatSpecpcbitemPojo itemPojo = this.matSpecpcbitemService.clearNull(matSpecpcbitemPojo);
                MatSpecpcbitemEntity matSpecpcbitemEntity = new MatSpecpcbitemEntity();
                BeanUtils.copyProperties(itemPojo, matSpecpcbitemEntity);
                //设置id和Pid
                matSpecpcbitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matSpecpcbitemEntity.setPid(id);
                matSpecpcbitemEntity.setTenantid(tid);
                matSpecpcbitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matSpecpcbitemMapper.insert(matSpecpcbitemEntity);
            }
        }

        //Draw子表处理
        List<MatSpecpcbdrawPojo> lstdraw = matSpecpcbPojo.getDraw();
        if (lstdraw != null) {
            //循环每个item子表
            for (int i = 0; i < lstdraw.size(); i++) {
                //初始化item的NULL
                MatSpecpcbdrawPojo drawPojo = this.matSpecpcbdrawService.clearNull(lstdraw.get(i));
                MatSpecpcbdrawEntity matSpecpcbdrawEntity = new MatSpecpcbdrawEntity();
                BeanUtils.copyProperties(drawPojo, matSpecpcbdrawEntity);
                //设置id和Pid
                matSpecpcbdrawEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matSpecpcbdrawEntity.setPid(id);
                matSpecpcbdrawEntity.setTenantid(tid);
                matSpecpcbdrawEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matSpecpcbdrawMapper.insert(matSpecpcbdrawEntity);
            }
        }


        //Draw子表处理
        List<MatSpecpcbdrlPojo> lstdrl = matSpecpcbPojo.getDrl();
        if (lstdrl != null) {
            //循环每个item子表
            for (MatSpecpcbdrlPojo matSpecpcbdrlPojo : lstdrl) {
                //初始化item的NULL
                MatSpecpcbdrlPojo drlPojo = this.matSpecpcbdrlService.clearNull(matSpecpcbdrlPojo);
                MatSpecpcbdrlEntity matSpecpcbdrlEntity = new MatSpecpcbdrlEntity();
                BeanUtils.copyProperties(drlPojo, matSpecpcbdrlEntity);
                //设置id和Pid
                matSpecpcbdrlEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matSpecpcbdrlEntity.setPid(id);
                matSpecpcbdrlEntity.setTenantid(tid);
                matSpecpcbdrlEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matSpecpcbdrlMapper.insert(matSpecpcbdrlEntity);
            }
        }


        // 读取指定系统参数module.goods.misizetoareaqty 示例：{"areaqty":  "${data.pnlx} * ${data.pnly} / ${data.pnl2pcs} / 1000000 / ${data.printlayer}", "areaunit": "m²"}
        // 用于：mi尺寸同步货品表的多个字段 areaqty，areaunit
        if (StringUtils.isNotBlank(misizetoareaqty)) {
            // 调用渲染vm并计算方法
            String renderedResult = SpecVelocityUtils.renderAndCalculate(misizetoareaqty, JSON.toJSONString(matSpecpcbPojo));
            System.out.println("计算vm: " + renderedResult);
            // 解析结果
            JSONObject jsonObject = JSON.parseObject(renderedResult);
            // 同步货品 TODO 按照jsonObject.getDouble("XXX")同步更多字段
            matSpecpcbPojo.setAreaqty(jsonObject.getDouble("areaqty"));
            matSpecpcbPojo.setAreaunit(jsonObject.getString("areaunit"));
        }
        // 将Mat_SpecPcb.id【和尺寸字段PcsX、PcsY、SizeUnit、SetX、SetY、Set2Pcs、PnlX、PnlY、Pnl2Pcs】反写到Mat_Goods.Specid
        // 20241220加入areaqty、areaunit字段
        this.matGoodsMapper.updateGoodsSpecidAndPcsX(matSpecpcbPojo);
        //返回Bill实例
        return this.getBillEntity(matSpecpcbEntity.getId(), matSpecpcbEntity.getTenantid());

    }


    // 查询全表的 matSpecpcb，公共公式刷新Mat_Goods表面积和单位areaqty、areaunit
    public void upall() {
        String misizetoareaqty = "#set($effectivePrintLayer = $data.printlayer)  #if($data.printlayer == 0)     #set($effectivePrintLayer = 1) #end {\"areaqty\":  \"${data.pnlx} * ${data.pnly} / ${data.pnl2pcs} / 1000000 / $effectivePrintLayer\", \"areaunit\": \"m²\"}";
        List<MatSpecpcbPojo> list = matSpecpcbMapper.getList();
        int batchSize = 500;
        int total = list.size();

        for (int i = 0; i < total; i += batchSize) {
            int end = Math.min(i + batchSize, total);
            List<MatSpecpcbPojo> batch = list.subList(i, end);

            for (MatSpecpcbPojo matSpecpcbPojo : batch) {
                if (StringUtils.isNotBlank(misizetoareaqty)) {
                    String renderedResult = SpecVelocityUtils.renderAndCalculate(misizetoareaqty, JSON.toJSONString(matSpecpcbPojo));
                    JSONObject jsonObject = JSON.parseObject(renderedResult);
                    matSpecpcbPojo.setAreaqty(jsonObject.getDouble("areaqty"));
                    matSpecpcbPojo.setAreaunit(jsonObject.getString("areaunit"));
                }
                matGoodsMapper.updateGoodsSpecidAndPcsX(matSpecpcbPojo);
            }
            // 打印当前进度
            System.out.printf("已处理：%d/%d%n", end, total);
        }
    }

    /**
     * 修改数据
     *
     * @param matSpecpcbPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecpcbPojo update(MatSpecpcbPojo matSpecpcbPojo, String misizetoareaqty) {
        //主表更改
        MatSpecpcbEntity matSpecpcbEntity = new MatSpecpcbEntity();
        BeanUtils.copyProperties(matSpecpcbPojo, matSpecpcbEntity);
        this.matSpecpcbMapper.update(matSpecpcbEntity);
        // Item
        if (matSpecpcbPojo.getItem() != null) {
            //Item子表处理
            List<MatSpecpcbitemPojo> lst = matSpecpcbPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = matSpecpcbMapper.getDelItemIds(matSpecpcbPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.matSpecpcbitemMapper.delete(lstDelId, matSpecpcbEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (MatSpecpcbitemPojo matSpecpcbitemPojo : lst) {
                    MatSpecpcbitemEntity matSpecpcbitemEntity = new MatSpecpcbitemEntity();
                    if (matSpecpcbitemPojo.getId() == "" || matSpecpcbitemPojo.getId() == null) {
                        //初始化item的NULL
                        MatSpecpcbitemPojo itemPojo = this.matSpecpcbitemService.clearNull(matSpecpcbitemPojo);
                        BeanUtils.copyProperties(itemPojo, matSpecpcbitemEntity);
                        //设置id和Pid
                        matSpecpcbitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matSpecpcbitemEntity.setPid(matSpecpcbEntity.getId());  // 主表 id
                        matSpecpcbitemEntity.setTenantid(matSpecpcbPojo.getTenantid());   // 租户id
                        matSpecpcbitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matSpecpcbitemMapper.insert(matSpecpcbitemEntity);
                    } else {
                        BeanUtils.copyProperties(matSpecpcbitemPojo, matSpecpcbitemEntity);
                        matSpecpcbitemEntity.setTenantid(matSpecpcbPojo.getTenantid());
                        this.matSpecpcbitemMapper.update(matSpecpcbitemEntity);
                    }
                }
            }
        }


        // Draw
        if (matSpecpcbPojo.getDraw() != null) {
            //Item子表处理
            List<MatSpecpcbdrawPojo> lst = matSpecpcbPojo.getDraw();
            //获取被删除的Item
            List<String> lstDelIds = matSpecpcbMapper.getDelDrawIds(matSpecpcbPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.matSpecpcbdrawMapper.delete(lstDelId, matSpecpcbEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个draw子表
                for (MatSpecpcbdrawPojo matSpecpcbdrawPojo : lst) {
                    MatSpecpcbdrawEntity matSpecpcbdrawEntity = new MatSpecpcbdrawEntity();
                    if (Objects.equals(matSpecpcbdrawPojo.getId(), "") || matSpecpcbdrawPojo.getId() == null) {
                        //初始化draw的NULL
                        MatSpecpcbdrawPojo drawPojo = this.matSpecpcbdrawService.clearNull(matSpecpcbdrawPojo);
                        BeanUtils.copyProperties(drawPojo, matSpecpcbdrawEntity);
                        //设置id和Pid
                        matSpecpcbdrawEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // draw id
                        matSpecpcbdrawEntity.setPid(matSpecpcbEntity.getId());  // 主表 id
                        matSpecpcbdrawEntity.setTenantid(matSpecpcbPojo.getTenantid());   // 租户id
                        matSpecpcbdrawEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matSpecpcbdrawMapper.insert(matSpecpcbdrawEntity);
                    } else {
                        BeanUtils.copyProperties(matSpecpcbdrawPojo, matSpecpcbdrawEntity);
                        matSpecpcbdrawEntity.setTenantid(matSpecpcbPojo.getTenantid());
                        this.matSpecpcbdrawMapper.update(matSpecpcbdrawEntity);
                    }
                }
            }
        }


        // Draw
        if (matSpecpcbPojo.getDrl() != null) {
            //Item子表处理
            List<MatSpecpcbdrlPojo> lst = matSpecpcbPojo.getDrl();
            //获取被删除的Item
            List<String> lstDelIds = matSpecpcbMapper.getDelDrlIds(matSpecpcbPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.matSpecpcbdrlMapper.delete(lstDelId, matSpecpcbEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个drl子表
                for (MatSpecpcbdrlPojo matSpecpcbdrlPojo : lst) {
                    MatSpecpcbdrlEntity matSpecpcbdrlEntity = new MatSpecpcbdrlEntity();
                    if (Objects.equals(matSpecpcbdrlPojo.getId(), "") || matSpecpcbdrlPojo.getId() == null) {
                        //初始化drl的NULL
                        MatSpecpcbdrlPojo drlPojo = this.matSpecpcbdrlService.clearNull(matSpecpcbdrlPojo);
                        BeanUtils.copyProperties(drlPojo, matSpecpcbdrlEntity);
                        //设置id和Pid
                        matSpecpcbdrlEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // drl id
                        matSpecpcbdrlEntity.setPid(matSpecpcbEntity.getId());  // 主表 id
                        matSpecpcbdrlEntity.setTenantid(matSpecpcbPojo.getTenantid());   // 租户id
                        matSpecpcbdrlEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matSpecpcbdrlMapper.insert(matSpecpcbdrlEntity);
                    } else {
                        BeanUtils.copyProperties(matSpecpcbdrlPojo, matSpecpcbdrlEntity);
                        matSpecpcbdrlEntity.setTenantid(matSpecpcbPojo.getTenantid());
                        this.matSpecpcbdrlMapper.update(matSpecpcbdrlEntity);
                    }
                }
            }
        }

        // 读取指定系统参数module.goods.misizetoareaqty 示例：{"areaqty":  "${data.pnlx} * ${data.pnly} / ${data.pnl2pcs} / 1000000 / ${data.printlayer}", "areaunit": "m²"}
        // 用于：mi尺寸同步货品表的多个字段 areaqty，areaunit
        if (StringUtils.isNotBlank(misizetoareaqty)) {
            // 调用渲染vm并计算方法
            String renderedResult = SpecVelocityUtils.renderAndCalculate(misizetoareaqty, JSON.toJSONString(matSpecpcbPojo));
            System.out.println("计算vm: " + renderedResult);
            // 解析结果
            JSONObject jsonObject = JSON.parseObject(renderedResult);
            // 同步货品 TODO 按照jsonObject.getDouble("XXX")同步更多字段
            matSpecpcbPojo.setAreaqty(jsonObject.getDouble("areaqty"));
            matSpecpcbPojo.setAreaunit(jsonObject.getString("areaunit"));
        }
        // 将Mat_SpecPcb.id【和尺寸字段PcsX、PcsY、SizeUnit、SetX、SetY、Set2Pcs、PnlX、PnlY、Pnl2Pcs】反写到Mat_Goods.Specid
        // 20241220加入areaqty、areaunit字段
        this.matGoodsMapper.updateGoodsSpecidAndPcsX(matSpecpcbPojo);

        //返回Bill实例
        return this.getBillEntity(matSpecpcbEntity.getId(), matSpecpcbEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        MatSpecpcbPojo matSpecpcbPojo = this.getBillEntity(key, tid);
        //Item子表处理
        if (matSpecpcbPojo.getItem() != null) {
            List<MatSpecpcbitemPojo> lst = matSpecpcbPojo.getItem();
            if (lst != null) {
                //循环每个删除item子表
                for (MatSpecpcbitemPojo matSpecpcbitemPojo : lst) {
                    this.matSpecpcbitemMapper.delete(matSpecpcbitemPojo.getId(), tid);
                }
            }
        }

        //Item子表处理
        if (matSpecpcbPojo.getDraw() != null) {
            List<MatSpecpcbdrawPojo> lst = matSpecpcbPojo.getDraw();
            if (lst != null) {
                //循环每个删除item子表
                for (MatSpecpcbdrawPojo matSpecpcbdrawPojo : lst) {
                    this.matSpecpcbdrawMapper.delete(matSpecpcbdrawPojo.getId(), tid);
                }
            }
        }

        //Item子表处理
        if (matSpecpcbPojo.getDrl() != null) {
            List<MatSpecpcbdrlPojo> lst = matSpecpcbPojo.getDrl();
            if (lst != null) {
                //循环每个删除item子表
                for (MatSpecpcbdrlPojo matSpecpcbdrlPojo : lst) {
                    this.matSpecpcbdrlMapper.delete(matSpecpcbdrlPojo.getId(), tid);
                }
            }
        }

        // 将Mat_SpecPcb.id【和尺寸字段PcsX、PcsY、SizeUnit、SetX、SetY、Set2Pcs、PnlX、PnlY、Pnl2Pcs】反写到Mat_Goods.Specid
        // 20241220加入areaqty、areaunit字段
        MatSpecpcbPojo specpcbPojo = new MatSpecpcbPojo();
        specpcbPojo.setTenantid(tid);
        specpcbPojo.setGoodsid(matSpecpcbPojo.getGoodsid());
        specpcbPojo.setId("");
        specpcbPojo.setPcsx(0d);
        specpcbPojo.setPcsy(0d);
        specpcbPojo.setSizeunit("");
        specpcbPojo.setSetx(0d);
        specpcbPojo.setSety(0d);
        specpcbPojo.setSet2pcs(0);
        specpcbPojo.setPnlx(0d);
        specpcbPojo.setPnly(0d);
        specpcbPojo.setPnl2pcs(0);
        specpcbPojo.setAreaqty(0d);
        specpcbPojo.setAreaunit("");
        this.matGoodsMapper.updateGoodsSpecidAndPcsX(specpcbPojo);
        this.matSpecpcbMapper.delete(key, tid);
        return matSpecpcbPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param matSpecpcbPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecpcbPojo approval(MatSpecpcbPojo matSpecpcbPojo) {
        //主表更改
        MatSpecpcbEntity matSpecpcbEntity = new MatSpecpcbEntity();
        BeanUtils.copyProperties(matSpecpcbPojo, matSpecpcbEntity);
        this.matSpecpcbMapper.approval(matSpecpcbEntity);
        //返回Bill实例
        return this.getBillEntity(matSpecpcbEntity.getId(), matSpecpcbEntity.getTenantid());
    }

    @Override
    public int checkGoodsid(String goodsid, String tenantid) {
        return this.matSpecpcbMapper.checkGoodsid(goodsid, tenantid);
    }

    /**
     * 解析XML内容并转换为MatSpecpcbPojo对象
     */
    public MatSpecpcbPojo parseXmlToMatSpecpcbPojo(String xmlContent, LoginUser loginUser) throws ParserConfigurationException, IOException, SAXException {
        String tid = loginUser.getTenantid();
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new InputSource(new StringReader(xmlContent)));

        MatSpecpcbPojo matSpecpcbPojo = new MatSpecpcbPojo();

        // 解析主表数据 Eng_Spec
        NodeList engSpecNodes = document.getElementsByTagName("Eng_Spec");
        if (engSpecNodes.getLength() > 0) {
            Element engSpecElement = (Element) engSpecNodes.item(0);
            mapEngSpecToMatSpecpcbPojo(engSpecElement, matSpecpcbPojo, tid);
        }

        // 解析工序项数据 Eng_SpecItem
        List<MatSpecpcbitemPojo> itemList = new ArrayList<>();
        NodeList engSpecItemNodes = document.getElementsByTagName("Eng_SpecItem");
        for (int i = 0; i < engSpecItemNodes.getLength(); i++) {
            Element itemElement = (Element) engSpecItemNodes.item(i);
            MatSpecpcbitemPojo itemPojo = mapEngSpecItemToMatSpecpcbitemPojo(itemElement, tid);
            itemList.add(itemPojo);
        }
        matSpecpcbPojo.setItem(itemList);

        // 解析钻孔数据 Eng_SpecDrl
        List<MatSpecpcbdrlPojo> drlList = new ArrayList<>();
        NodeList engSpecDrlNodes = document.getElementsByTagName("Eng_SpecDrl");
        for (int i = 0; i < engSpecDrlNodes.getLength(); i++) {
            Element drlElement = (Element) engSpecDrlNodes.item(i);
            MatSpecpcbdrlPojo drlPojo = mapEngSpecDrlToMatSpecpcbdrlPojo(drlElement, tid);
            drlList.add(drlPojo);
        }
        matSpecpcbPojo.setDrl(drlList);

        // 解析图片数据 Eng_SpecPhoto
        List<MatSpecpcbdrawPojo> drawList = new ArrayList<>();
        NodeList engSpecPhotoNodes = document.getElementsByTagName("Eng_SpecPhoto");
        for (int i = 0; i < engSpecPhotoNodes.getLength(); i++) {
            Element photoElement = (Element) engSpecPhotoNodes.item(i);
            MatSpecpcbdrawPojo drawPojo = mapEngSpecPhotoToMatSpecpcbdrawPojo(photoElement, tid);
            drawList.add(drawPojo);
        }
        matSpecpcbPojo.setDraw(drawList);

        return matSpecpcbPojo;
    }

    /**
     * 映射Eng_Spec到MatSpecpcbPojo主表字段
     */
    private void mapEngSpecToMatSpecpcbPojo(Element engSpecElement, MatSpecpcbPojo pojo, String tid) {
        try {

            // 基本货品信息映射  查询Goodsid By GoodsUid
            pojo.setId(getElementTextContent(engSpecElement, "id"));
            pojo.setItemcode(getElementTextContent(engSpecElement, "GoodsUid"));
            //pojo.setItemname(getElementTextContent(engSpecElement, "GoodsName"));
            //pojo.setItemspec(getElementTextContent(engSpecElement, "GoodsSpec"));
            //pojo.setItemunit(getElementTextContent(engSpecElement, "GoodsUnit"));
            //String goodsid = getElementTextContent(engSpecElement, "Goodsid");
            MatGoodsPojo goodsDB = matGoodsMapper.getEntityByGoodsUid(pojo.getItemcode(), tid);
            if (goodsDB != null) {
                pojo.setGoodsid(goodsDB.getId());
                pojo.setItemname(goodsDB.getGoodsname());
                pojo.setItemspec(goodsDB.getGoodsspec());
                pojo.setItemunit(goodsDB.getGoodsunit());
            } else {
                throw new RuntimeException("未找到对应的货品信息，请检查GoodsUid: " + pojo.getItemcode());
            }
            // 配对Groupid (使用货品表的默认Groupid)
            String groupid = goodsDB.getGroupid();
            if (StringUtils.isNotBlank(groupid)) {
                pojo.setGroupid(groupid);
                WorkgroupPojo groupInfo = matSpecpcbMapper.getGroupInfo(groupid, tid);
                if (groupInfo != null) {
                    pojo.setGroupname(groupInfo.getGroupname());
                    pojo.setGroupuid(groupInfo.getGroupuid());
                    pojo.setAbbreviate(groupInfo.getAbbreviate());
                } else {
                    throw new RuntimeException("未找到对应的客户，请检查groupid: " + groupid);
                }
            }
            pojo.setVersionnum(getElementTextContent(engSpecElement, "EditionID"));

            // 尺寸信息
            String pcsX = getElementTextContent(engSpecElement, "PcsX");
            String pcsY = getElementTextContent(engSpecElement, "PcsY");
            if (StringUtils.isNotBlank(pcsX)) {
                pojo.setPcsx(Double.parseDouble(pcsX));
            }
            if (StringUtils.isNotBlank(pcsY)) {
                pojo.setPcsy(Double.parseDouble(pcsY));
            }

            String setX = getElementTextContent(engSpecElement, "SetX");
            String setY = getElementTextContent(engSpecElement, "SetY");
            if (StringUtils.isNotBlank(setX)) {
                pojo.setSetx(Double.parseDouble(setX));
            }
            if (StringUtils.isNotBlank(setY)) {
                pojo.setSety(Double.parseDouble(setY));
            }

            String pnlX = getElementTextContent(engSpecElement, "PnlX");
            String pnlY = getElementTextContent(engSpecElement, "PnlY");
            if (StringUtils.isNotBlank(pnlX)) {
                pojo.setPnlx(Double.parseDouble(pnlX));
            }
            if (StringUtils.isNotBlank(pnlY)) {
                pojo.setPnly(Double.parseDouble(pnlY));
            }

            // 数量信息
            String set2Pcs = getElementTextContent(engSpecElement, "Set2Pcs");
            String pnl2Pcs = getElementTextContent(engSpecElement, "Pnl2Pcs");
            if (StringUtils.isNotBlank(set2Pcs)) {
                pojo.setSet2pcs(Integer.parseInt(set2Pcs));
            }
            if (StringUtils.isNotBlank(pnl2Pcs)) {
                pojo.setPnl2pcs(Integer.parseInt(pnl2Pcs));
            }

            // 材料信息
            pojo.setMaterial(getElementTextContent(engSpecElement, "Material"));
            pojo.setSurface(getElementTextContent(engSpecElement, "Surface"));
            pojo.setProductthick(getElementTextContent(engSpecElement, "ProductThick"));

            // 利用率
            String pnlUseRate = getElementTextContent(engSpecElement, "PnlUseRate");
            String cutUseRate = getElementTextContent(engSpecElement, "CutUseRate");
            if (StringUtils.isNotBlank(pnlUseRate)) {
                pojo.setPnluserate(Double.parseDouble(pnlUseRate));
            }
            if (StringUtils.isNotBlank(cutUseRate)) {
                pojo.setCutuserate(Double.parseDouble(cutUseRate));
            }

            // 日期信息
            String createDate = getElementTextContent(engSpecElement, "CreateDate");
            String modifyDate = getElementTextContent(engSpecElement, "ModifyDate");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
            if (StringUtils.isNotBlank(createDate)) {
                pojo.setCreatedate(sdf.parse(createDate));
            }
            if (StringUtils.isNotBlank(modifyDate)) {
                pojo.setModifydate(sdf.parse(modifyDate));
            }

            // 其他信息
            pojo.setLister(getElementTextContent(engSpecElement, "Lister"));
            pojo.setAssessor(getElementTextContent(engSpecElement, "Assessor"));
            pojo.setOperator(getElementTextContent(engSpecElement, "EngCam"));
            pojo.setMachclass(getElementTextContent(engSpecElement, "MachClass"));
            pojo.setStatecode(getElementTextContent(engSpecElement, "StateText"));


            // 补充GoodsClass字段映射 (按SQL视图: GoodsClassID AS GoodsClass)
            pojo.setGoodsclass(getElementTextContent(engSpecElement, "GoodsClassID"));

            // 补充Operator字段映射 (按SQL视图: EngCam AS Operator)
            pojo.setOperator(getElementTextContent(engSpecElement, "EngCam"));

            // 补充LayerNum字段映射 (按SQL视图: Layer AS LayerNum)
            String layer = getElementTextContent(engSpecElement, "Layer");
            if (StringUtils.isNotBlank(layer)) {
                pojo.setLayernum(Integer.parseInt(layer));
            }

            // 补充Parentid字段映射
            String parentid = getElementTextContent(engSpecElement, "Parentid");
            if (StringUtils.isNotBlank(parentid)) {
                pojo.setParentid(Integer.parseInt(parentid));
            }

            // 补充其他字段映射
            pojo.setMachtype(getElementTextContent(engSpecElement, "MachType"));
            pojo.setTechnics(getElementTextContent(engSpecElement, "Technics"));
            pojo.setSummary(getElementTextContent(engSpecElement, "Summary"));

            // 补充材料相关字段
            String mat2Pcs = getElementTextContent(engSpecElement, "Mat2Pcs");
            if (StringUtils.isNotBlank(mat2Pcs)) {
                pojo.setMat2pcs(Integer.parseInt(mat2Pcs));
            }
            pojo.setMatthick(getElementTextContent(engSpecElement, "MatThick"));
            pojo.setMatcuthick(getElementTextContent(engSpecElement, "MatCuThick"));
            pojo.setMatname(getElementTextContent(engSpecElement, "MatName"));
            pojo.setMatcode(getElementTextContent(engSpecElement, "MatCode"));
            pojo.setMatfactory(getElementTextContent(engSpecElement, "MatFactory"));
            pojo.setMatcolor(getElementTextContent(engSpecElement, "MatColor"));

            // 补充产品相关字段
            pojo.setProductcode(getElementTextContent(engSpecElement, "ProductCode"));
            pojo.setProductcuthick(getElementTextContent(engSpecElement, "ProductCuThick"));
            String productWeight = getElementTextContent(engSpecElement, "ProductWeight");
            if (StringUtils.isNotBlank(productWeight)) {
                pojo.setProductweight(Double.parseDouble(productWeight));
            }

            // 补充打印层数
            String printLayer = getElementTextContent(engSpecElement, "PrintLayer");
            if (StringUtils.isNotBlank(printLayer)) {
                pojo.setPrintlayer(Integer.parseInt(printLayer));
            }

            // 补充尺寸单位和允许NG
            pojo.setSizeunit(getElementTextContent(engSpecElement, "SizeUnit"));
            String allowNg = getElementTextContent(engSpecElement, "AllowNg");
            if (StringUtils.isNotBlank(allowNg)) {
                pojo.setAllowng(Integer.parseInt(allowNg));
            }

            // 补充自定义字段
            pojo.setCustom1(getElementTextContent(engSpecElement, "Custom1"));
            pojo.setCustom2(getElementTextContent(engSpecElement, "Custom2"));
            pojo.setCustom3(getElementTextContent(engSpecElement, "Custom3"));
            pojo.setCustom4(getElementTextContent(engSpecElement, "Custom4"));
            pojo.setCustom5(getElementTextContent(engSpecElement, "Custom5"));
            pojo.setCustom6(getElementTextContent(engSpecElement, "Custom6"));
        } catch (Exception e) {
            throw new RuntimeException("映射Eng_Spec数据时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 映射Eng_SpecItem到MatSpecpcbitemPojo
     */
    private MatSpecpcbitemPojo mapEngSpecItemToMatSpecpcbitemPojo(Element itemElement, String tid) {
        MatSpecpcbitemPojo itemPojo = new MatSpecpcbitemPojo();

        try {
            itemPojo.setId(getElementTextContent(itemElement, "id"));
            itemPojo.setPid(getElementTextContent(itemElement, "Especid"));
            // 查询工序id By WpName
            itemPojo.setWpcode(getElementTextContent(itemElement, "WpUid"));
            itemPojo.setWpname(getElementTextContent(itemElement, "WpName"));
            //itemPojo.setWpid(getElementTextContent(itemElement, "Wpid"));
            String wpid = matSpecpcbitemMapper.getWpidByWpNameIgnoreCase(itemPojo.getWpname(), tid);
            if (StringUtils.isNotBlank(wpid)) {
                itemPojo.setWpid(wpid);
            } else {
                throw new RuntimeException("未找到工序，请检查工序名称: " + itemPojo.getWpname());
            }
            itemPojo.setDescription(getElementTextContent(itemElement, "Indiceate"));
            itemPojo.setDetailjson(getElementTextContent(itemElement, "JsonStr"));
            String rowid = getElementTextContent(itemElement, "Rowid");
            if (StringUtils.isNotBlank(rowid)) {
                itemPojo.setRownum(Integer.parseInt(rowid));
            }
            String areaMultiple = getElementTextContent(itemElement, "AreaMultiple");
            if (StringUtils.isNotBlank(areaMultiple)) {
                itemPojo.setAreamult(Integer.parseInt(areaMultiple));
            }
            itemPojo.setToolscode(getElementTextContent(itemElement, "ToolsCode"));
            //WorkHours、WorkCost忽略
            // 补充自定义字段映射
            itemPojo.setCustom1(getElementTextContent(itemElement, "Custom1"));
            itemPojo.setCustom2(getElementTextContent(itemElement, "Custom2"));
        } catch (Exception e) {
            throw new RuntimeException("映射Eng_SpecItem数据时发生错误: " + e.getMessage(), e);
        }

        return itemPojo;
    }

    /**
     * 映射Eng_SpecDrl到MatSpecpcbdrlPojo
     */
    private MatSpecpcbdrlPojo mapEngSpecDrlToMatSpecpcbdrlPojo(Element drlElement, String tid) {
        MatSpecpcbdrlPojo drlPojo = new MatSpecpcbdrlPojo();

        try {
            drlPojo.setId(getElementTextContent(drlElement, "id"));
            drlPojo.setPid(getElementTextContent(drlElement, "Especid"));
            drlPojo.setSymbol(getElementTextContent(drlElement, "Symbol"));
            drlPojo.setTolerance(getElementTextContent(drlElement, "Tolerance"));
            drlPojo.setPthmark(getElementTextContent(drlElement, "Pth"));
            drlPojo.setRemark(getElementTextContent(drlElement, "Remark"));

            String hole = getElementTextContent(drlElement, "Hole");
            String drill = getElementTextContent(drlElement, "Drill");
            String pcsTotal = getElementTextContent(drlElement, "PcsTotal");
            String pnlTotal = getElementTextContent(drlElement, "PnlTotal");
            String rowid = getElementTextContent(drlElement, "Rowid");

            if (StringUtils.isNotBlank(hole)) {
                drlPojo.setHolesize(Double.parseDouble(hole));
            }
            if (StringUtils.isNotBlank(drill)) {
                drlPojo.setDrillsize(Double.parseDouble(drill));
            }
            if (StringUtils.isNotBlank(pcsTotal)) {
                drlPojo.setPcstotal(Integer.parseInt(pcsTotal));
            }
            if (StringUtils.isNotBlank(pnlTotal)) {
                drlPojo.setPnltotal(Integer.parseInt(pnlTotal));
            }
            if (StringUtils.isNotBlank(rowid)) {
                drlPojo.setRownum(Integer.parseInt(rowid));
            }

            // 补充自定义字段映射
            drlPojo.setCustom1(getElementTextContent(drlElement, "Custom1"));
            drlPojo.setCustom2(getElementTextContent(drlElement, "Custom2"));

        } catch (Exception e) {
            throw new RuntimeException("映射Eng_SpecDrl数据时发生错误: " + e.getMessage(), e);
        }

        return drlPojo;
    }

    /**
     * 映射Eng_SpecPhoto到MatSpecpcbdrawPojo
     */
    private MatSpecpcbdrawPojo mapEngSpecPhotoToMatSpecpcbdrawPojo(Element photoElement, String tid) {
        MatSpecpcbdrawPojo drawPojo = new MatSpecpcbdrawPojo();

        try {
            drawPojo.setId(getElementTextContent(photoElement, "id"));
            drawPojo.setPid(getElementTextContent(photoElement, "Especid"));
            drawPojo.setDrawtype(getElementTextContent(photoElement, "PhotoType"));
            drawPojo.setDrawtitle(getElementTextContent(photoElement, "PhotoTitle"));
            drawPojo.setDrawurl(getElementTextContent(photoElement, "PhotoPath"));

            // 处理PhotoStr：转换格式并生成Base64图片
            String photoStr = getElementTextContent(photoElement, "PhotoStr");
            if (StringUtils.isNotBlank(photoStr)) {
                try {
                    String drawType = drawPojo.getDrawtype();
                    if ("VcutMap".equals(drawType)) {
                        // VcutMap先改为VCut
                        drawPojo.setDrawtype("VCut");
                        // VcutMap 类型：转换为单层结构的 DrawVcutPojo 格式并生成图片
                        String transformedJson = transformPhotoStrToVcutJson(photoStr);
                        drawPojo.setDrawjson(transformedJson);

                        // 解析JSON，提取根级别的DrawVcutPojo用于图片生成
                        JSONObject jsonObject = JSON.parseObject(transformedJson);
                        DrawVcutPojo vcutPojo = new DrawVcutPojo();
                        vcutPojo.setName(jsonObject.getString("name"));
                        vcutPojo.setVtype(jsonObject.getString("vtype"));
                        vcutPojo.setAngv(jsonObject.getString("angv"));
                        vcutPojo.setRes(jsonObject.getString("res"));
                        vcutPojo.setWarp(jsonObject.getString("warp"));
                        vcutPojo.setAngi(jsonObject.getString("angi"));
                        vcutPojo.setLon(jsonObject.getString("lon"));

                        // 使用根级别的DrawVcutPojo生成Base64图片
                        BufferedImage image;
                        if ("double".equals(vcutPojo.getVtype())) {
                            image = graphicsPcbService.getVcutDoubleMap(vcutPojo);
                        } else if ("single".equals(vcutPojo.getVtype())) {
                            image = graphicsPcbService.getVcutSingleMap(vcutPojo);
                        } else {
                            throw new RuntimeException("不支持的VcutMap类型: " + vcutPojo.getVtype());
                        }
                        String base64Image = D91M09B1Controller.bufferedImageToBase64(image);
                        drawPojo.setDrawimage(base64Image);
                    } else {
                        // 先改为Cut或Pnl
                        if ("CutMap".equals(drawType)) {
                            drawPojo.setDrawtype("Cut");
                        } else if ("PnlMap".equals(drawType)) {
                            drawPojo.setDrawtype("Pnl");
                        } else {
                            throw new RuntimeException("不支持的DrawType: " + drawType);
                        }
                        // CutMap 和 PnlMap 类型：使用现有逻辑
                        String transformedJson = transformPhotoStrToDrawPnlMapJson(photoStr);
                        drawPojo.setDrawjson(transformedJson);

                        // 直接复用现有逻辑生成Base64图片
                        DrawPnlMapPojo mapPojo = JSON.parseObject(transformedJson, DrawPnlMapPojo.class);
                        BufferedImage image = graphicsPcbService.getMapDrew(mapPojo);
                        String base64Image = D91M09B1Controller.bufferedImageToBase64(image);
                        drawPojo.setDrawimage(base64Image);
                    }
                } catch (Exception e) {
                    // 如果转换失败，保留原始PhotoStr
                    drawPojo.setDrawjson(photoStr);
                    System.err.println("PhotoStr转换失败: " + e.getMessage());
                }
            }
            String rowid = getElementTextContent(photoElement, "Rowid");
            if (StringUtils.isNotBlank(rowid)) {
                drawPojo.setRownum(Integer.parseInt(rowid));
            }
            // 创建信息
            drawPojo.setCreateby(getElementTextContent(photoElement, "Lister"));
            String createDate = getElementTextContent(photoElement, "CreateDate");
            String modifyDate = getElementTextContent(photoElement, "ModifyDate");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
            if (StringUtils.isNotBlank(createDate)) {
                drawPojo.setCreatedate(sdf.parse(createDate));
            }
            if (StringUtils.isNotBlank(modifyDate)) {
                drawPojo.setModifydate(sdf.parse(modifyDate));
            }
        } catch (Exception e) {
            throw new RuntimeException("映射Eng_SpecPhoto数据时发生错误: " + e.getMessage(), e);
        }
        return drawPojo;
    }

    /**
     * 将PhotoStr转换为DrawPnlMapPojo所需的JSON格式
     */
    private String transformPhotoStrToDrawPnlMapJson(String photoStr) throws Exception {
        // 解析原始PhotoStr JSON数组
        JSONArray originalArray = JSON.parseArray(photoStr);

        // 创建DrawPnlMapPojo对象
        DrawPnlMapPojo drawPnlMapPojo = new DrawPnlMapPojo();
        drawPnlMapPojo.setName(""); // 设置为空字符串，与示例一致

        // 从第一个元素获取拼板尺寸
        double pnlx = 100.0; // 默认宽度
        double pnly = 100.0; // 默认高度
        if (!originalArray.isEmpty()) {
            JSONObject firstItem = originalArray.getJSONObject(0);
            if (firstItem.containsKey("PnlX")) {
                pnlx = firstItem.getDoubleValue("PnlX");
            }
            if (firstItem.containsKey("PnlY")) {
                pnly = firstItem.getDoubleValue("PnlY");
            }
        }

        List<DrawPnlMapItemPojo> items = new ArrayList<>();

        // 遍历原始数组，转换为DrawPnlMapItemPojo格式
        for (int i = 0; i < originalArray.size(); i++) {
            JSONObject originalItem = originalArray.getJSONObject(i);
            DrawPnlMapItemPojo item = new DrawPnlMapItemPojo();

            // 映射字段，严格按照示例格式
            item.setLabel(""); // 设置为空字符串，与示例一致

            // orgx和orgy需要设置为字符串格式（根据示例）
            item.setOrgx(originalItem.getDoubleValue("OrgX"));
            item.setOrgy(originalItem.getDoubleValue("OrgY"));

            // 其他数值字段
            item.setSetx(originalItem.getDoubleValue("SetX"));
            item.setSety(originalItem.getDoubleValue("SetY"));
            item.setCpx(originalItem.getIntValue("CpX")); // 注意大小写CpX
            item.setCpy(originalItem.getIntValue("CpY")); // 注意大小写CpY
            item.setSpx(originalItem.getDoubleValue("SpX")); // 注意大小写SpX
            item.setSpy(originalItem.getDoubleValue("SpY")); // 注意大小写SpY

            items.add(item);
        }

        drawPnlMapPojo.setPnlx(pnlx);
        drawPnlMapPojo.setPnly(pnly);
        drawPnlMapPojo.setItem(items);

        // 转换为JSON字符串
        return JSON.toJSONString(drawPnlMapPojo);
    }

    /**
     * 将PhotoStr转换为DrawVcutPojo所需的JSON格式
     */
    private String transformPhotoStrToVcutJson(String photoStr) throws Exception {
        // 解析原始PhotoStr JSON数组
        JSONArray originalArray = JSON.parseArray(photoStr);

        if (originalArray.isEmpty()) {
            throw new RuntimeException("VcutMap PhotoStr 数组为空");
        }

        // 从第一个元素获取基础信息
        JSONObject firstItem = originalArray.getJSONObject(0);

        // 创建单层 Vcut JSON 结构
        JSONObject vcutJson = new JSONObject();

        // 设置固定的name值
        vcutJson.put("name", "4242");

        // 映射字段：VType -> vtype
        String vtype = firstItem.getString("VType");
        if ("双面".equals(vtype)) {
            vcutJson.put("vtype", "double");
        } else if ("单面".equals(vtype)) {
            vcutJson.put("vtype", "single");
        } else {
            vcutJson.put("vtype", StringUtils.isNotBlank(vtype) ? vtype : "");
        }

        // 映射其他字段，从原始数据提取并处理，没有值就为空
        String angv = firstItem.getString("AngV");
        vcutJson.put("angv", StringUtils.isNotBlank(angv) ? extractNumericValue(angv, "") : "");

        String res = firstItem.getString("Res");
        vcutJson.put("res", StringUtils.isNotBlank(res) ? extractNumericValue(res, "") : "");

        String warp = firstItem.getString("Warp");
        vcutJson.put("warp", StringUtils.isNotBlank(warp) ? extractNumericValue(warp, "") : "");

        String angi = firstItem.getString("AngI");
        vcutJson.put("angi", StringUtils.isNotBlank(angi) ? extractNumericValue(angi, "") : "");

        String lon = firstItem.getString("Lon");
        vcutJson.put("lon", StringUtils.isNotBlank(lon) ? extractNumericValue(lon, "") : "");

        // 转换为JSON字符串
        return vcutJson.toJSONString();
    }

    /**
     * 从字符串中提取数值部分
     */
    private String extractNumericValue(String input, String defaultValue) {
        if (StringUtils.isBlank(input)) {
            return defaultValue;
        }

        // 提取数字部分，去除单位和特殊字符
        String numericPart = input.replaceAll("[^0-9.]", "");
        return StringUtils.isNotBlank(numericPart) ? numericPart : defaultValue;
    }

    /**
     * 获取XML元素的文本内容
     */
    private String getElementTextContent(Element parent, String tagName) {
        NodeList nodeList = parent.getElementsByTagName(tagName);
        if (nodeList.getLength() > 0) {
            Node node = nodeList.item(0);
            return node.getTextContent();
        }
        return null;
    }
}
