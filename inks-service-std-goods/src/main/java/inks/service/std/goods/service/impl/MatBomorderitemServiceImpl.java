package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatBomorderitemEntity;
import inks.service.std.goods.domain.pojo.MatBomorderitemPojo;
import inks.service.std.goods.mapper.MatBomorderitemMapper;
import inks.service.std.goods.service.MatBomorderitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单Bom项目(MatBomorderitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-08 10:21:37
 */
@Service("matBomorderitemService")
public class MatBomorderitemServiceImpl implements MatBomorderitemService {
    @Resource
    private MatBomorderitemMapper matBomorderitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomorderitemPojo getEntity(String key, String tid) {
        return this.matBomorderitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBomorderitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBomorderitemPojo> lst = matBomorderitemMapper.getPageList(queryParam);
            PageInfo<MatBomorderitemPojo> pageInfo = new PageInfo<MatBomorderitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatBomorderitemPojo> getList(String Pid, String tid) {
        try {
            List<MatBomorderitemPojo> lst = matBomorderitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matBomorderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatBomorderitemPojo insert(MatBomorderitemPojo matBomorderitemPojo) {
        //初始化item的NULL
        MatBomorderitemPojo itempojo = this.clearNull(matBomorderitemPojo);
        MatBomorderitemEntity matBomorderitemEntity = new MatBomorderitemEntity();
        BeanUtils.copyProperties(itempojo, matBomorderitemEntity);

        matBomorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matBomorderitemEntity.setRevision(1);  //乐观锁
        this.matBomorderitemMapper.insert(matBomorderitemEntity);
        return this.getEntity(matBomorderitemEntity.getId(), matBomorderitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matBomorderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatBomorderitemPojo update(MatBomorderitemPojo matBomorderitemPojo) {
        MatBomorderitemEntity matBomorderitemEntity = new MatBomorderitemEntity();
        BeanUtils.copyProperties(matBomorderitemPojo, matBomorderitemEntity);
        this.matBomorderitemMapper.update(matBomorderitemEntity);
        return this.getEntity(matBomorderitemEntity.getId(), matBomorderitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matBomorderitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matBomorderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatBomorderitemPojo clearNull(MatBomorderitemPojo matBomorderitemPojo) {
        //初始化NULL字段
        if (matBomorderitemPojo.getPid() == null) matBomorderitemPojo.setPid("");
        if (matBomorderitemPojo.getGoodsid() == null) matBomorderitemPojo.setGoodsid("");
        if (matBomorderitemPojo.getItemcode() == null) matBomorderitemPojo.setItemcode("");
        if (matBomorderitemPojo.getItemname() == null) matBomorderitemPojo.setItemname("");
        if (matBomorderitemPojo.getItemspec() == null) matBomorderitemPojo.setItemspec("");
        if (matBomorderitemPojo.getItemunit() == null) matBomorderitemPojo.setItemunit("");
        if (matBomorderitemPojo.getMainqty() == null) matBomorderitemPojo.setMainqty(0D);
        if (matBomorderitemPojo.getSubqty() == null) matBomorderitemPojo.setSubqty(0D);
        if (matBomorderitemPojo.getNeedqty() == null) matBomorderitemPojo.setNeedqty(0D);
        if (matBomorderitemPojo.getLossrate() == null) matBomorderitemPojo.setLossrate(0D);
        if (matBomorderitemPojo.getAttrcode() == null) matBomorderitemPojo.setAttrcode("");
        if (matBomorderitemPojo.getFlowcode() == null) matBomorderitemPojo.setFlowcode("");
        if (matBomorderitemPojo.getRownum() == null) matBomorderitemPojo.setRownum(0);
        if (matBomorderitemPojo.getRemark() == null) matBomorderitemPojo.setRemark("");
        if (matBomorderitemPojo.getCustom1() == null) matBomorderitemPojo.setCustom1("");
        if (matBomorderitemPojo.getCustom2() == null) matBomorderitemPojo.setCustom2("");
        if (matBomorderitemPojo.getCustom3() == null) matBomorderitemPojo.setCustom3("");
        if (matBomorderitemPojo.getCustom4() == null) matBomorderitemPojo.setCustom4("");
        if (matBomorderitemPojo.getCustom5() == null) matBomorderitemPojo.setCustom5("");
        if (matBomorderitemPojo.getCustom6() == null) matBomorderitemPojo.setCustom6("");
        if (matBomorderitemPojo.getCustom7() == null) matBomorderitemPojo.setCustom7("");
        if (matBomorderitemPojo.getCustom8() == null) matBomorderitemPojo.setCustom8("");
        if (matBomorderitemPojo.getCustom9() == null) matBomorderitemPojo.setCustom9("");
        if (matBomorderitemPojo.getCustom10() == null) matBomorderitemPojo.setCustom10("");
        if (matBomorderitemPojo.getTenantid() == null) matBomorderitemPojo.setTenantid("");
        if (matBomorderitemPojo.getRevision() == null) matBomorderitemPojo.setRevision(0);
        return matBomorderitemPojo;
    }
}
