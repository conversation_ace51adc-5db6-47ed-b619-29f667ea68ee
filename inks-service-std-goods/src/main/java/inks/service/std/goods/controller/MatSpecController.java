package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatSpecPojo;
import inks.service.std.goods.domain.pojo.MatSpecitemPojo;
import inks.service.std.goods.domain.pojo.MatSpecitemdetailPojo;
import inks.service.std.goods.service.MatSpecService;
import inks.service.std.goods.service.MatSpecitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 工艺流程卡(MatSpec)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-16 10:38:32
 */
public class MatSpecController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(MatSpecController.class);
    private final String moduleCode = "D91M08B1";
    /**
     * 服务对象
     */
    @Resource
    private MatSpecService matSpecService;
    @Resource
    private UtilsFeignService utilsFeignService;
    /**
     * 服务对象Item
     */
    @Resource
    private MatSpecitemService matSpecitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取工艺流程卡详细信息", notes = "获取工艺流程卡详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Spec.List")
    public R<MatSpecPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matSpecService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Spec.List")
    public R<PageInfo<MatSpecitemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Spec.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matSpecService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取工艺流程卡详细信息", notes = "获取工艺流程卡详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Spec.List")
    public R<MatSpecPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matSpecService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Spec.List")
    public R<PageInfo<MatSpecPojo>> getBillList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Spec.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.matSpecService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Spec.List")
    public R<PageInfo<MatSpecPojo>> getPageTh(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Spec.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matSpecService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增工艺流程卡", notes = "新增工艺流程卡", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Spec.Add")
    public R<MatSpecPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatSpecPojo matSpecPojo = JSONArray.parseObject(json, MatSpecPojo.class);
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Mat_Spec", null, loginUser.getTenantid());
            matSpecPojo.setRefno(refno);
            matSpecPojo.setCreateby(loginUser.getRealName());   // 创建者
            matSpecPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matSpecPojo.setCreatedate(new Date());   // 创建时间
            matSpecPojo.setLister(loginUser.getRealname());   // 制表
            matSpecPojo.setListerid(loginUser.getUserid());    // 制表id            
            matSpecPojo.setModifydate(new Date());   //修改时间
            matSpecPojo.setTenantid(loginUser.getTenantid());   //租户id
            MatSpecPojo insert = this.matSpecService.insert(matSpecPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改工艺流程卡", notes = "修改工艺流程卡", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Spec.Edit")
    public R<MatSpecPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatSpecPojo matSpecPojo = JSONArray.parseObject(json, MatSpecPojo.class);
            matSpecPojo.setLister(loginUser.getRealname());   // 制表
            matSpecPojo.setListerid(loginUser.getUserid());    // 制表id   
            matSpecPojo.setModifydate(new Date());   //修改时间
            matSpecPojo.setAssessor(""); //审核员
            matSpecPojo.setAssessorid(""); //审核员
            matSpecPojo.setAssessdate(new Date()); //审核时间
            matSpecPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matSpecService.update(matSpecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除工艺流程卡", notes = "删除工艺流程卡", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Spec.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String refno = this.matSpecService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增工艺流程卡Item", notes = "新增工艺流程卡Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Spec.Add")
    public R<MatSpecitemPojo> createItem(@RequestBody String json) {
        try {
            MatSpecitemPojo matSpecitemPojo = JSONArray.parseObject(json, MatSpecitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            matSpecitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.matSpecitemService.insert(matSpecitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除工艺流程卡Item", notes = "删除工艺流程卡Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Spec.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matSpecitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核工艺流程卡", notes = "审核工艺流程卡", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Spec.Approval")
    public R<MatSpecPojo> approval(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            MatSpecPojo matSpecPojo = this.matSpecService.getEntity(key, loginUser.getTenantid());
            if (matSpecPojo.getAssessor().equals("")) {
                matSpecPojo.setAssessor(loginUser.getRealname()); //审核员
                matSpecPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                matSpecPojo.setAssessor(""); //审核员
                matSpecPojo.setAssessorid(""); //审核员
            }
            matSpecPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matSpecService.approval(matSpecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Spec.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatSpecPojo matSpecPojo = this.matSpecService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matSpecPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = matSpecPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    MatSpecitemPojo matSpecitemPojo = new MatSpecitemPojo();
                    matSpecPojo.getItem().add(matSpecitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(matSpecPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            MatSpecPojo matSpecPojo = this.matSpecService.getEntity(key, loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData(); //
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved != null && printapproved.equals("true") && matSpecPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(matSpecPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<MatSpecitemPojo> lstitem = this.matSpecitemService.getList(key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrcostListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "工艺流程卡" + matSpecPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限


            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

