package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpectempEntity;
import inks.service.std.goods.domain.pojo.MatSpectempPojo;
import inks.service.std.goods.mapper.MatSpectempMapper;
import inks.service.std.goods.service.MatSpectempService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * MI模板(MatSpectemp)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-10 16:32:06
 */
@Service("matSpectempService")
public class MatSpectempServiceImpl implements MatSpectempService {
    @Resource
    private MatSpectempMapper matSpectempMapper;

    private static void cleanNull(MatSpectempPojo matSpectempPojo) {
        if (matSpectempPojo.getTempcode() == null) matSpectempPojo.setTempcode("");
        if (matSpectempPojo.getTempname() == null) matSpectempPojo.setTempname("");
        if (matSpectempPojo.getOperator() == null) matSpectempPojo.setOperator("");
        if (matSpectempPojo.getOperatorid() == null) matSpectempPojo.setOperatorid("");
        if (matSpectempPojo.getItemjson() == null) matSpectempPojo.setItemjson("");
        if (matSpectempPojo.getSummary() == null) matSpectempPojo.setSummary("");
        if (matSpectempPojo.getEnabledmark() == null) matSpectempPojo.setEnabledmark(0);
        if (matSpectempPojo.getVersionnum() == null) matSpectempPojo.setVersionnum("");
        if (matSpectempPojo.getCreateby() == null) matSpectempPojo.setCreateby("");
        if (matSpectempPojo.getCreatebyid() == null) matSpectempPojo.setCreatebyid("");
        if (matSpectempPojo.getCreatedate() == null) matSpectempPojo.setCreatedate(new Date());
        if (matSpectempPojo.getLister() == null) matSpectempPojo.setLister("");
        if (matSpectempPojo.getListerid() == null) matSpectempPojo.setListerid("");
        if (matSpectempPojo.getModifydate() == null) matSpectempPojo.setModifydate(new Date());
        if (matSpectempPojo.getAssessor() == null) matSpectempPojo.setAssessor("");
        if (matSpectempPojo.getAssessorid() == null) matSpectempPojo.setAssessorid("");
        if (matSpectempPojo.getAssessdate() == null) matSpectempPojo.setAssessdate(new Date());
        if (matSpectempPojo.getCustom1() == null) matSpectempPojo.setCustom1("");
        if (matSpectempPojo.getCustom2() == null) matSpectempPojo.setCustom2("");
        if (matSpectempPojo.getCustom3() == null) matSpectempPojo.setCustom3("");
        if (matSpectempPojo.getCustom4() == null) matSpectempPojo.setCustom4("");
        if (matSpectempPojo.getCustom5() == null) matSpectempPojo.setCustom5("");
        if (matSpectempPojo.getCustom6() == null) matSpectempPojo.setCustom6("");
        if (matSpectempPojo.getCustom7() == null) matSpectempPojo.setCustom7("");
        if (matSpectempPojo.getCustom8() == null) matSpectempPojo.setCustom8("");
        if (matSpectempPojo.getCustom9() == null) matSpectempPojo.setCustom9("");
        if (matSpectempPojo.getCustom10() == null) matSpectempPojo.setCustom10("");
        if (matSpectempPojo.getTenantid() == null) matSpectempPojo.setTenantid("");
        if (matSpectempPojo.getTenantname() == null) matSpectempPojo.setTenantname("");
        if (matSpectempPojo.getRevision() == null) matSpectempPojo.setRevision(0);
    }

    @Override
    public MatSpectempPojo getEntity(String key, String tid) {
        return this.matSpectempMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<MatSpectempPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpectempPojo> lst = matSpectempMapper.getPageList(queryParam);
            PageInfo<MatSpectempPojo> pageInfo = new PageInfo<MatSpectempPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public MatSpectempPojo insert(MatSpectempPojo matSpectempPojo) {
        //初始化NULL字段
        cleanNull(matSpectempPojo);
        MatSpectempEntity matSpectempEntity = new MatSpectempEntity();
        BeanUtils.copyProperties(matSpectempPojo, matSpectempEntity);
        //生成雪花id
        matSpectempEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSpectempEntity.setRevision(1);  //乐观锁
        this.matSpectempMapper.insert(matSpectempEntity);
        return this.getEntity(matSpectempEntity.getId(), matSpectempEntity.getTenantid());
    }

    @Override
    public MatSpectempPojo update(MatSpectempPojo matSpectempPojo) {
        MatSpectempEntity matSpectempEntity = new MatSpectempEntity();
        BeanUtils.copyProperties(matSpectempPojo, matSpectempEntity);
        this.matSpectempMapper.update(matSpectempEntity);
        return this.getEntity(matSpectempEntity.getId(), matSpectempEntity.getTenantid());
    }

    @Override
    public int delete(String key, String tid) {
        return this.matSpectempMapper.delete(key, tid);
    }

    @Override
    @Transactional
    public MatSpectempPojo approval(MatSpectempPojo matSpectempPojo) {
        //主表更改
        MatSpectempEntity matSpectempEntity = new MatSpectempEntity();
        BeanUtils.copyProperties(matSpectempPojo, matSpectempEntity);
        this.matSpectempMapper.approval(matSpectempEntity);
        //返回Bill实例
        return this.getEntity(matSpectempEntity.getId(), matSpectempEntity.getTenantid());
    }

}
