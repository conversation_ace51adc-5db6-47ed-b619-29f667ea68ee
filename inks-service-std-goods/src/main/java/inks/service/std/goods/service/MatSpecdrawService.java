package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecdrawPojo;

import java.util.List;

/**
 * 工艺流程排版(MatSpecdraw)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-02 14:49:50
 */
public interface MatSpecdrawService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecdrawPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecdrawPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecdrawPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSpecdrawPojo 实例对象
     * @return 实例对象
     */
    MatSpecdrawPojo insert(MatSpecdrawPojo matSpecdrawPojo);

    /**
     * 修改数据
     *
     * @param matSpecdrawpojo 实例对象
     * @return 实例对象
     */
    MatSpecdrawPojo update(MatSpecdrawPojo matSpecdrawpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSpecdrawpojo 实例对象
     * @return 实例对象
     */
    MatSpecdrawPojo clearNull(MatSpecdrawPojo matSpecdrawpojo);
}
