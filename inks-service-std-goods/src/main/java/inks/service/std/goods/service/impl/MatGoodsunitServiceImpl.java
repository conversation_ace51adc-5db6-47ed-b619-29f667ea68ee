package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatGoodsunitEntity;
import inks.service.std.goods.domain.pojo.MatGoodsunitPojo;
import inks.service.std.goods.mapper.MatGoodsunitMapper;
import inks.service.std.goods.service.MatGoodsunitService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 货品单位(MatGoodsunit)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-08 11:18:20
 */
@Service("matGoodsunitService")
public class MatGoodsunitServiceImpl implements MatGoodsunitService {
    @Resource
    private MatGoodsunitMapper matGoodsunitMapper;

    @Override
    public MatGoodsunitPojo getEntity(String key, String tid) {
        return this.matGoodsunitMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<MatGoodsunitPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGoodsunitPojo> lst = matGoodsunitMapper.getPageList(queryParam);
            PageInfo<MatGoodsunitPojo> pageInfo = new PageInfo<MatGoodsunitPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public MatGoodsunitPojo insert(MatGoodsunitPojo matGoodsunitPojo) {
        // unitname重名检查 忽略大小写
        int i = this.matGoodsunitMapper.countUnitname(matGoodsunitPojo.getUnitname().toLowerCase(), null, matGoodsunitPojo.getTenantid());
        if (i > 0) {
            throw new BaseBusinessException("货品单位名称已存在");
        }
        //初始化NULL字段
        cleanNull(matGoodsunitPojo);
        MatGoodsunitEntity matGoodsunitEntity = new MatGoodsunitEntity();
        BeanUtils.copyProperties(matGoodsunitPojo, matGoodsunitEntity);
        //生成雪花id
        matGoodsunitEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matGoodsunitEntity.setRevision(1);  //乐观锁
        this.matGoodsunitMapper.insert(matGoodsunitEntity);
        return this.getEntity(matGoodsunitEntity.getId(), matGoodsunitEntity.getTenantid());
    }


    @Override
    public MatGoodsunitPojo update(MatGoodsunitPojo matGoodsunitPojo) {
        // unitname重名检查 忽略大小写
        int i = this.matGoodsunitMapper.countUnitname(matGoodsunitPojo.getUnitname().toLowerCase(), matGoodsunitPojo.getId(), matGoodsunitPojo.getTenantid());
        if (i > 0) {
            throw new BaseBusinessException("货品单位名称已存在");
        }
        MatGoodsunitEntity matGoodsunitEntity = new MatGoodsunitEntity();
        BeanUtils.copyProperties(matGoodsunitPojo, matGoodsunitEntity);
        this.matGoodsunitMapper.update(matGoodsunitEntity);
        return this.getEntity(matGoodsunitEntity.getId(), matGoodsunitEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.matGoodsunitMapper.delete(key, tid);
    }


    private static void cleanNull(MatGoodsunitPojo matGoodsunitPojo) {
        if (matGoodsunitPojo.getUnitname() == null) matGoodsunitPojo.setUnitname("");
        if (matGoodsunitPojo.getEnabledmark() == null) matGoodsunitPojo.setEnabledmark(0);
        if (matGoodsunitPojo.getDecnum() == null) matGoodsunitPojo.setDecnum(0);
        if (matGoodsunitPojo.getRoundingtype() == null) matGoodsunitPojo.setRoundingtype(0);
        if (matGoodsunitPojo.getUnittype() == null) matGoodsunitPojo.setUnittype(0);
        if (matGoodsunitPojo.getBackcolorargb() == null) matGoodsunitPojo.setBackcolorargb("");
        if (matGoodsunitPojo.getForecolorargb() == null) matGoodsunitPojo.setForecolorargb("");
        if (matGoodsunitPojo.getRownum() == null) matGoodsunitPojo.setRownum(0);
        if (matGoodsunitPojo.getRemark() == null) matGoodsunitPojo.setRemark("");
        if (matGoodsunitPojo.getCreateby() == null) matGoodsunitPojo.setCreateby("");
        if (matGoodsunitPojo.getCreatebyid() == null) matGoodsunitPojo.setCreatebyid("");
        if (matGoodsunitPojo.getCreatedate() == null) matGoodsunitPojo.setCreatedate(new Date());
        if (matGoodsunitPojo.getLister() == null) matGoodsunitPojo.setLister("");
        if (matGoodsunitPojo.getListerid() == null) matGoodsunitPojo.setListerid("");
        if (matGoodsunitPojo.getModifydate() == null) matGoodsunitPojo.setModifydate(new Date());
        if (matGoodsunitPojo.getCustom1() == null) matGoodsunitPojo.setCustom1("");
        if (matGoodsunitPojo.getCustom2() == null) matGoodsunitPojo.setCustom2("");
        if (matGoodsunitPojo.getCustom3() == null) matGoodsunitPojo.setCustom3("");
        if (matGoodsunitPojo.getCustom4() == null) matGoodsunitPojo.setCustom4("");
        if (matGoodsunitPojo.getCustom5() == null) matGoodsunitPojo.setCustom5("");
        if (matGoodsunitPojo.getCustom6() == null) matGoodsunitPojo.setCustom6("");
        if (matGoodsunitPojo.getCustom7() == null) matGoodsunitPojo.setCustom7("");
        if (matGoodsunitPojo.getCustom8() == null) matGoodsunitPojo.setCustom8("");
        if (matGoodsunitPojo.getCustom9() == null) matGoodsunitPojo.setCustom9("");
        if (matGoodsunitPojo.getCustom10() == null) matGoodsunitPojo.setCustom10("");
        if (matGoodsunitPojo.getTenantid() == null) matGoodsunitPojo.setTenantid("");
        if (matGoodsunitPojo.getRevision() == null) matGoodsunitPojo.setRevision(0);
    }


    @Override
    public List<MatGoodsunitPojo> getList(Integer type, String tid) {
        return this.matGoodsunitMapper.getList(type, tid);
    }
}
