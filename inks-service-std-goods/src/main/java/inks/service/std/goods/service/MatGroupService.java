package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatGroupPojo;

import java.util.List;

/**
 * 物料分组(MatGroup)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-21 09:03:24
 */
public interface MatGroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatGroupPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatGroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matGroupPojo 实例对象
     * @return 实例对象
     */
    MatGroupPojo insert(MatGroupPojo matGroupPojo);

    /**
     * 修改数据
     *
     * @param matGrouppojo 实例对象
     * @return 实例对象
     */
    MatGroupPojo update(MatGroupPojo matGrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过主键读取子集
     *
     * @param key 主键
     * @return 是否成功
     */

    List<MatGroupPojo> getListByParentid(String key, String tid);

}
