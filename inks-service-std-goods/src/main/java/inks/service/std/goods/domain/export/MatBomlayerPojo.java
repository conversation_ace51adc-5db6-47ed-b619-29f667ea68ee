package inks.service.std.goods.domain.export;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.List;

/**
 * 物料Bom(MatBom)实体类
 *
 * <AUTHOR>
 * @since 2022-09-07 10:40:08
 */
public class MatBomlayerPojo implements Serializable {
    private static final long serialVersionUID = -87461496195151425L;
    //赋值bom.id
    private String id;

    // 货品ID
    private String goodsid;
    // bomid
    private String bomid;
    // 货品编码
    @Excel(name = "货品编码")
    private String goodsuid;
    // 名称
    @Excel(name = "名称")
    private String goodsname;
    // 规格
    @Excel(name = "规格")
    private String goodsspec;
    // 外部编码
    @Excel(name = "外部编码")
    private String partid;
    // 货品单位
    @Excel(name = "货品单位")
    private String goodsunit;
    // 定额工时
    @Excel(name = "定额工时")
    private Double quotahour;
    // 最小用时
    @Excel(name = "最小用时")
    private Double mintime;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // 版本号
    @Excel(name = "版本号")
    private String versionnum;
    // 子表
    private List<MatBomlayeritemPojo> item;


    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    public String getBomid() {
        return bomid;
    }

    public void setBomid(String bomid) {
        this.bomid = bomid;
    }

    public String getGoodsuid() {
        return goodsuid;
    }

    public void setGoodsuid(String goodsuid) {
        this.goodsuid = goodsuid;
    }

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    public String getPartid() {
        return partid;
    }

    public void setPartid(String partid) {
        this.partid = partid;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public Double getQuotahour() {
        return quotahour;
    }

    public void setQuotahour(Double quotahour) {
        this.quotahour = quotahour;
    }

    public Double getMintime() {
        return mintime;
    }

    public void setMintime(Double mintime) {
        this.mintime = mintime;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getVersionnum() {
        return versionnum;
    }

    public void setVersionnum(String versionnum) {
        this.versionnum = versionnum;
    }

    public List<MatBomlayeritemPojo> getItem() {
        return item;
    }

    public void setItem(List<MatBomlayeritemPojo> item) {
        this.item = item;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}

