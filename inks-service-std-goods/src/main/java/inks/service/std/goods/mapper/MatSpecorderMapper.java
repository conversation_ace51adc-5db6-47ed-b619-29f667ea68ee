package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSpecorderEntity;
import inks.service.std.goods.domain.pojo.MatSpecorderPojo;
import inks.service.std.goods.domain.pojo.MatSpecorderitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单工艺卡(MatSpecorder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-16 14:42:35
 */
@Mapper
public interface MatSpecorderMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecorderPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecorderitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecorderPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSpecorderEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpecorderEntity matSpecorderEntity);


    /**
     * 修改数据
     *
     * @param matSpecorderEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpecorderEntity matSpecorderEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matSpecorderPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(MatSpecorderPojo matSpecorderPojo);

    List<String> getDelDrawIds(MatSpecorderPojo matSpecorderPojo);

    /**
     * 修改数据
     *
     * @param matSpecorderEntity 实例对象
     * @return 影响行数
     */
    int approval(MatSpecorderEntity matSpecorderEntity);

    int updateMachItemSpecStatus(@Param("specstatus") Integer specstatus,@Param("machitemid") String machitemid, @Param("tenantid") String tenantid);

    int updateMachItemSpec(@Param("id") String id, @Param("refno") String refno, @Param("specstatus") int i, @Param("machitemid") String machitemid, @Param("tenantid") String tenantid);
}

