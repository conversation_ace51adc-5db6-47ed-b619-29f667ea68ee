package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecpcbPojo;
import inks.service.std.goods.domain.pojo.MatSpecpcbitemdetailPojo;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;

/**
 * Pcb工艺(MatSpecpcb)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-16 10:40:34
 */
public interface MatSpecpcbService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecpcbPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecpcbitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecpcbPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecpcbPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecpcbPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSpecpcbPojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbPojo insert(MatSpecpcbPojo matSpecpcbPojo,String misizetoareaqty);

    /**
     * 修改数据
     *
     * @param matSpecpcbpojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbPojo update(MatSpecpcbPojo matSpecpcbpojo,String misizetoareaqty);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matSpecpcbPojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbPojo approval(MatSpecpcbPojo matSpecpcbPojo);

    int checkGoodsid(String goodsid, String tenantid);

    MatSpecpcbPojo parseXmlToMatSpecpcbPojo(String xmlContent, LoginUser loginUser) throws ParserConfigurationException, IOException, SAXException;
}
