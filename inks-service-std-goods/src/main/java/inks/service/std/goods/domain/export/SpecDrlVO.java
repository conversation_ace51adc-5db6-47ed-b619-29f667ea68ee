package inks.service.std.goods.domain.export;

// 前端只需要返回3个字段：规格、钻孔、总行数PNL
public class SpecDrlVO {
    // 规格
    private String symbol;
    // 前端决定是 孔径或刀径
    private double drill;
    // XY坐标总行数PNL
    private int pnltotal;
    private int rowid;


    @Override
    public String toString() {
        return "SpecDrlEntity{" +
                "symbol='" + symbol + '\'' +
                ", drill=" + drill +
                ", pnltotal=" + pnltotal +
                ", rowid=" + rowid +
                '}';
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public void setPnltotal(int pnltotal) {
        this.pnltotal = pnltotal;
    }

    public void setRowid(int rowid) {
        this.rowid = rowid;
    }

    public double getDrill() {
        return drill;
    }

    public void setDrill(double drill) {
        this.drill = drill;
    }

    public int getPnltotal() {
        return pnltotal;
    }

    public int getRowid() {
        return rowid;
    }
// Add other getters and setters as needed


}