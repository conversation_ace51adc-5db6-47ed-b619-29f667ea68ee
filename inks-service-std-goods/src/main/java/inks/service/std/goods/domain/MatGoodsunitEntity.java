package inks.service.std.goods.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 货品单位(MatGoodsunit)实体类
 *
 * <AUTHOR>
 * @since 2025-05-08 11:18:18
 */
public class MatGoodsunitEntity implements Serializable {
    private static final long serialVersionUID = 804861825567517152L;
     // 主键ID
    private String id;
     // 单位名称
    private String unitname;
     // 有效
    private Integer enabledmark;
     // 小数位数
    private Integer decnum;
     // 舍入类型（0：四舍五入，1：向上，2：向下）
    private Integer roundingtype;
     // 单位适用类型（0：通用，1：产品/半成品，2：物料）
    private Integer unittype;
     // 背景色
    private String backcolorargb;
     // 前景色
    private String forecolorargb;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

// 主键ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 单位名称
    public String getUnitname() {
        return unitname;
    }
    
    public void setUnitname(String unitname) {
        this.unitname = unitname;
    }
        
// 有效
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 小数位数
    public Integer getDecnum() {
        return decnum;
    }
    
    public void setDecnum(Integer decnum) {
        this.decnum = decnum;
    }
        
// 舍入类型（0：四舍五入，1：向上，2：向下）
    public Integer getRoundingtype() {
        return roundingtype;
    }
    
    public void setRoundingtype(Integer roundingtype) {
        this.roundingtype = roundingtype;
    }
        
// 单位适用类型（0：通用，1：产品/半成品，2：物料）
    public Integer getUnittype() {
        return unittype;
    }
    
    public void setUnittype(Integer unittype) {
        this.unittype = unittype;
    }
        
// 背景色
    public String getBackcolorargb() {
        return backcolorargb;
    }
    
    public void setBackcolorargb(String backcolorargb) {
        this.backcolorargb = backcolorargb;
    }
        
// 前景色
    public String getForecolorargb() {
        return forecolorargb;
    }
    
    public void setForecolorargb(String forecolorargb) {
        this.forecolorargb = forecolorargb;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

