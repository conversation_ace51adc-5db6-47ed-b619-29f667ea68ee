package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecdrawEntity;
import inks.service.std.goods.domain.pojo.MatSpecdrawPojo;
import inks.service.std.goods.mapper.MatSpecdrawMapper;
import inks.service.std.goods.service.MatSpecdrawService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工艺流程排版(MatSpecdraw)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-02 14:49:51
 */
@Service("matSpecdrawService")
public class MatSpecdrawServiceImpl implements MatSpecdrawService {
    @Resource
    private MatSpecdrawMapper matSpecdrawMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecdrawPojo getEntity(String key, String tid) {
        return this.matSpecdrawMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecdrawPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecdrawPojo> lst = matSpecdrawMapper.getPageList(queryParam);
            PageInfo<MatSpecdrawPojo> pageInfo = new PageInfo<MatSpecdrawPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSpecdrawPojo> getList(String Pid, String tid) {
        try {
            List<MatSpecdrawPojo> lst = matSpecdrawMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSpecdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecdrawPojo insert(MatSpecdrawPojo matSpecdrawPojo) {
        //初始化item的NULL
        MatSpecdrawPojo itempojo = this.clearNull(matSpecdrawPojo);
        MatSpecdrawEntity matSpecdrawEntity = new MatSpecdrawEntity();
        BeanUtils.copyProperties(itempojo, matSpecdrawEntity);

        matSpecdrawEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSpecdrawEntity.setRevision(1);  //乐观锁
        this.matSpecdrawMapper.insert(matSpecdrawEntity);
        return this.getEntity(matSpecdrawEntity.getId(), matSpecdrawEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecdrawPojo update(MatSpecdrawPojo matSpecdrawPojo) {
        MatSpecdrawEntity matSpecdrawEntity = new MatSpecdrawEntity();
        BeanUtils.copyProperties(matSpecdrawPojo, matSpecdrawEntity);
        this.matSpecdrawMapper.update(matSpecdrawEntity);
        return this.getEntity(matSpecdrawEntity.getId(), matSpecdrawEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSpecdrawMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSpecdrawPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecdrawPojo clearNull(MatSpecdrawPojo matSpecdrawPojo) {
        //初始化NULL字段
        if (matSpecdrawPojo.getPid() == null) matSpecdrawPojo.setPid("");
        if (matSpecdrawPojo.getWpid() == null) matSpecdrawPojo.setWpid("");
        if (matSpecdrawPojo.getWpcode() == null) matSpecdrawPojo.setWpcode("");
        if (matSpecdrawPojo.getWpname() == null) matSpecdrawPojo.setWpname("");
        if (matSpecdrawPojo.getDrawtype() == null) matSpecdrawPojo.setDrawtype("");
        if (matSpecdrawPojo.getDrawtitle() == null) matSpecdrawPojo.setDrawtitle("");
        if (matSpecdrawPojo.getDrawimage() == null) matSpecdrawPojo.setDrawimage("");
        if (matSpecdrawPojo.getDrawjson() == null) matSpecdrawPojo.setDrawjson("");
        if (matSpecdrawPojo.getDrawurl() == null) matSpecdrawPojo.setDrawurl("");
        if (matSpecdrawPojo.getInsidemark() == null) matSpecdrawPojo.setInsidemark(0);
        if (matSpecdrawPojo.getRownum() == null) matSpecdrawPojo.setRownum(0);
        if (matSpecdrawPojo.getRemark() == null) matSpecdrawPojo.setRemark("");
        if (matSpecdrawPojo.getCustom1() == null) matSpecdrawPojo.setCustom1("");
        if (matSpecdrawPojo.getCustom2() == null) matSpecdrawPojo.setCustom2("");
        if (matSpecdrawPojo.getCustom3() == null) matSpecdrawPojo.setCustom3("");
        if (matSpecdrawPojo.getCustom4() == null) matSpecdrawPojo.setCustom4("");
        if (matSpecdrawPojo.getCustom5() == null) matSpecdrawPojo.setCustom5("");
        if (matSpecdrawPojo.getCustom6() == null) matSpecdrawPojo.setCustom6("");
        if (matSpecdrawPojo.getCustom7() == null) matSpecdrawPojo.setCustom7("");
        if (matSpecdrawPojo.getCustom8() == null) matSpecdrawPojo.setCustom8("");
        if (matSpecdrawPojo.getCustom9() == null) matSpecdrawPojo.setCustom9("");
        if (matSpecdrawPojo.getCustom10() == null) matSpecdrawPojo.setCustom10("");
        if (matSpecdrawPojo.getTenantid() == null) matSpecdrawPojo.setTenantid("");
        if (matSpecdrawPojo.getTenantname() == null) matSpecdrawPojo.setTenantname("");
        if (matSpecdrawPojo.getRevision() == null) matSpecdrawPojo.setRevision(0);
        return matSpecdrawPojo;
    }
}
