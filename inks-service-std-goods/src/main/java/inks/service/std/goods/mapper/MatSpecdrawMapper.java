package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSpecdrawEntity;
import inks.service.std.goods.domain.pojo.MatSpecdrawPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工艺流程排版(MatSpecdraw)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-02 14:49:49
 */
 @Mapper
public interface MatSpecdrawMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecdrawPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecdrawPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecdrawPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matSpecdrawEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpecdrawEntity matSpecdrawEntity);

    
    /**
     * 修改数据
     *
     * @param matSpecdrawEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpecdrawEntity matSpecdrawEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

