package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatBomorderPojo;
import inks.service.std.goods.domain.pojo.MatBomorderitemPojo;
import inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo;
import inks.service.std.goods.domain.pojo.MatBomordertreePojo;
import inks.service.std.goods.service.MatBomorderService;
import inks.service.std.goods.service.MatBomorderitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 订单Bom(Mat_BomOrder)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-11 14:40:09
 */
public class MatBomorderController {
    /**
     * 服务对象
     */
    @Resource
    private MatBomorderService matBomorderService;

    /**
     * 服务对象Item
     */
    @Resource
    private MatBomorderitemService matBomorderitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取订单Bom详细信息", notes = "获取订单Bom详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_BomOrder.List")
    public R<MatBomorderPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matBomorderService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.List")
    public R<PageInfo<MatBomorderitemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_BomOrder.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matBomorderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取订单Bom详细信息", notes = "获取订单Bom详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_BomOrder.List")
    public R<MatBomorderPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matBomorderService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.List")
    public R<PageInfo<MatBomorderPojo>> getBillList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_BomOrder.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.matBomorderService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.List")
    public R<PageInfo<MatBomorderPojo>> getPageTh(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_BomOrder.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matBomorderService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增订单Bom", notes = "新增订单Bom", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.Add")
    public R<MatBomorderPojo> create(@RequestBody String json) {
        try {
            MatBomorderPojo matBomorderPojo = JSONArray.parseObject(json, MatBomorderPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            matBomorderPojo.setCreateby(loginUser.getRealname());   //创建者
            matBomorderPojo.setCreatebyid(loginUser.getUserid());
            matBomorderPojo.setCreatedate(new Date());   //创建时间
            matBomorderPojo.setLister(loginUser.getRealname());   //用户名
            matBomorderPojo.setListerid(loginUser.getUserid());
            matBomorderPojo.setModifydate(new Date());   //修改时间
            matBomorderPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matBomorderService.insert(matBomorderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改订单Bom", notes = "修改订单Bom", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.Edit")
    public R<MatBomorderPojo> update(@RequestBody String json) {
        try {
            MatBomorderPojo matBomorderPojo = JSONArray.parseObject(json, MatBomorderPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            matBomorderPojo.setLister(loginUser.getRealname());   //用户名
            matBomorderPojo.setListerid(loginUser.getUserid());
            matBomorderPojo.setModifydate(new Date());   //修改时间
            matBomorderPojo.setAssessor(""); //审核员
            matBomorderPojo.setAssessorid("");
            matBomorderPojo.setAssessdate(new Date()); //审核时间
            matBomorderPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matBomorderService.update(matBomorderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除订单Bom", notes = "删除订单Bom", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_BomOrder.Delete")
    public R<Integer> delete(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String machuid = this.matBomorderService.delete(key, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  machuid:" + machuid);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增订单BomItem", notes = "新增订单BomItem", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.Add")
    public R<MatBomorderitemPojo> createItem(@RequestBody String json) {
        try {
            MatBomorderitemPojo matBomorderitemPojo = JSONArray.parseObject(json, MatBomorderitemPojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            matBomorderitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.matBomorderitemService.insert(matBomorderitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除订单BomItem", notes = "删除订单BomItem", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_BomOrder.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matBomorderitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核订单Bom", notes = "审核订单Bom", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_BomOrder.Approval")
    public R<MatBomorderPojo> approval(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            MatBomorderPojo matBomorderPojo = this.matBomorderService.getEntity(key, loginUser.getTenantid());
            if (matBomorderPojo.getAssessor().equals(""))
                matBomorderPojo.setAssessor(loginUser.getRealname()); //审核员
            else
                matBomorderPojo.setAssessor(""); //审核员
            matBomorderPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matBomorderService.approval(matBomorderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_BomOrder.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatBomorderPojo matBomorderPojo = this.matBomorderService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matBomorderPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = matBomorderPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    MatBomorderitemPojo matBomorderitemPojo = new MatBomorderitemPojo();
                    matBomorderPojo.getItem().add(matBomorderitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(matBomorderPojo.getItem());

        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @param qty 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结构清单Tree信息", notes = "获取结构清单Tree信息", produces = "application/json")
    @RequestMapping(value = "/getTreeEntity", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.List")
    public R<MatBomordertreePojo> getTreeEntity(String key, @RequestBody Double qty) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            MatBomordertreePojo matBomordertreePojo = this.matBomorderService.getTreeEntity(key, loginUser.getTenantid());
            return R.ok(matBomordertreePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

