package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatSpectempPojo;
import inks.service.std.goods.service.MatSpectempService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * MI模板(Mat_SpecTemp)表控制层
 *
 * <AUTHOR>
 * @since 2024-04-10 16:32:06
 */
//@RestController
//@RequestMapping("matSpectemp")
public class MatSpectempController {

    private final static Logger logger = LoggerFactory.getLogger(MatSpectempController.class);
    @Resource
    private MatSpectempService matSpectempService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取MI模板详细信息", notes = "获取MI模板详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecTemp.List")
    public R<MatSpectempPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matSpectempService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecTemp.List")
    public R<PageInfo<MatSpectempPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_SpecTemp.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matSpectempService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增MI模板", notes = "新增MI模板", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecTemp.Add")
    public R<MatSpectempPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatSpectempPojo matSpectempPojo = JSONArray.parseObject(json, MatSpectempPojo.class);
            matSpectempPojo.setCreateby(loginUser.getRealName());   // 创建者
            matSpectempPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matSpectempPojo.setCreatedate(new Date());   // 创建时间
            matSpectempPojo.setLister(loginUser.getRealname());   // 制表
            matSpectempPojo.setListerid(loginUser.getUserid());    // 制表id  
            matSpectempPojo.setModifydate(new Date());   //修改时间
            matSpectempPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matSpectempService.insert(matSpectempPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改MI模板", notes = "修改MI模板", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_SpecTemp.Edit")
    public R<MatSpectempPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatSpectempPojo matSpectempPojo = JSONArray.parseObject(json, MatSpectempPojo.class);
            matSpectempPojo.setLister(loginUser.getRealname());   // 制表
            matSpectempPojo.setListerid(loginUser.getUserid());    // 制表id  
            matSpectempPojo.setTenantid(loginUser.getTenantid());   //租户id
            matSpectempPojo.setModifydate(new Date());   //修改时间
//            matSpectempPojo.setAssessor(""); // 审核员
//            matSpectempPojo.setAssessorid(""); // 审核员id
//            matSpectempPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matSpectempService.update(matSpectempPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除MI模板", notes = "删除MI模板", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecTemp.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matSpectempService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "审核MI模板", notes = "审核MI模板", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecTemp.Approval")
    public R<MatSpectempPojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatSpectempPojo matSpectempPojo = this.matSpectempService.getEntity(key, loginUser.getTenantid());
            if (matSpectempPojo.getAssessor().equals("")) {
                matSpectempPojo.setAssessor(loginUser.getRealname()); //审核员
                matSpectempPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                matSpectempPojo.setAssessor(""); //审核员
                matSpectempPojo.setAssessorid(""); //审核员
            }
            matSpectempPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matSpectempService.approval(matSpectempPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SpecTemp.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatSpectempPojo matSpectempPojo = this.matSpectempService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matSpectempPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

