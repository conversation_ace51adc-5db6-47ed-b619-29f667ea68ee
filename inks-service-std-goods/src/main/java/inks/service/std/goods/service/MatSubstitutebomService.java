package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSubstitutebomPojo;

import java.util.List;

/**
 * 替代料BOM表(MatSubstitutebom)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-08 10:11:57
 */
public interface MatSubstitutebomService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstitutebomPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSubstitutebomPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSubstitutebomPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSubstitutebomPojo 实例对象
     * @return 实例对象
     */
    MatSubstitutebomPojo insert(MatSubstitutebomPojo matSubstitutebomPojo);

    /**
     * 修改数据
     *
     * @param matSubstitutebompojo 实例对象
     * @return 实例对象
     */
    MatSubstitutebomPojo update(MatSubstitutebomPojo matSubstitutebompojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSubstitutebompojo 实例对象
     * @return 实例对象
     */
    MatSubstitutebomPojo clearNull(MatSubstitutebomPojo matSubstitutebompojo);
}
