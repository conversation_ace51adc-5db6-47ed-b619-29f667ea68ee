package inks.service.std.goods.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatGoodsunitPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 货品单位(Mat_GoodsUnit)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-08 11:18:20
 */
public interface MatGoodsunitService {

    MatGoodsunitPojo getEntity(String key,String tid);

    PageInfo<MatGoodsunitPojo> getPageList(QueryParam queryParam);

    MatGoodsunitPojo insert(MatGoodsunitPojo matGoodsunitPojo);

    MatGoodsunitPojo update(MatGoodsunitPojo matGoodsunitpojo);

    int delete(String key,String tid);

    List<MatGoodsunitPojo> getList(Integer type,String tid);
}
