package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecitemPojo;

import java.util.List;

/**
 * 工艺项目(MatSpecitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-16 10:38:45
 */
public interface MatSpecitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSpecitemPojo 实例对象
     * @return 实例对象
     */
    MatSpecitemPojo insert(MatSpecitemPojo matSpecitemPojo);

    /**
     * 修改数据
     *
     * @param matSpecitempojo 实例对象
     * @return 实例对象
     */
    MatSpecitemPojo update(MatSpecitemPojo matSpecitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSpecitempojo 实例对象
     * @return 实例对象
     */
    MatSpecitemPojo clearNull(MatSpecitemPojo matSpecitempojo);
}
