package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSubstituteitemEntity;
import inks.service.std.goods.domain.pojo.MatSubstituteitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 替代料项目(MatSubstituteitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-06 11:08:38
 */
 @Mapper
public interface MatSubstituteitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstituteitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSubstituteitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSubstituteitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matSubstituteitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSubstituteitemEntity matSubstituteitemEntity);

    
    /**
     * 修改数据
     *
     * @param matSubstituteitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatSubstituteitemEntity matSubstituteitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

