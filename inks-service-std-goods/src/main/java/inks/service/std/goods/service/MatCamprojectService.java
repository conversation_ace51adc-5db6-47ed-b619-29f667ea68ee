package inks.service.std.goods.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatCamprojectPojo;
import com.github.pagehelper.PageInfo;

/**
 * 工程事务(Mat_CamProject)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-21 09:17:15
 */
public interface MatCamprojectService {

    MatCamprojectPojo getEntity(String key,String tid);

    PageInfo<MatCamprojectPojo> getPageList(QueryParam queryParam);

    MatCamprojectPojo insert(MatCamprojectPojo matCamprojectPojo);

    MatCamprojectPojo update(MatCamprojectPojo matCamprojectpojo);

    int delete(String key,String tid);

     MatCamprojectPojo approval(MatCamprojectPojo matCamprojectPojo);
}
