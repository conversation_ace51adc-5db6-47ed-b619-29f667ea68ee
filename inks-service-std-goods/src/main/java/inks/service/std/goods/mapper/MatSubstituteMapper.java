package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSubstituteEntity;
import inks.service.std.goods.domain.pojo.MatSubstitutePojo;
import inks.service.std.goods.domain.pojo.MatSubstituteitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 替代品管理(MatSubstitute)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-06 11:17:33
 */
@Mapper
public interface MatSubstituteMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstitutePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSubstituteitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSubstitutePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSubstituteEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSubstituteEntity matSubstituteEntity);


    /**
     * 修改数据
     *
     * @param matSubstituteEntity 实例对象
     * @return 影响行数
     */
    int update(MatSubstituteEntity matSubstituteEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matSubstitutePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(MatSubstitutePojo matSubstitutePojo);

    /**
     * 查询 被删除的Bom
     *
     * @param matSubstitutePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelBomIds(MatSubstitutePojo matSubstitutePojo);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstitutePojo getEntityByGoodsid(@Param("key") String key, @Param("tid") String tid);

    MatSubstitutePojo chkSubstitute(@Param("mkey") String mkey, @Param("skey") String skey, @Param("bomid") String bomid, @Param("tid") String tid);
}

