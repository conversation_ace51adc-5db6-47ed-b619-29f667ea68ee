package inks.service.std.goods.domain.pojo;


/**
 * PCB画图 规则pojo
 *
 * <AUTHOR>
 */
public class DrawVcutMapLinePojo {


    /**
     * 起始点X坐标
     */
    private int orgx;

    /**
     * 起始点Y坐标
     */
    private int orgy;

    /**
     * 起始点X坐标
     */
    private int endx;

    /**
     * 起始点Y坐标
     */
    private int endy;


    public int getOrgx() {
        return orgx;
    }

    public void setOrgx(int orgx) {
        this.orgx = orgx;
    }

    public int getOrgy() {
        return orgy;
    }

    public void setOrgy(int orgy) {
        this.orgy = orgy;
    }

    public int getEndx() {
        return endx;
    }

    public void setEndx(int endx) {
        this.endx = endx;
    }

    public int getEndy() {
        return endy;
    }

    public void setEndy(int endy) {
        this.endy = endy;
    }
}
