package inks.service.std.goods.domain;

import java.io.Serializable;

/**
 * 工艺流程排版(MatSpecdraw)Entity
 *
 * <AUTHOR>
 * @since 2023-02-02 14:49:45
 */
public class MatSpecdrawEntity implements Serializable {
    private static final long serialVersionUID = 757442229485741115L;
    // id
    private String id;
    // Pid
    private String pid;
    // 工序id
    private String wpid;
    // 工序编码
    private String wpcode;
    // 工序名称
    private String wpname;
    // 类型
    private String drawtype;
    // 图片标题
    private String drawtitle;
    // 图片Base64
    private String drawimage;
    // 失量Json
    private String drawjson;
    // 图片Url
    private String drawurl;
    // 内嵌图稿
    private Integer insidemark;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 工序id
    public String getWpid() {
        return wpid;
    }

    public void setWpid(String wpid) {
        this.wpid = wpid;
    }

    // 工序编码
    public String getWpcode() {
        return wpcode;
    }

    public void setWpcode(String wpcode) {
        this.wpcode = wpcode;
    }

    // 工序名称
    public String getWpname() {
        return wpname;
    }

    public void setWpname(String wpname) {
        this.wpname = wpname;
    }

    // 类型
    public String getDrawtype() {
        return drawtype;
    }

    public void setDrawtype(String drawtype) {
        this.drawtype = drawtype;
    }

    // 图片标题
    public String getDrawtitle() {
        return drawtitle;
    }

    public void setDrawtitle(String drawtitle) {
        this.drawtitle = drawtitle;
    }

    // 图片Base64
    public String getDrawimage() {
        return drawimage;
    }

    public void setDrawimage(String drawimage) {
        this.drawimage = drawimage;
    }

    // 失量Json
    public String getDrawjson() {
        return drawjson;
    }

    public void setDrawjson(String drawjson) {
        this.drawjson = drawjson;
    }

    // 图片Url
    public String getDrawurl() {
        return drawurl;
    }

    public void setDrawurl(String drawurl) {
        this.drawurl = drawurl;
    }

    // 内嵌图稿
    public Integer getInsidemark() {
        return insidemark;
    }

    public void setInsidemark(Integer insidemark) {
        this.insidemark = insidemark;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

