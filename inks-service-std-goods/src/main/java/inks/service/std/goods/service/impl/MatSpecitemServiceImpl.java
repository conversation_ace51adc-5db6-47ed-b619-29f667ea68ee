package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecitemEntity;
import inks.service.std.goods.domain.pojo.MatSpecitemPojo;
import inks.service.std.goods.mapper.MatSpecitemMapper;
import inks.service.std.goods.service.MatSpecitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工艺项目(MatSpecitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-16 10:38:45
 */
@Service("matSpecitemService")
public class MatSpecitemServiceImpl implements MatSpecitemService {
    @Resource
    private MatSpecitemMapper matSpecitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecitemPojo getEntity(String key, String tid) {
        return this.matSpecitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecitemPojo> lst = matSpecitemMapper.getPageList(queryParam);
            PageInfo<MatSpecitemPojo> pageInfo = new PageInfo<MatSpecitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSpecitemPojo> getList(String Pid, String tid) {
        try {
            List<MatSpecitemPojo> lst = matSpecitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSpecitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecitemPojo insert(MatSpecitemPojo matSpecitemPojo) {
        //初始化item的NULL
        MatSpecitemPojo itempojo = this.clearNull(matSpecitemPojo);
        MatSpecitemEntity matSpecitemEntity = new MatSpecitemEntity();
        BeanUtils.copyProperties(itempojo, matSpecitemEntity);

        matSpecitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSpecitemEntity.setRevision(1);  //乐观锁
        this.matSpecitemMapper.insert(matSpecitemEntity);
        return this.getEntity(matSpecitemEntity.getId(), matSpecitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecitemPojo update(MatSpecitemPojo matSpecitemPojo) {
        MatSpecitemEntity matSpecitemEntity = new MatSpecitemEntity();
        BeanUtils.copyProperties(matSpecitemPojo, matSpecitemEntity);
        this.matSpecitemMapper.update(matSpecitemEntity);
        return this.getEntity(matSpecitemEntity.getId(), matSpecitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSpecitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSpecitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecitemPojo clearNull(MatSpecitemPojo matSpecitemPojo) {
        //初始化NULL字段
        if (matSpecitemPojo.getPid() == null) matSpecitemPojo.setPid("");
        if (matSpecitemPojo.getWpid() == null) matSpecitemPojo.setWpid("");
        if (matSpecitemPojo.getWpcode() == null) matSpecitemPojo.setWpcode("");
        if (matSpecitemPojo.getWpname() == null) matSpecitemPojo.setWpname("");
        if (matSpecitemPojo.getDescription() == null) matSpecitemPojo.setDescription("");
        if (matSpecitemPojo.getDetailjson() == null) matSpecitemPojo.setDetailjson("");
        if (matSpecitemPojo.getFlowcode() == null) matSpecitemPojo.setFlowcode("");
        if (matSpecitemPojo.getToolscode() == null) matSpecitemPojo.setToolscode("");
        if (matSpecitemPojo.getRemark() == null) matSpecitemPojo.setRemark("");
        if (matSpecitemPojo.getRownum() == null) matSpecitemPojo.setRownum(0);
        if (matSpecitemPojo.getCustom1() == null) matSpecitemPojo.setCustom1("");
        if (matSpecitemPojo.getCustom2() == null) matSpecitemPojo.setCustom2("");
        if (matSpecitemPojo.getCustom3() == null) matSpecitemPojo.setCustom3("");
        if (matSpecitemPojo.getCustom4() == null) matSpecitemPojo.setCustom4("");
        if (matSpecitemPojo.getCustom5() == null) matSpecitemPojo.setCustom5("");
        if (matSpecitemPojo.getCustom6() == null) matSpecitemPojo.setCustom6("");
        if (matSpecitemPojo.getCustom7() == null) matSpecitemPojo.setCustom7("");
        if (matSpecitemPojo.getCustom8() == null) matSpecitemPojo.setCustom8("");
        if (matSpecitemPojo.getCustom9() == null) matSpecitemPojo.setCustom9("");
        if (matSpecitemPojo.getCustom10() == null) matSpecitemPojo.setCustom10("");
        if (matSpecitemPojo.getTenantid() == null) matSpecitemPojo.setTenantid("");
        if (matSpecitemPojo.getTenantname() == null) matSpecitemPojo.setTenantname("");
        if (matSpecitemPojo.getRevision() == null) matSpecitemPojo.setRevision(0);
        return matSpecitemPojo;
    }
}
