package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSupplierEntity;
import inks.service.std.goods.domain.pojo.MatSupplierPojo;
import inks.service.std.goods.mapper.MatSupplierMapper;
import inks.service.std.goods.service.MatSupplierService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 货品供应(MatSupplier)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-03 19:51:11
 */
@Service("matSupplierService")
public class MatSupplierServiceImpl implements MatSupplierService {
    @Resource
    private MatSupplierMapper matSupplierMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSupplierPojo getEntity(String key, String tid) {
        return this.matSupplierMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSupplierPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSupplierPojo> lst = matSupplierMapper.getPageList(queryParam);
            PageInfo<MatSupplierPojo> pageInfo = new PageInfo<MatSupplierPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matSupplierPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSupplierPojo insert(MatSupplierPojo matSupplierPojo) {
        //初始化NULL字段
        if (matSupplierPojo.getGoodsid() == null) matSupplierPojo.setGoodsid("");
        if (matSupplierPojo.getGoodsuid() == null) matSupplierPojo.setGoodsuid("");
        if (matSupplierPojo.getGoodsname() == null) matSupplierPojo.setGoodsname("");
        if (matSupplierPojo.getGoodsspec() == null) matSupplierPojo.setGoodsspec("");
        if (matSupplierPojo.getGoodsunit() == null) matSupplierPojo.setGoodsunit("");
        if (matSupplierPojo.getGroupid() == null) matSupplierPojo.setGroupid("");
        if (matSupplierPojo.getGroupuid() == null) matSupplierPojo.setGroupuid("");
        if (matSupplierPojo.getGroupname() == null) matSupplierPojo.setGroupname("");
        if (matSupplierPojo.getStatecode() == null) matSupplierPojo.setStatecode("");
        if (matSupplierPojo.getStatedate() == null) matSupplierPojo.setStatedate(new Date());
        if (matSupplierPojo.getOperator() == null) matSupplierPojo.setOperator("");
        if (matSupplierPojo.getOperatorid() == null) matSupplierPojo.setOperatorid("");
        if (matSupplierPojo.getRemark() == null) matSupplierPojo.setRemark("");
        if (matSupplierPojo.getClosed() == null) matSupplierPojo.setClosed(0);
        if (matSupplierPojo.getRownum() == null) matSupplierPojo.setRownum(0);
        if (matSupplierPojo.getCreateby() == null) matSupplierPojo.setCreateby("");
        if (matSupplierPojo.getCreatebyid() == null) matSupplierPojo.setCreatebyid("");
        if (matSupplierPojo.getCreatedate() == null) matSupplierPojo.setCreatedate(new Date());
        if (matSupplierPojo.getLister() == null) matSupplierPojo.setLister("");
        if (matSupplierPojo.getListerid() == null) matSupplierPojo.setListerid("");
        if (matSupplierPojo.getModifydate() == null) matSupplierPojo.setModifydate(new Date());
        if (matSupplierPojo.getCustom1() == null) matSupplierPojo.setCustom1("");
        if (matSupplierPojo.getCustom2() == null) matSupplierPojo.setCustom2("");
        if (matSupplierPojo.getCustom3() == null) matSupplierPojo.setCustom3("");
        if (matSupplierPojo.getCustom4() == null) matSupplierPojo.setCustom4("");
        if (matSupplierPojo.getCustom5() == null) matSupplierPojo.setCustom5("");
        if (matSupplierPojo.getCustom6() == null) matSupplierPojo.setCustom6("");
        if (matSupplierPojo.getCustom7() == null) matSupplierPojo.setCustom7("");
        if (matSupplierPojo.getCustom8() == null) matSupplierPojo.setCustom8("");
        if (matSupplierPojo.getCustom9() == null) matSupplierPojo.setCustom9("");
        if (matSupplierPojo.getCustom10() == null) matSupplierPojo.setCustom10("");
        if (matSupplierPojo.getTenantid() == null) matSupplierPojo.setTenantid("");
        if (matSupplierPojo.getRevision() == null) matSupplierPojo.setRevision(0);
        MatSupplierEntity matSupplierEntity = new MatSupplierEntity();
        BeanUtils.copyProperties(matSupplierPojo, matSupplierEntity);

        matSupplierEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSupplierEntity.setRevision(1);  //乐观锁
        this.matSupplierMapper.insert(matSupplierEntity);
        return this.getEntity(matSupplierEntity.getId(), matSupplierEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSupplierPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSupplierPojo update(MatSupplierPojo matSupplierPojo) {
        MatSupplierEntity matSupplierEntity = new MatSupplierEntity();
        BeanUtils.copyProperties(matSupplierPojo, matSupplierEntity);
        this.matSupplierMapper.update(matSupplierEntity);
        return this.getEntity(matSupplierEntity.getId(), matSupplierEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSupplierMapper.delete(key, tid);
    }


}
