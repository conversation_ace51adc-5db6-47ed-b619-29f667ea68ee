package inks.service.std.goods.domain.pojo;

import java.util.List;

/**
 * PCB画图 pojo
 *
 * <AUTHOR>
 */
public class DrawVcutMapPojo {
    /**
     * 名称
     */
    private String name = "";

    /**
     * 背景总宽度
     */
    private int sizex;

    /**
     * 背景总高度
     */
    private int sizey;

    /**
     * 具体规则
     */
    private List<DrawVcutMapLinePojo> line;

    /**
     * 具体规则
     */
    private List<DrawVcutMapTextPojo> text;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSizex() {
        return sizex;
    }

    public void setSizex(int sizex) {
        this.sizex = sizex;
    }

    public int getSizey() {
        return sizey;
    }

    public void setSizey(int sizey) {
        this.sizey = sizey;
    }

    public List<DrawVcutMapLinePojo> getLine() {
        return line;
    }

    public void setLine(List<DrawVcutMapLinePojo> line) {
        this.line = line;
    }

    public List<DrawVcutMapTextPojo> getText() {
        return text;
    }

    public void setText(List<DrawVcutMapTextPojo> text) {
        this.text = text;
    }
}
