package inks.service.std.goods.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatGoodsunitPojo;
import inks.service.std.goods.service.MatGoodsunitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 货品单位(Mat_GoodsUnit)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-08 11:18:17
 */
@RestController
@RequestMapping("D91M01S5")
@Api(tags = "D91M01S5:货品单位")
public class D91M01S5Controller extends MatGoodsunitController {
    @Resource
    private TokenService tokenService;
    @Resource
    private MatGoodsunitService matGoodsunitService;


    @ApiOperation(value = "按条件分页查询 type=单位适用类型（0：通用，1：产品/半成品，2：物料）", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_GoodsUnit.List")
    public R<List<MatGoodsunitPojo>> getList(@RequestParam(defaultValue = "0") Integer type) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matGoodsunitService.getList(type, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
