package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSubstitutebomEntity;
import inks.service.std.goods.domain.pojo.MatSubstitutebomPojo;
import inks.service.std.goods.mapper.MatSubstitutebomMapper;
import inks.service.std.goods.service.MatSubstitutebomService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 替代料BOM表(MatSubstitutebom)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-08 10:11:57
 */
@Service("matSubstitutebomService")
public class MatSubstitutebomServiceImpl implements MatSubstitutebomService {
    @Resource
    private MatSubstitutebomMapper matSubstitutebomMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSubstitutebomPojo getEntity(String key, String tid) {
        return this.matSubstitutebomMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSubstitutebomPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSubstitutebomPojo> lst = matSubstitutebomMapper.getPageList(queryParam);
            PageInfo<MatSubstitutebomPojo> pageInfo = new PageInfo<MatSubstitutebomPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSubstitutebomPojo> getList(String Pid, String tid) {
        try {
            List<MatSubstitutebomPojo> lst = matSubstitutebomMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSubstitutebomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSubstitutebomPojo insert(MatSubstitutebomPojo matSubstitutebomPojo) {
        //初始化item的NULL
        MatSubstitutebomPojo itempojo = this.clearNull(matSubstitutebomPojo);
        MatSubstitutebomEntity matSubstitutebomEntity = new MatSubstitutebomEntity();
        BeanUtils.copyProperties(itempojo, matSubstitutebomEntity);

        matSubstitutebomEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSubstitutebomEntity.setRevision(1);  //乐观锁
        this.matSubstitutebomMapper.insert(matSubstitutebomEntity);
        return this.getEntity(matSubstitutebomEntity.getId(), matSubstitutebomEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSubstitutebomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSubstitutebomPojo update(MatSubstitutebomPojo matSubstitutebomPojo) {
        MatSubstitutebomEntity matSubstitutebomEntity = new MatSubstitutebomEntity();
        BeanUtils.copyProperties(matSubstitutebomPojo, matSubstitutebomEntity);
        this.matSubstitutebomMapper.update(matSubstitutebomEntity);
        return this.getEntity(matSubstitutebomEntity.getId(), matSubstitutebomEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSubstitutebomMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSubstitutebomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSubstitutebomPojo clearNull(MatSubstitutebomPojo matSubstitutebomPojo) {
        //初始化NULL字段
        if (matSubstitutebomPojo.getPid() == null) matSubstitutebomPojo.setPid("");
        if (matSubstitutebomPojo.getBomid() == null) matSubstitutebomPojo.setBomid("");
        if (matSubstitutebomPojo.getBomtype() == null) matSubstitutebomPojo.setBomtype("");
        if (matSubstitutebomPojo.getGoodsid() == null) matSubstitutebomPojo.setGoodsid("");
        if (matSubstitutebomPojo.getItemcode() == null) matSubstitutebomPojo.setItemcode("");
        if (matSubstitutebomPojo.getItemname() == null) matSubstitutebomPojo.setItemname("");
        if (matSubstitutebomPojo.getItemspec() == null) matSubstitutebomPojo.setItemspec("");
        if (matSubstitutebomPojo.getItemunit() == null) matSubstitutebomPojo.setItemunit("");
        if (matSubstitutebomPojo.getMachuid() == null) matSubstitutebomPojo.setMachuid("");
        if (matSubstitutebomPojo.getMachitemid() == null) matSubstitutebomPojo.setMachitemid("");
        if (matSubstitutebomPojo.getMachgroupid() == null) matSubstitutebomPojo.setMachgroupid("");
        if (matSubstitutebomPojo.getClosed() == null) matSubstitutebomPojo.setClosed(0);
        if (matSubstitutebomPojo.getRownum() == null) matSubstitutebomPojo.setRownum(0);
        if (matSubstitutebomPojo.getRemark() == null) matSubstitutebomPojo.setRemark("");
        if (matSubstitutebomPojo.getCustom1() == null) matSubstitutebomPojo.setCustom1("");
        if (matSubstitutebomPojo.getCustom2() == null) matSubstitutebomPojo.setCustom2("");
        if (matSubstitutebomPojo.getCustom3() == null) matSubstitutebomPojo.setCustom3("");
        if (matSubstitutebomPojo.getCustom4() == null) matSubstitutebomPojo.setCustom4("");
        if (matSubstitutebomPojo.getCustom5() == null) matSubstitutebomPojo.setCustom5("");
        if (matSubstitutebomPojo.getCustom6() == null) matSubstitutebomPojo.setCustom6("");
        if (matSubstitutebomPojo.getCustom7() == null) matSubstitutebomPojo.setCustom7("");
        if (matSubstitutebomPojo.getCustom8() == null) matSubstitutebomPojo.setCustom8("");
        if (matSubstitutebomPojo.getCustom9() == null) matSubstitutebomPojo.setCustom9("");
        if (matSubstitutebomPojo.getCustom10() == null) matSubstitutebomPojo.setCustom10("");
        if (matSubstitutebomPojo.getTenantid() == null) matSubstitutebomPojo.setTenantid("");
        if (matSubstitutebomPojo.getRevision() == null) matSubstitutebomPojo.setRevision(0);
        return matSubstitutebomPojo;
    }
}
