package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatEcnEntity;
import inks.service.std.goods.domain.pojo.MatEcnPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料变更(MatEcn)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-29 15:02:15
 */
@Mapper
public interface MatEcnMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatEcnPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatEcnPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param matEcnEntity 实例对象
     * @return 影响行数
     */
    int insert(MatEcnEntity matEcnEntity);

    
    /**
     * 修改数据
     *
     * @param matEcnEntity 实例对象
     * @return 影响行数
     */
    int update(MatEcnEntity matEcnEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                                               /**
     * 修改数据
     *
     * @param matEcnEntity 实例对象
     * @return 影响行数
     */
    int approval(MatEcnEntity matEcnEntity);


    List<MatEcnPojo> getListByGoodsid(String goodsid, String tid);
}

