package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatCamprojectPojo;
import inks.service.std.goods.mapper.MatCamprojectMapper;
import inks.service.std.goods.service.MatCamprojectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 工程事务(Mat_CamProject)表控制层
 *
 * <AUTHOR>
 * @since 2024-08-21 09:17:15
 */
@RestController
@RequestMapping("D91M11B1")
@Api(tags = "D91M11B1:工程事务")
public class D91M11B1Controller extends MatCamprojectController {
    @Resource
    private MatCamprojectService matCamprojectService;
    @Resource
    private MatCamprojectMapper matCamprojectMapper;

    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "开始事务 赋值StartDate、Handler处理人 关联的销售订单EngStateText字段为“处理”", notes = "获取工程事务详细信息", produces = "application/json")
    @RequestMapping(value = "/startProject", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.List")
    @Transactional
    public R<MatCamprojectPojo> startProject(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatCamprojectPojo matCamprojectPojo = new MatCamprojectPojo();
            matCamprojectPojo.setId(key);
            matCamprojectPojo.setStartdate(new Date()); //开始日期
            matCamprojectPojo.setHandler(loginUser.getRealname()); //处理人
            matCamprojectPojo.setTenantid(loginUser.getTenantid()); //租户id
            // citeitemid是销售订单子表id 同步销售订单Item表的EngStateText字段 Mat_CamProject创建时，/cancelHandler“导入”，/startProject/cancelFinish"处理"，/finishProject"完成"
            MatCamprojectPojo entity = matCamprojectMapper.getEntity(key, loginUser.getTenantid());
            String citeitemid = entity.getCiteitemid();
            if (StringUtils.isNotBlank(citeitemid)) {
                matCamprojectMapper.syncMachItemEngStateText(citeitemid, "处理", loginUser.getTenantid());
            }
            return R.ok(this.matCamprojectService.update(matCamprojectPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 完成事务 赋值EndDate 关联的销售订单EngStateText字段为“完成”", notes = "获取工程事务详细信息", produces = "application/json")
    @RequestMapping(value = "/finishProject", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.List")
    @Transactional
    public R<MatCamprojectPojo> finishProject(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatCamprojectPojo matCamprojectPojo = new MatCamprojectPojo();
            matCamprojectPojo.setId(key);
            matCamprojectPojo.setEnddate(new Date());
            matCamprojectPojo.setTenantid(loginUser.getTenantid()); //租户id
            // citeitemid是销售订单子表id 同步销售订单Item表的EngStateText字段 Mat_CamProject创建时，/cancelHandler“导入”，/startProject/cancelFinish"处理"，/finishProject"完成"
            MatCamprojectPojo entity = matCamprojectMapper.getEntity(key, loginUser.getTenantid());
            String citeitemid = entity.getCiteitemid();
            if (StringUtils.isNotBlank(citeitemid)) {
                matCamprojectMapper.syncMachItemEngStateText(citeitemid, "完成", loginUser.getTenantid());
            }
            return R.ok(this.matCamprojectService.update(matCamprojectPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "撤销处理人 【开始日期】、【结束日期】、【接收人】清空，关联的销售订单EngStateText字段为“导入”", notes = "", produces = "application/json")
    @RequestMapping(value = "/cancelHandler", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.List")
    @Transactional
    public R<MatCamprojectPojo> cancelHandler(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            // citeitemid是销售订单子表id 同步销售订单Item表的EngStateText字段 Mat_CamProject创建时，/cancelHandler“导入”，/startProject/cancelFinish"处理"，/finishProject"完成"
            MatCamprojectPojo entity = matCamprojectMapper.getEntity(key, loginUser.getTenantid());
            String citeitemid = entity.getCiteitemid();
            if (StringUtils.isNotBlank(citeitemid)) {
                matCamprojectMapper.syncMachItemEngStateText(citeitemid, "导入", tid);
            }
            //【开始日期】、【结束日期】、【接收人】清空
            this.matCamprojectMapper.setNullStartDateEndDateHandler(key, tid);
            return R.ok(matCamprojectMapper.getEntity(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "撤销完成  【结束日期】清空， 关联的销售订单EngStateText字段为“处理”", notes = "", produces = "application/json")
    @RequestMapping(value = "/cancelFinish", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.List")
    @Transactional
    public R<MatCamprojectPojo> cancelFinish(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            // citeitemid是销售订单子表id 同步销售订单Item表的EngStateText字段 Mat_CamProject创建时，/cancelHandler“导入”，/startProject/cancelFinish"处理"，/finishProject"完成"
            MatCamprojectPojo entity = matCamprojectMapper.getEntity(key, loginUser.getTenantid());
            String citeitemid = entity.getCiteitemid();
            if (StringUtils.isNotBlank(citeitemid)) {
                matCamprojectMapper.syncMachItemEngStateText(citeitemid, "处理", tid);
            }
            //【结束日期】清空
            this.matCamprojectMapper.setNullEndDate(key, tid);
            return R.ok(matCamprojectMapper.getEntity(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询在线：即EndDate为空", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CamProject.List")
    public R<PageInfo<MatCamprojectPojo>> getOnlinePageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_CamProject.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_CamProject.EndDate is null ";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matCamprojectService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
