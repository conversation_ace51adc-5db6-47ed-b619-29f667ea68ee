package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatBomPojo;
import inks.service.std.goods.domain.pojo.MatBomitemPojo;
import inks.service.std.goods.domain.pojo.MatGoodsPojo;

import java.util.List;

/**
 * BOM子表(MatBomitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-08 10:21:21
 */
public interface MatBomitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBomitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatBomitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matBomitemPojo 实例对象
     * @return 实例对象
     */
    MatBomitemPojo insert(MatBomitemPojo matBomitemPojo);

    /**
     * 修改数据
     *
     * @param matBomitempojo 实例对象
     * @return 实例对象
     */
    MatBomitemPojo update(MatBomitemPojo matBomitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matBomitempojo 实例对象
     * @return 实例对象
     */
    MatBomitemPojo clearNull(MatBomitemPojo matBomitempojo);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<MatBomitemPojo> getListByGoodsid(String key, String pid, String tid);

    List<MatBomPojo> getListByItemGoodsid(String key, String tid);

    List<MatGoodsPojo> getParentBom(String key, String tenantid);
}
