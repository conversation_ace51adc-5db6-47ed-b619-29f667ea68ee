package inks.service.std.goods.domain.pojo;


/**
 * PCB画图 规则pojo
 *
 * <AUTHOR>
 */
public class DrawPnlMapItemPojo {


    /**
     * 文本内容
     */
    private String label = "";

    /**
     * 宽度
     */
    private double setx;

    /**
     * 高度
     */
    private double sety;

    /**
     * 起始点X坐标
     */
    private double orgx;

    /**
     * 起始点Y坐标
     */
    private double orgy;

    /**
     * 横向数量
     */
    private int cpx;

    /**
     * 竖向数量
     */
    private int cpy;

    /**
     * 横向间距
     */
    private double spx;

    /**
     * 竖向间距
     */
    private double spy;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public double getOrgx() {
        return orgx;
    }

    public void setOrgx(double orgx) {
        this.orgx = orgx;
    }

    public double getOrgy() {
        return orgy;
    }

    public void setOrgy(double orgy) {
        this.orgy = orgy;
    }


    public double getSetx() {
        return setx;
    }

    public void setSetx(double setx) {
        this.setx = setx;
    }

    public double getSety() {
        return sety;
    }

    public void setSety(double sety) {
        this.sety = sety;
    }

    public int getCpx() {
        return cpx;
    }

    public void setCpx(int cpx) {
        this.cpx = cpx;
    }

    public int getCpy() {
        return cpy;
    }

    public void setCpy(int cpy) {
        this.cpy = cpy;
    }

    public double getSpx() {
        return spx;
    }

    public void setSpx(double spx) {
        this.spx = spx;
    }

    public double getSpy() {
        return spy;
    }

    public void setSpy(double spy) {
        this.spy = spy;
    }


}
