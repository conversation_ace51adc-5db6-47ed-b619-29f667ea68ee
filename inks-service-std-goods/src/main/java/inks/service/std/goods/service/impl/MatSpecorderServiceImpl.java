package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecorderEntity;
import inks.service.std.goods.domain.MatSpecorderdrawEntity;
import inks.service.std.goods.domain.MatSpecorderitemEntity;
import inks.service.std.goods.domain.pojo.MatSpecorderPojo;
import inks.service.std.goods.domain.pojo.MatSpecorderdrawPojo;
import inks.service.std.goods.domain.pojo.MatSpecorderitemPojo;
import inks.service.std.goods.domain.pojo.MatSpecorderitemdetailPojo;
import inks.service.std.goods.mapper.MatSpecorderMapper;
import inks.service.std.goods.mapper.MatSpecorderdrawMapper;
import inks.service.std.goods.mapper.MatSpecorderitemMapper;
import inks.service.std.goods.service.MatSpecorderService;
import inks.service.std.goods.service.MatSpecorderdrawService;
import inks.service.std.goods.service.MatSpecorderitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 订单工艺卡(MatSpecorder)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-16 14:42:36
 */
@Service("matSpecorderService")
public class MatSpecorderServiceImpl implements MatSpecorderService {
    @Resource
    private MatSpecorderMapper matSpecorderMapper;

    @Resource
    private MatSpecorderitemMapper matSpecorderitemMapper;
    @Resource
    private MatSpecorderdrawMapper matSpecorderdrawMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private MatSpecorderitemService matSpecorderitemService;
    @Resource
    private MatSpecorderdrawService matSpecorderdrawService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecorderPojo getEntity(String key, String tid) {
        return this.matSpecorderMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecorderitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecorderitemdetailPojo> lst = matSpecorderMapper.getPageList(queryParam);
            PageInfo<MatSpecorderitemdetailPojo> pageInfo = new PageInfo<MatSpecorderitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecorderPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatSpecorderPojo matSpecorderPojo = this.matSpecorderMapper.getEntity(key, tid);
            //读取子表
            matSpecorderPojo.setItem(matSpecorderitemMapper.getList(matSpecorderPojo.getId(), matSpecorderPojo.getTenantid()));
            //读取draw子表
            matSpecorderPojo.setDraw(matSpecorderdrawMapper.getList(matSpecorderPojo.getId(), matSpecorderPojo.getTenantid()));
            return matSpecorderPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecorderPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecorderPojo> lst = matSpecorderMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表和draw子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matSpecorderitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setDraw(matSpecorderdrawMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatSpecorderPojo> pageInfo = new PageInfo<MatSpecorderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecorderPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecorderPojo> lst = matSpecorderMapper.getPageTh(queryParam);
            PageInfo<MatSpecorderPojo> pageInfo = new PageInfo<MatSpecorderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matSpecorderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecorderPojo insert(MatSpecorderPojo matSpecorderPojo) {
//初始化NULL字段
        if (matSpecorderPojo.getRefno() == null) matSpecorderPojo.setRefno("");
        if (matSpecorderPojo.getBilldate() == null) matSpecorderPojo.setBilldate(new Date());
        if (matSpecorderPojo.getBilltype() == null) matSpecorderPojo.setBilltype("");
        if (matSpecorderPojo.getBilltitle() == null) matSpecorderPojo.setBilltitle("");
        if (matSpecorderPojo.getMachuid() == null) matSpecorderPojo.setMachuid("");
        if (matSpecorderPojo.getMachitemid() == null) matSpecorderPojo.setMachitemid("");
        if (matSpecorderPojo.getMachgroupid() == null) matSpecorderPojo.setMachgroupid("");
        if (matSpecorderPojo.getGoodsid() == null) matSpecorderPojo.setGoodsid("");
        if (matSpecorderPojo.getItemcode() == null) matSpecorderPojo.setItemcode("");
        if (matSpecorderPojo.getItemname() == null) matSpecorderPojo.setItemname("");
        if (matSpecorderPojo.getItemspec() == null) matSpecorderPojo.setItemspec("");
        if (matSpecorderPojo.getItemunit() == null) matSpecorderPojo.setItemunit("");
        if (matSpecorderPojo.getPnlx() == null) matSpecorderPojo.setPnlx(0D);
        if (matSpecorderPojo.getPnly() == null) matSpecorderPojo.setPnly(0D);
        if (matSpecorderPojo.getPnl2pcs() == null) matSpecorderPojo.setPnl2pcs(0);
        if (matSpecorderPojo.getPnlbx() == null) matSpecorderPojo.setPnlbx(0D);
        if (matSpecorderPojo.getPnlby() == null) matSpecorderPojo.setPnlby(0D);
        if (matSpecorderPojo.getPnlb2pcs() == null) matSpecorderPojo.setPnlb2pcs(0);
        if (matSpecorderPojo.getOperator() == null) matSpecorderPojo.setOperator("");
        if (matSpecorderPojo.getOperatorid() == null) matSpecorderPojo.setOperatorid("");
        if (matSpecorderPojo.getQuickkey() == null) matSpecorderPojo.setQuickkey("");
        if (matSpecorderPojo.getAttributejson() == null) matSpecorderPojo.setAttributejson("{}");
        if (matSpecorderPojo.getSummary() == null) matSpecorderPojo.setSummary("");
        if (matSpecorderPojo.getEnabledmark() == null) matSpecorderPojo.setEnabledmark(0);
        if (matSpecorderPojo.getVersionnum() == null) matSpecorderPojo.setVersionnum("");
        if (matSpecorderPojo.getCreateby() == null) matSpecorderPojo.setCreateby("");
        if (matSpecorderPojo.getCreatebyid() == null) matSpecorderPojo.setCreatebyid("");
        if (matSpecorderPojo.getCreatedate() == null) matSpecorderPojo.setCreatedate(new Date());
        if (matSpecorderPojo.getLister() == null) matSpecorderPojo.setLister("");
        if (matSpecorderPojo.getListerid() == null) matSpecorderPojo.setListerid("");
        if (matSpecorderPojo.getModifydate() == null) matSpecorderPojo.setModifydate(new Date());
        if (matSpecorderPojo.getAssessor() == null) matSpecorderPojo.setAssessor("");
        if (matSpecorderPojo.getAssessorid() == null) matSpecorderPojo.setAssessorid("");
        if (matSpecorderPojo.getAssessdate() == null) matSpecorderPojo.setAssessdate(new Date());
        if (matSpecorderPojo.getCustom1() == null) matSpecorderPojo.setCustom1("");
        if (matSpecorderPojo.getCustom2() == null) matSpecorderPojo.setCustom2("");
        if (matSpecorderPojo.getCustom3() == null) matSpecorderPojo.setCustom3("");
        if (matSpecorderPojo.getCustom4() == null) matSpecorderPojo.setCustom4("");
        if (matSpecorderPojo.getCustom5() == null) matSpecorderPojo.setCustom5("");
        if (matSpecorderPojo.getCustom6() == null) matSpecorderPojo.setCustom6("");
        if (matSpecorderPojo.getCustom7() == null) matSpecorderPojo.setCustom7("");
        if (matSpecorderPojo.getCustom8() == null) matSpecorderPojo.setCustom8("");
        if (matSpecorderPojo.getCustom9() == null) matSpecorderPojo.setCustom9("");
        if (matSpecorderPojo.getCustom10() == null) matSpecorderPojo.setCustom10("");
        if (matSpecorderPojo.getTenantid() == null) matSpecorderPojo.setTenantid("");
        if (matSpecorderPojo.getTenantname() == null) matSpecorderPojo.setTenantname("");
        if (matSpecorderPojo.getRevision() == null) matSpecorderPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatSpecorderEntity matSpecorderEntity = new MatSpecorderEntity();
        BeanUtils.copyProperties(matSpecorderPojo, matSpecorderEntity);

        //设置id和新建日期
        matSpecorderEntity.setId(id);
        matSpecorderEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matSpecorderMapper.insert(matSpecorderEntity);
        //Item子表处理
        List<MatSpecorderitemPojo> lst = matSpecorderPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatSpecorderitemPojo itemPojo = this.matSpecorderitemService.clearNull(lst.get(i));
                MatSpecorderitemEntity matSpecorderitemEntity = new MatSpecorderitemEntity();
                BeanUtils.copyProperties(itemPojo, matSpecorderitemEntity);
                //设置id和Pid
                matSpecorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matSpecorderitemEntity.setPid(id);
                matSpecorderitemEntity.setTenantid(matSpecorderPojo.getTenantid());
                matSpecorderitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matSpecorderitemMapper.insert(matSpecorderitemEntity);
            }
        }
        //Draw子表处理
        List<MatSpecorderdrawPojo> lstDraw = matSpecorderPojo.getDraw();
        if (lstDraw != null) {
            //循环每个Draw子表
            for (int i = 0; i < lstDraw.size(); i++) {
                //初始化Draw的NULL
                MatSpecorderdrawPojo drawPojo = this.matSpecorderdrawService.clearNull(lstDraw.get(i));
                MatSpecorderdrawEntity matSpecorderdrawEntity = new MatSpecorderdrawEntity();
                BeanUtils.copyProperties(drawPojo, matSpecorderdrawEntity);
                //设置id和Pid
                matSpecorderdrawEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matSpecorderdrawEntity.setPid(id);
                matSpecorderdrawEntity.setTenantid(matSpecorderPojo.getTenantid());
                matSpecorderdrawEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matSpecorderdrawMapper.insert(matSpecorderdrawEntity);
            }
        }

        // 同步订单工艺卡的id,refno,specstated到销售订单子表
        this.matSpecorderMapper.updateMachItemSpec(id, matSpecorderPojo.getRefno(), 0, matSpecorderPojo.getMachitemid(), matSpecorderPojo.getTenantid());
        //返回Bill实例
        return this.getBillEntity(matSpecorderEntity.getId(), matSpecorderEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecorderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecorderPojo update(MatSpecorderPojo matSpecorderPojo) {
        //主表更改
        MatSpecorderEntity matSpecorderEntity = new MatSpecorderEntity();
        BeanUtils.copyProperties(matSpecorderPojo, matSpecorderEntity);
        this.matSpecorderMapper.update(matSpecorderEntity);
        if (matSpecorderPojo.getItem() != null) {
            //Item子表处理
            List<MatSpecorderitemPojo> lst = matSpecorderPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = matSpecorderMapper.getDelItemIds(matSpecorderPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.matSpecorderitemMapper.delete(lstDelIds.get(i), matSpecorderEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    MatSpecorderitemEntity matSpecorderitemEntity = new MatSpecorderitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        MatSpecorderitemPojo itemPojo = this.matSpecorderitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, matSpecorderitemEntity);
                        //设置id和Pid
                        matSpecorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matSpecorderitemEntity.setPid(matSpecorderEntity.getId());  // 主表 id
                        matSpecorderitemEntity.setTenantid(matSpecorderPojo.getTenantid());   // 租户id
                        matSpecorderitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matSpecorderitemMapper.insert(matSpecorderitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), matSpecorderitemEntity);
                        matSpecorderitemEntity.setTenantid(matSpecorderPojo.getTenantid());
                        this.matSpecorderitemMapper.update(matSpecorderitemEntity);
                    }
                }
            }
        }
        //Draw子表处理
        if (matSpecorderPojo.getDraw() != null) {
            List<MatSpecorderdrawPojo> lstDraw = matSpecorderPojo.getDraw();
            //获取被删除的Draw
            List<String> lstDelIds = matSpecorderMapper.getDelDrawIds(matSpecorderPojo);
            if (lstDelIds != null) {
                //循环每个删除Draw子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.matSpecorderdrawMapper.delete(lstDelIds.get(i), matSpecorderEntity.getTenantid());
                }
            }
            if (lstDraw != null) {
                //循环每个Draw子表
                for (int i = 0; i < lstDraw.size(); i++) {
                    MatSpecorderdrawEntity matSpecorderdrawEntity = new MatSpecorderdrawEntity();
                    if ("".equals(lstDraw.get(i).getId()) || lstDraw.get(i).getId() == null) {
                        //初始化Draw的NULL
                        MatSpecorderdrawPojo drawPojo = this.matSpecorderdrawService.clearNull(lstDraw.get(i));
                        BeanUtils.copyProperties(drawPojo, matSpecorderdrawEntity);
                        //设置id和Pid
                        matSpecorderdrawEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // Draw id
                        matSpecorderdrawEntity.setPid(matSpecorderEntity.getId());  // 主表 id
                        matSpecorderdrawEntity.setTenantid(matSpecorderPojo.getTenantid());   // 租户id
                        matSpecorderdrawEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matSpecorderdrawMapper.insert(matSpecorderdrawEntity);
                    } else {
                        BeanUtils.copyProperties(lstDraw.get(i), matSpecorderdrawEntity);
                        matSpecorderdrawEntity.setTenantid(matSpecorderPojo.getTenantid());
                        this.matSpecorderdrawMapper.update(matSpecorderdrawEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(matSpecorderEntity.getId(), matSpecorderEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        MatSpecorderPojo matSpecorderPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatSpecorderitemPojo> lst = matSpecorderPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (MatSpecorderitemPojo matSpecorderitemPojo : lst) {
                this.matSpecorderitemMapper.delete(matSpecorderitemPojo.getId(), tid);
            }
        }
        //Draw子表处理
        List<MatSpecorderdrawPojo> lstDraw = matSpecorderPojo.getDraw();
        if (lstDraw != null) {
            //循环每个删除Draw子表
            for (MatSpecorderdrawPojo matSpecorderdrawPojo : lstDraw) {
                this.matSpecorderdrawMapper.delete(matSpecorderdrawPojo.getId(), tid);
            }
        }
        // 同步订单工艺卡的id,refno,specstated到销售订单子表(清空)
        this.matSpecorderMapper.updateMachItemSpec("", "", 0, matSpecorderPojo.getMachitemid(), matSpecorderPojo.getTenantid());

        this.matSpecorderMapper.delete(key, tid);
        return matSpecorderPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param matSpecorderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSpecorderPojo approval(MatSpecorderPojo matSpecorderPojo) {
        //主表更改
        MatSpecorderEntity matSpecorderEntity = new MatSpecorderEntity();
        BeanUtils.copyProperties(matSpecorderPojo, matSpecorderEntity);
        this.matSpecorderMapper.approval(matSpecorderEntity);
        //返回Bill实例
        return this.getBillEntity(matSpecorderEntity.getId(), matSpecorderEntity.getTenantid());
    }

}
