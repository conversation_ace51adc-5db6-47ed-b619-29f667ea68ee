package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.exception.WarnException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.goods.domain.MatGoodsEntity;
import inks.service.std.goods.domain.pojo.MatGoodsPojo;
import inks.service.std.goods.mapper.MatGoodsMapper;
import inks.service.std.goods.mapper.MatGoodsunitMapper;
import inks.service.std.goods.service.MatGoodsService;
import inks.service.std.goods.service.MatGoodsunitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 货品信息(MatGoods)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-14 08:36:14
 */
@Service("matGoodsService")
public class MatGoodsServiceImpl implements MatGoodsService {
    @Resource
    private MatGoodsMapper matGoodsMapper;
    @Resource
    private MatGoodsunitMapper matGoodsunitMapper;
    @Resource
    private MatGoodsunitService matGoodsunitService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatGoodsPojo getEntity(String key, String tid) {
        MatGoodsPojo matGoodsPojo = this.matGoodsMapper.getEntity(key, tid);
        return matGoodsPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatGoodsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGoodsPojo> lst = matGoodsMapper.getPageList(queryParam);
            PageInfo<MatGoodsPojo> pageInfo = new PageInfo<MatGoodsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matGoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatGoodsPojo insert(MatGoodsPojo matGoodsPojo, String allow, Integer warn) {
        //初始化NULL字段
        if (matGoodsPojo.getGoodsuid() == null) matGoodsPojo.setGoodsuid("");
        if (matGoodsPojo.getGoodsname() == null) matGoodsPojo.setGoodsname("");
        if (matGoodsPojo.getGoodsspec() == null) matGoodsPojo.setGoodsspec("");
        if (matGoodsPojo.getGoodsunit() == null) matGoodsPojo.setGoodsunit("");
        if (matGoodsPojo.getGoodsstate() == null) matGoodsPojo.setGoodsstate("");
        if (matGoodsPojo.getGoodspinyin() == null) matGoodsPojo.setGoodspinyin("");
        if (matGoodsPojo.getVersionnum() == null) matGoodsPojo.setVersionnum("");
        if (matGoodsPojo.getMaterial() == null) matGoodsPojo.setMaterial("");
        if (matGoodsPojo.getSurface() == null) matGoodsPojo.setSurface("");
        if (matGoodsPojo.getBarcode() == null) matGoodsPojo.setBarcode("");
        if (matGoodsPojo.getSafestock() == null) matGoodsPojo.setSafestock(0D);
        if (matGoodsPojo.getInprice() == null) matGoodsPojo.setInprice(0D);
        if (matGoodsPojo.getOutprice() == null) matGoodsPojo.setOutprice(0D);
        if (matGoodsPojo.getGroupid() == null) matGoodsPojo.setGroupid("");
        if (matGoodsPojo.getFileguid() == null) matGoodsPojo.setFileguid("");
        if (matGoodsPojo.getDrawing() == null) matGoodsPojo.setDrawing("");
        if (matGoodsPojo.getStoreid() == null) matGoodsPojo.setStoreid("");
        if (matGoodsPojo.getStorelistname() == null) matGoodsPojo.setStorelistname("");
        if (matGoodsPojo.getStorelistguid() == null) matGoodsPojo.setStorelistguid("");
        if (matGoodsPojo.getIvquantity() == null) matGoodsPojo.setIvquantity(0D);
        if (matGoodsPojo.getAgeprice() == null) matGoodsPojo.setAgeprice(0D);
        if (matGoodsPojo.getUidgroupguid() == null) matGoodsPojo.setUidgroupguid("");
        if (matGoodsPojo.getUidgroupcode() == null) matGoodsPojo.setUidgroupcode("");
        if (matGoodsPojo.getUidgroupname() == null) matGoodsPojo.setUidgroupname("");
        if (matGoodsPojo.getUidgroupnum() == null) matGoodsPojo.setUidgroupnum(0);
        if (matGoodsPojo.getPartid() == null) matGoodsPojo.setPartid("");
        if (matGoodsPojo.getPid() == null) matGoodsPojo.setPid("");
        if (matGoodsPojo.getPuid() == null) matGoodsPojo.setPuid("");
        if (matGoodsPojo.getEnabledmark() == null) matGoodsPojo.setEnabledmark(0);
        if (matGoodsPojo.getGoodsphoto1() == null) matGoodsPojo.setGoodsphoto1("");
        if (matGoodsPojo.getGoodsphoto2() == null) matGoodsPojo.setGoodsphoto2("");
        if (matGoodsPojo.getRemark() == null) matGoodsPojo.setRemark("");
        if (matGoodsPojo.getCreateby() == null) matGoodsPojo.setCreateby("");
        if (matGoodsPojo.getCreatebyid() == null) matGoodsPojo.setCreatebyid("");
        if (matGoodsPojo.getCreatedate() == null) matGoodsPojo.setCreatedate(new Date());
        if (matGoodsPojo.getLister() == null) matGoodsPojo.setLister("");
        if (matGoodsPojo.getListerid() == null) matGoodsPojo.setListerid("");
        if (matGoodsPojo.getModifydate() == null) matGoodsPojo.setModifydate(new Date());
        if (matGoodsPojo.getDeletemark() == null) matGoodsPojo.setDeletemark(0);
        if (matGoodsPojo.getDeletelister() == null) matGoodsPojo.setDeletelister("");
        if (matGoodsPojo.getDeletelisterid() == null) matGoodsPojo.setDeletelisterid("");
        if (matGoodsPojo.getDeletedate() == null) matGoodsPojo.setDeletedate(new Date());
        if (matGoodsPojo.getBatchmg() == null) matGoodsPojo.setBatchmg(0);
        if (matGoodsPojo.getBatchonly() == null) matGoodsPojo.setBatchonly(0);
        if (matGoodsPojo.getSkumark() == null) matGoodsPojo.setSkumark(0);
        if (matGoodsPojo.getPacksnmark() == null) matGoodsPojo.setPacksnmark(0);
        if (matGoodsPojo.getVirtualitem() == null) matGoodsPojo.setVirtualitem(0);
        if (matGoodsPojo.getBomid() == null) matGoodsPojo.setBomid("");
        if (matGoodsPojo.getQuickcode() == null) matGoodsPojo.setQuickcode("");
        if (matGoodsPojo.getBrandname() == null) matGoodsPojo.setBrandname("");
        if (matGoodsPojo.getBuyremqty() == null) matGoodsPojo.setBuyremqty(0D);
        if (matGoodsPojo.getWkwsremqty() == null) matGoodsPojo.setWkwsremqty(0D);
        if (matGoodsPojo.getWkscremqty() == null) matGoodsPojo.setWkscremqty(0D);
        if (matGoodsPojo.getBusremqty() == null) matGoodsPojo.setBusremqty(0D);
        if (matGoodsPojo.getMrpremqty() == null) matGoodsPojo.setMrpremqty(0D);
        if (matGoodsPojo.getRequremqty() == null) matGoodsPojo.setRequremqty(0D);
        if (matGoodsPojo.getAlertsqty() == null) matGoodsPojo.setAlertsqty(0D);
        if (matGoodsPojo.getIntqtymark() == null) matGoodsPojo.setIntqtymark(0);
        if (matGoodsPojo.getWeightqty() == null) matGoodsPojo.setWeightqty(0D);
        if (matGoodsPojo.getWeightunit() == null) matGoodsPojo.setWeightunit("");
        if (matGoodsPojo.getLengthqty() == null) matGoodsPojo.setLengthqty(0D);
        if (matGoodsPojo.getLengthunit() == null) matGoodsPojo.setLengthunit("");
        if (matGoodsPojo.getAreaqty() == null) matGoodsPojo.setAreaqty(0D);
        if (matGoodsPojo.getAreaunit() == null) matGoodsPojo.setAreaunit("");
        if (matGoodsPojo.getVolumeqty() == null) matGoodsPojo.setVolumeqty(0D);
        if (matGoodsPojo.getVolumeunit() == null) matGoodsPojo.setVolumeunit("");
        if (matGoodsPojo.getPackqty() == null) matGoodsPojo.setPackqty(0D);
        if (matGoodsPojo.getPackunit() == null) matGoodsPojo.setPackunit("");
        if (matGoodsPojo.getMatqtyunit() == null) matGoodsPojo.setMatqtyunit(0);
        if (matGoodsPojo.getOverflowqty() == null) matGoodsPojo.setOverflowqty(0D);
        if (matGoodsPojo.getPricingmode() == null) matGoodsPojo.setPricingmode(0);
        if (matGoodsPojo.getTaxrate() == null) matGoodsPojo.setTaxrate(0);
        if (matGoodsPojo.getIntaxprice() == null) matGoodsPojo.setIntaxprice(0D);
        if (matGoodsPojo.getOuttaxprice() == null) matGoodsPojo.setOuttaxprice(0D);
        if (matGoodsPojo.getSpecid() == null) matGoodsPojo.setSpecid("");
        if (matGoodsPojo.getAliasname() == null) matGoodsPojo.setAliasname("");
        if (matGoodsPojo.getPcsx() == null) matGoodsPojo.setPcsx(0D);
        if (matGoodsPojo.getPcsy() == null) matGoodsPojo.setPcsy(0D);
        if (matGoodsPojo.getSizeunit() == null) matGoodsPojo.setSizeunit("");
        if (matGoodsPojo.getSetx() == null) matGoodsPojo.setSetx(0D);
        if (matGoodsPojo.getSety() == null) matGoodsPojo.setSety(0D);
        if (matGoodsPojo.getSet2pcs() == null) matGoodsPojo.setSet2pcs(0);
        if (matGoodsPojo.getPnlx() == null) matGoodsPojo.setPnlx(0D);
        if (matGoodsPojo.getPnly() == null) matGoodsPojo.setPnly(0D);
        if (matGoodsPojo.getPnl2pcs() == null) matGoodsPojo.setPnl2pcs(0);
        if (matGoodsPojo.getExpimark() == null) matGoodsPojo.setExpimark(0);
        if (matGoodsPojo.getDefexpiday() == null) matGoodsPojo.setDefexpiday(0);
        if (matGoodsPojo.getInspid() == null) matGoodsPojo.setInspid("");
        if (matGoodsPojo.getUnitjson() == null) matGoodsPojo.setUnitjson("");
        if (matGoodsPojo.getCustjson() == null) matGoodsPojo.setCustjson("");
        if (matGoodsPojo.getCustom1() == null) matGoodsPojo.setCustom1("");
        if (matGoodsPojo.getCustom2() == null) matGoodsPojo.setCustom2("");
        if (matGoodsPojo.getCustom3() == null) matGoodsPojo.setCustom3("");
        if (matGoodsPojo.getCustom4() == null) matGoodsPojo.setCustom4("");
        if (matGoodsPojo.getCustom5() == null) matGoodsPojo.setCustom5("");
        if (matGoodsPojo.getCustom6() == null) matGoodsPojo.setCustom6("");
        if (matGoodsPojo.getCustom7() == null) matGoodsPojo.setCustom7("");
        if (matGoodsPojo.getCustom8() == null) matGoodsPojo.setCustom8("");
        if (matGoodsPojo.getCustom9() == null) matGoodsPojo.setCustom9("");
        if (matGoodsPojo.getCustom10() == null) matGoodsPojo.setCustom10("");
        if (matGoodsPojo.getDeptid() == null) matGoodsPojo.setDeptid("");
        if (matGoodsPojo.getTenantid() == null) matGoodsPojo.setTenantid("");
        if (matGoodsPojo.getTenantname() == null) matGoodsPojo.setTenantname("");
        if (matGoodsPojo.getRevision() == null) matGoodsPojo.setRevision(0);

        MatGoodsEntity matGoodsEntity = new MatGoodsEntity();
        BeanUtils.copyProperties(matGoodsPojo, matGoodsEntity);
        String id = inksSnowflake.getSnowflake().nextIdStr();
        matGoodsEntity.setId(id);
        matGoodsEntity.setRevision(1);  //乐观锁
        MatGoodsPojo dbPojo = this.matGoodsMapper.getEntityByNameSpecPartBrandNameSurface(matGoodsPojo);
        // 读取指定系统参数 货品建立是否允许重名 true
        // 20250708： na 不控制 reject 禁止  warning警告
        if (dbPojo != null) {
            // reject 禁止 或 未配置参数默认按禁止处理
            if ("reject".equalsIgnoreCase(allow) || StringUtils.isBlank(allow)) {
                throw new RuntimeException("同名同规格的货品已有,请勿重建:" + DateUtils.parseDateToStr("yyyy-MM-dd", dbPojo.getCreatedate()));
            }
            // warning 警告，当warn=0时跳过警告
            if ("warning".equalsIgnoreCase(allow) && warn == 1) {
                throw new WarnException("同名同规格的货品已有,是否继续保存?");
            }
            // na、true、其它值 允许直接创建
        }
        Integer count = this.matGoodsMapper.checkGoodsUid("", matGoodsPojo.getGoodsuid(), matGoodsPojo.getTenantid());
        if (count > 0) {
            throw new RuntimeException("货品编码已存在,请修改编码后重试!");
        }
        this.matGoodsMapper.insert(matGoodsEntity);

        return this.getEntity(matGoodsEntity.getId(), matGoodsEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matGoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatGoodsPojo update(MatGoodsPojo matGoodsPojo, String allow, Integer warn) {
        MatGoodsEntity matGoodsEntity = new MatGoodsEntity();
        BeanUtils.copyProperties(matGoodsPojo, matGoodsEntity);
        MatGoodsPojo dbPojo = this.matGoodsMapper.getEntityByNameSpecPartBrandNameSurface(matGoodsPojo);
        //if (dbPojo != null && !dbPojo.getId().equals(matGoodsPojo.getId()) && !"true".equals(allow)) {
        //    throw new RuntimeException("同名同规格的货品已有,请勿重建:" + DateUtils.parseDateToStr("yyyy-MM-dd", dbPojo.getCreatedate()));
        //}
        // 读取指定系统参数 货品建立是否允许重名 true
        // 20250708： na 不控制 reject 禁止  warning警告
        if (dbPojo != null && !dbPojo.getId().equals(matGoodsPojo.getId())) {
            // reject 禁止 或 未配置参数默认按禁止处理
            if ("reject".equalsIgnoreCase(allow) || StringUtils.isBlank(allow)) {
                throw new RuntimeException("同名同规格的货品已有,请勿重建:" + DateUtils.parseDateToStr("yyyy-MM-dd", dbPojo.getCreatedate()));
            }
            // warning 警告，当warn=0时跳过警告
            if ("warning".equalsIgnoreCase(allow) && warn == 1) {
                throw new WarnException("同名同规格的货品已有,是否继续保存?");
            }
            // na、true、其它值 允许直接创建
        }
        Integer count = this.matGoodsMapper.checkGoodsUid(matGoodsPojo.getId(), matGoodsPojo.getGoodsuid(), matGoodsPojo.getTenantid());
        if (count > 0) {
            throw new RuntimeException("货品编码已存在,请修改编码后重试!");
        }
        this.matGoodsMapper.update(matGoodsEntity);
        return this.getEntity(matGoodsEntity.getId(), matGoodsEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        MatGoodsPojo matGoodsPojo = this.getEntity(key, tid);
        return this.matGoodsMapper.delete(key, tid);
    }

    //按货品料号和租户ID查询货品
    @Override
    public MatGoodsPojo getEntityByGoodsUid(String name, String tid) {
        try {
            return matGoodsMapper.getEntityByGoodsUid(name, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //按货品名称和租户ID查询货品
    @Override
    public MatGoodsPojo getEntityByName(String name, String tid) {
        try {
            return matGoodsMapper.getEntityByName(name, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //按货品名称\规格查询货品信息
    @Override
    public MatGoodsPojo getEntityByNameSpec(String name, String goodsspec, String tid) {
        try {
            return matGoodsMapper.getEntityByNameSpec(name, goodsspec, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //按货品名称\规格\外部编码查询货品信息
    @Override
    public MatGoodsPojo getEntityByNameSpecPart(String name, String goodsspec, String partid, String tid) {
        try {
            return matGoodsMapper.getEntityByNameSpecPart(name, goodsspec, partid, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //获取最大货品编码实例
    @Override
    public MatGoodsPojo getEntityByGroup(String groupid, String tid) {
        try {
            return matGoodsMapper.getEntityByGroup(groupid, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    // 查询货品是否被引用
    @Override
    public List<String> getCiteBillName(String key, String tid) {
        return this.matGoodsMapper.getCiteBillName(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatGoodsPojo getEntityByPartid(String key, String tid) {
        return this.matGoodsMapper.getEntityByPartid(key, tid);
    }


    //按快速码和租户ID查询货品
    @Override
    public MatGoodsPojo getEntityByQuickCode(String key, String tid) {
        try {
            return matGoodsMapper.getEntityByQuickCode(key, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 修改数据
     *
     * @param tid 实例对象
     * @return 实例对象
     */
    @Override
    public Integer updateIvQty(String tid) {
        try {
            return this.matGoodsMapper.updateIvQty(tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //刷新销售待出数
    @Override
    public int updateGoodsBusRemQty(String goodsid, String tid) {
        return this.matGoodsMapper.updateGoodsBusRemQty(goodsid, tid);
    }

    //刷新收货待入数

    @Override
    public int updateGoodsBuyRemQty(String key, String tid) {
        return this.matGoodsMapper.updateGoodsBuyRemQty(key, tid);
    }
    //刷新生产待入数

    @Override
    public int updateGoodsWkWsRemQty(String key, String tid) {
        return this.matGoodsMapper.updateGoodsWkWsRemQty(key, tid);
    }
    //刷新加工待入数

    @Override
    public int updateGoodsWkScRemQty(String key, String tid) {
        return this.matGoodsMapper.updateGoodsWkScRemQty(key, tid);
    }
    //刷新领料待出数

    @Override
    public int updateGoodsRequRemQty(String key, String tid) {
        return this.matGoodsMapper.updateGoodsRequRemQty(key, tid);
    }

    //刷新当前库存数和单价
    @Override
    public int updateGoodsIvQuantity(String key, String tid) {
        return this.matGoodsMapper.updateGoodsIvQuantity(key, tid);
    }

    @Override
    public String getGoodsstate(String goodsid, String tenantid) {
        return this.matGoodsMapper.getGoodsstate(goodsid, tenantid);
    }

    @Override
    public Integer cleanGoods(String goodsstate, String tenantid) {
        return this.matGoodsMapper.cleanGoods(goodsstate, tenantid);
    }

    @Override
    public Integer cleanWorkgroup(String grouptype, String tenantid) {
        return this.matGoodsMapper.cleanWorkgroup(grouptype, tenantid);
    }

    /**
     * 解析XML内容并转换为MatGoodsPojo对象
     *
     * @param xmlContent XML内容
     * @param loginUser  登录用户
     * @return MatGoodsPojo对象
     */
    @Override
    public MatGoodsPojo parseXmlToMatGoodsPojo(String xmlContent, LoginUser loginUser) throws Exception {
        String tid = loginUser.getTenantid();
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new InputSource(new StringReader(xmlContent)));

        MatGoodsPojo matGoodsPojo = new MatGoodsPojo();

        // 解析Mat_Goods节点
        NodeList matGoodsNodes = document.getElementsByTagName("Mat_Goods");
        if (matGoodsNodes.getLength() > 0) {
            Element matGoodsElement = (Element) matGoodsNodes.item(0);
            mapMatGoodsElementToPojo(matGoodsElement, matGoodsPojo, tid, loginUser);
        }

        return matGoodsPojo;
    }

    /**
     * 将Mat_Goods XML元素映射到MatGoodsPojo对象
     */
    private void mapMatGoodsElementToPojo(Element element, MatGoodsPojo pojo, String tid, LoginUser loginUser) throws Exception {
        try {
            // 基本字段映射
            pojo.setId(getElementTextContent(element, "id"));
            pojo.setGoodsuid(getElementTextContent(element, "GoodsUid"));
            pojo.setVersionnum(getElementTextContent(element, "EditionID"));
            pojo.setGoodsname(getElementTextContent(element, "GoodsName"));
            pojo.setGoodsspec(getElementTextContent(element, "GoodsSpec"));
            pojo.setUidgroupcode(getElementTextContent(element, "GoodsClassID"));
            pojo.setGoodsstate(getElementTextContent(element, "GoodsState"));
            pojo.setGoodspinyin(getElementTextContent(element, "GoodsPinyin"));
            pojo.setMaterial(getElementTextContent(element, "Material"));
            pojo.setSurface(getElementTextContent(element, "Surface"));
            pojo.setBarcode(getElementTextContent(element, "BarCode"));
            pojo.setGoodsunit(getElementTextContent(element, "GoodsUnit"));
            // 数值字段映射
            String safeStock = getElementTextContent(element, "SafeStock");
            if (StringUtils.isNotBlank(safeStock)) {
                pojo.setSafestock(Double.parseDouble(safeStock));
            }
            String inPrice = getElementTextContent(element, "InPrice");
            if (StringUtils.isNotBlank(inPrice)) {
                pojo.setInprice(Double.parseDouble(inPrice));
            }

            String outPrice = getElementTextContent(element, "OutPrice");
            if (StringUtils.isNotBlank(outPrice)) {
                pojo.setOutprice(Double.parseDouble(outPrice));
            }
            //pojo.setFactory(getElementTextContent(element, "Factory"));
            pojo.setGroupid(getElementTextContent(element, "Custid"));
            //pojo.setSuppid(getElementTextContent(element, "Suppid"));
            pojo.setFileguid(getElementTextContent(element, "FileName"));
            pojo.setDrawing(getElementTextContent(element, "Drawing"));
            pojo.setStoreid(getElementTextContent(element, "Storeid"));
            pojo.setStorelistname(getElementTextContent(element, "StoreListName"));
            pojo.setStorelistguid(getElementTextContent(element, "StoreListGuid"));
            pojo.setCustom1(getElementTextContent(element, "Custom1"));
            pojo.setCustom2(getElementTextContent(element, "Custom2"));
            pojo.setRemark(getElementTextContent(element, "Remark"));
            String ivQuantity = getElementTextContent(element, "IvQuantity");
            if (StringUtils.isNotBlank(ivQuantity)) {
                pojo.setIvquantity(Double.parseDouble(ivQuantity));
            }
            String agePrice = getElementTextContent(element, "AgePrice");
            if (StringUtils.isNotBlank(agePrice)) {
                pojo.setAgeprice(Double.parseDouble(agePrice));
            }
            String invalid = getElementTextContent(element, "Invalid");
            if (StringUtils.isNotBlank(invalid)) {
                pojo.setEnabledmark("true".equalsIgnoreCase(invalid) ? 1 : 0);
            }
            //pojo.setPhotopath(getElementTextContent(element, "PhotoPath"));
            pojo.setLister(getElementTextContent(element, "Lister"));
            // 日期字段映射
            String createDate = getElementTextContent(element, "Createdate");
            if (StringUtils.isNotBlank(createDate)) {
                pojo.setCreatedate(parseDate(createDate));
            }
            String modifyDate = getElementTextContent(element, "ModifyDate");
            if (StringUtils.isNotBlank(modifyDate)) {
                pojo.setModifydate(parseDate(modifyDate));
            }
            //pojo.setState(getElementTextContent(element, "StateText"));
            pojo.setCustom1(getElementTextContent(element, "Custom1"));
            pojo.setCustom2(getElementTextContent(element, "Custom2"));
            pojo.setCustom3(getElementTextContent(element, "Custom3"));
            pojo.setCustom4(getElementTextContent(element, "Custom4"));
            // PCB相关字段
            String pcsX = getElementTextContent(element, "PcsX");
            if (StringUtils.isNotBlank(pcsX)) {
                pojo.setPcsx(Double.parseDouble(pcsX));
            }
            String pcsY = getElementTextContent(element, "PcsY");
            if (StringUtils.isNotBlank(pcsY)) {
                pojo.setPcsy(Double.parseDouble(pcsY));
            }
            pojo.setSizeunit(getElementTextContent(element, "SizeUnit"));
            String setX = getElementTextContent(element, "SetX");
            if (StringUtils.isNotBlank(setX)) {
                pojo.setSetx(Double.parseDouble(setX));
            }
            String setY = getElementTextContent(element, "SetY");
            if (StringUtils.isNotBlank(setY)) {
                pojo.setSety(Double.parseDouble(setY));
            }
            String set2Pcs = getElementTextContent(element, "Set2Pcs");
            if (StringUtils.isNotBlank(set2Pcs)) {
                pojo.setSet2pcs(Integer.parseInt(set2Pcs));
            }
            pojo.setUidgroupname(getElementTextContent(element, "UidGroup1"));
            pojo.setUidgroupguid(getElementTextContent(element, "UidGroup2"));
            String uidGroupNo = getElementTextContent(element, "UidGroupNo");
            if (StringUtils.isNotBlank(uidGroupNo)) {
                pojo.setUidgroupnum(Integer.parseInt(uidGroupNo));
            }
            pojo.setPartid(getElementTextContent(element, "Partid"));

            String pnlX = getElementTextContent(element, "PnlX");
            if (StringUtils.isNotBlank(pnlX)) {
                pojo.setPnlx(Double.parseDouble(pnlX));
            }

            String pnlY = getElementTextContent(element, "PnlY");
            if (StringUtils.isNotBlank(pnlY)) {
                pojo.setPnly(Double.parseDouble(pnlY));
            }

            String pnl2Pcs = getElementTextContent(element, "Pnl2Pcs");
            if (StringUtils.isNotBlank(pnl2Pcs)) {
                pojo.setPnl2pcs(Integer.parseInt(pnl2Pcs));
            }
            pojo.setPid(getElementTextContent(element, "Pid"));
            pojo.setPuid(getElementTextContent(element, "PUid"));
            //pojo.setUidgroup3(getElementTextContent(element, "UidGroup3"));
            pojo.setStorelistname(getElementTextContent(element, "StoreListName"));
            pojo.setStorelistguid(getElementTextContent(element, "StoreListGuid"));
            //pojo.setGroupguid(getElementTextContent(element, "GroupGuid"));
            // 设置租户ID和其他必要字段
            pojo.setTenantid(tid);
            pojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            pojo.setRevision(1);
        } catch (Exception e) {
            throw new RuntimeException("映射Mat_Goods数据时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 获取XML元素的文本内容
     */
    private String getElementTextContent(Element parent, String tagName) {
        NodeList nodeList = parent.getElementsByTagName(tagName);
        if (nodeList.getLength() > 0) {
            String content = nodeList.item(0).getTextContent();
            return StringUtils.isNotBlank(content) ? content.trim() : "";
        }
        return "";
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            // 处理ISO 8601格式: 2025-07-22T16:25:15+08:00
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
            return sdf.parse(dateStr);
        } catch (Exception e) {
            try {
                // 备用格式
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            } catch (Exception ex) {
                return new Date(); // 解析失败时返回当前时间
            }
        }
    }
}
