package inks.service.std.goods.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatGoodsPojo;
import inks.service.std.goods.service.MatGoodsService;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 货品信息(Mat_Goods)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 08:36:12
 */
public class D91ExportController {
    /**
     * 服务对象
     */
    @Resource
    private MatGoodsService matGoodsService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public void goodsList(@RequestBody String json, String groupid, HttpServletRequest request, HttpServletResponse response) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "") {
                queryParam.setOrderBy("CreateDate");
            }
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.deletemark=0";
            if (groupid != null) {
                qpfilter += " and Mat_Goods.Groupid='" + groupid + "'";
            }
            queryParam.setFilterstr(qpfilter);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("货品案档", ""),
                    MatGoodsPojo.class, this.matGoodsService.getPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "货品案档");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
