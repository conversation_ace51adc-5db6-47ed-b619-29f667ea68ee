package inks.service.std.goods.domain.export;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * BOM子表(MatBomitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-04-22 20:21:47
 */
public class MatBomlayeritemPojo implements Serializable {
    private static final long serialVersionUID = -49566951102699329L;
    //赋值bomitem.id
    private String id;

    // 商品ID
    private String goodsid;
    // bom
    private String bomid;
    // 层号
    private Integer layernum;
    // 1为物料
    private Integer matmark;
    // 行号
    @Excel(name = "序号")
    private Integer rownum;
    // 层标
    @Excel(name = "L1")
    private String layer1;
    // 层标
    @Excel(name = "L2")
    private String layer2;
    // 层标
    @Excel(name = "L3")
    private String layer3;
    // 层标
    @Excel(name = "L4")
    private String layer4;
    // 层标
    @Excel(name = "L5")
    private String layer5;
    // 层标
    @Excel(name = "L6")
    private String layer6;
    // 货品编码
    @Excel(name = "货品编码")
    private String goodsuid;
    // 名称
    @Excel(name = "名称")
    private String goodsname;
    // 规格
    @Excel(name = "规格")
    private String goodsspec;
    // 外部编码
    @Excel(name = "外部编码")
    private String partid;
    // 货品单位
    @Excel(name = "货品单位")
    private String goodsunit;
    // 主件数量
    @Excel(name = "主件数量")
    private Double mainqty;
    // 子件数量
    @Excel(name = "子件数量")
    private Double subqty;
    // 损耗率
    @Excel(name = "损耗率")
    private Double lossrate;
    // 属性 厂制/委制/外购/客供
    @Excel(name = "属性")
    private String attrcode;
    // 流程编码
    @Excel(name = "流程编码")
    private String flowcode;
    // 描述
    @Excel(name = "描述")
    private String description;
    // 标签
    @Excel(name = "标签")
    private String itemlabel;
    // 备注
    @Excel(name = "备注")
    private String remark;

    // 计算的bom数量
    @Excel(name = "bom数量")
    private Double bomqty;
    //可用量：仓库+在途-待出
    private Double avaiqty;
    //MRP应需量
    private Double needqty;
    //计划领用量
    private Double stoplanqty;
    //实际需求量
    private Double realqty;

    //货品物料状态(成品/半成品/原材料)
    private String goodsstate;

    public String getGoodsstate() {
        return goodsstate;
    }

    public void setGoodsstate(String goodsstate) {
        this.goodsstate = goodsstate;
    }

    public Double getRealqty() {
        return realqty;
    }

    public void setRealqty(Double realqty) {
        this.realqty = realqty;
    }

    public Double getAvaiqty() {
        return avaiqty;
    }

    public void setAvaiqty(Double avaiqty) {
        this.avaiqty = avaiqty;
    }

    public Double getNeedqty() {
        return needqty;
    }

    public void setNeedqty(Double needqty) {
        this.needqty = needqty;
    }

    public Double getStoplanqty() {
        return stoplanqty;
    }

    public void setStoplanqty(Double stoplanqty) {
        this.stoplanqty = stoplanqty;
    }

    public Double getBomqty() {
        return bomqty;
    }

    public void setBomqty(Double bomqty) {
        this.bomqty = bomqty;
    }

    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    public String getBomid() {
        return bomid;
    }

    public void setBomid(String bomid) {
        this.bomid = bomid;
    }

    public Integer getLayernum() {
        return layernum;
    }

    public void setLayernum(Integer layernum) {
        this.layernum = layernum;
    }

    public Integer getMatmark() {
        return matmark;
    }

    public void setMatmark(Integer matmark) {
        this.matmark = matmark;
    }

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    public String getLayer1() {
        return layer1;
    }

    public void setLayer1(String layer1) {
        this.layer1 = layer1;
    }

    public String getLayer2() {
        return layer2;
    }

    public void setLayer2(String layer2) {
        this.layer2 = layer2;
    }

    public String getLayer3() {
        return layer3;
    }

    public void setLayer3(String layer3) {
        this.layer3 = layer3;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLayer4() {
        return layer4;
    }

    public void setLayer4(String layer4) {
        this.layer4 = layer4;
    }

    public String getLayer5() {
        return layer5;
    }

    public void setLayer5(String layer5) {
        this.layer5 = layer5;
    }

    public String getLayer6() {
        return layer6;
    }

    public void setLayer6(String layer6) {
        this.layer6 = layer6;
    }

    public String getGoodsuid() {
        return goodsuid;
    }

    public void setGoodsuid(String goodsuid) {
        this.goodsuid = goodsuid;
    }

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    public String getPartid() {
        return partid;
    }

    public void setPartid(String partid) {
        this.partid = partid;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public Double getMainqty() {
        return mainqty;
    }

    public void setMainqty(Double mainqty) {
        this.mainqty = mainqty;
    }

    public Double getSubqty() {
        return subqty;
    }

    public void setSubqty(Double subqty) {
        this.subqty = subqty;
    }

    public Double getLossrate() {
        return lossrate;
    }

    public void setLossrate(Double lossrate) {
        this.lossrate = lossrate;
    }

    public String getAttrcode() {
        return attrcode;
    }

    public void setAttrcode(String attrcode) {
        this.attrcode = attrcode;
    }

    public String getFlowcode() {
        return flowcode;
    }

    public void setFlowcode(String flowcode) {
        this.flowcode = flowcode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getItemlabel() {
        return itemlabel;
    }

    public void setItemlabel(String itemlabel) {
        this.itemlabel = itemlabel;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

