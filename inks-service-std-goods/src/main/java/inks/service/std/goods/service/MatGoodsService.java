package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.LoginUser;
import inks.service.std.goods.domain.pojo.MatGoodsPojo;

import java.util.List;

/**
 * 货品信息(MatGoods)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 08:36:14
 */
public interface MatGoodsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatGoodsPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatGoodsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matGoodsPojo 实例对象
     * @return 实例对象
     */
    MatGoodsPojo insert(MatGoodsPojo matGoodsPojo, String allow, Integer warn);

    /**
     * 修改数据
     *
     * @param matGoodspojo 实例对象
     * @return 实例对象
     */
    MatGoodsPojo update(MatGoodsPojo matGoodspojo, String allow, Integer warn);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    //按货品料号获取货品信息
    MatGoodsPojo getEntityByGoodsUid(String name, String tid);

    //货品名称获取货品信息
    MatGoodsPojo getEntityByName(String name, String tid);

    //按货品名称\规格查询货品信息
    MatGoodsPojo getEntityByNameSpec(String name, String goodsspec, String tid);

    //按货品名称\规格\外部编码查询货品信息
    MatGoodsPojo getEntityByNameSpecPart(String name, String goodsspec, String partid, String tid);

    //获取最大货品编码实例
    MatGoodsPojo getEntityByGroup(String groupid, String tid);

    // 查询货品是否被引用
    List<String> getCiteBillName(String key, String tid);

    //按外部编码获取货品信息
    MatGoodsPojo getEntityByPartid(String key, String tid);

    //按快速码获取货品信息
    MatGoodsPojo getEntityByQuickCode(String key, String tid);

    /**
     * 修改数据
     *
     * @param tid 实例对象
     * @return 实例对象
     */
    Integer updateIvQty(String tid);
    //刷新销售待出数

    int updateGoodsBusRemQty(String goodsid, String tid);
    //刷新收货待入数

    int updateGoodsBuyRemQty(String key, String tid);
    //刷新生产待入数

    int updateGoodsWkWsRemQty(String key, String tid);
    //刷新加工待入数

    int updateGoodsWkScRemQty(String key, String tid);
    //刷新领料待出数

    int updateGoodsRequRemQty(String key, String tid);
    //刷新当前库存数和单价

    int updateGoodsIvQuantity(String key, String tid);


    String getGoodsstate(String goodsid, String tenantid);

    Integer cleanGoods(String goodsstate, String tenantid);

    Integer cleanWorkgroup(String grouptype, String tenantid);

    /**
     * 解析XML并转换为MatGoodsPojo对象
     *
     * @param xmlContent XML内容
     * @param loginUser 登录用户
     * @return MatGoodsPojo对象
     */
    MatGoodsPojo parseXmlToMatGoodsPojo(String xmlContent, LoginUser loginUser) throws Exception;
}
