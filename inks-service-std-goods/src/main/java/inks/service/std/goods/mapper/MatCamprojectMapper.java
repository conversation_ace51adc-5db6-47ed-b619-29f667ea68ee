package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatCamprojectPojo;
import inks.service.std.goods.domain.MatCamprojectEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工程事务(Mat_CamProject)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-21 09:17:15
 */
@Mapper
public interface MatCamprojectMapper {

    MatCamprojectPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<MatCamprojectPojo> getPageList(QueryParam queryParam);

    int insert(MatCamprojectEntity matCamprojectEntity);

    int update(MatCamprojectEntity matCamprojectEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    

    int approval(MatCamprojectEntity matCamprojectEntity);

    void syncMachItemEngStateText(String citeitemid, String engstatetext, String tid);

    void setNullStartDateEndDateHandler(String key, String tid);

    void setNullEndDate(String key, String tid);
}

