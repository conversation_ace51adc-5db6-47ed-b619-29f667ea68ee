package inks.service.std.goods.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 物料变更(MatEcn)实体类
 *
 * <AUTHOR>
 * @since 2021-12-29 15:02:14
 */
public class MatEcnEntity implements Serializable {
    private static final long serialVersionUID = 807759322389610414L;
    private String id;
    // 编码
    private String refno;
    // 单据日期
    private Date billdate;
    // 单据类型
    private String billtype;
    // 单据标题
    private String billtitle;
    // 变更原由
    private String causation;
    // ECL关联
    private String eclcode;
    // 描述
    private String description;
    // 关联货品
    private String goodsid;
    // 产品编码
    private String itemcode;
    // 产品名称
    private String itemname;
    // 产品规格
    private String itemspec;
    // 产品单位
    private String itemunit;
    // 关联单位id
    private String groupid;
    // 旧编码
    private String goodsuidold;
    // 新编码
    private String goodsuidnew;
    // 旧版本处理
    private String disposeold;
    // 新版本执行
    private String disposenew;
    // 旧库存处理
    private String disposesto;
    // 在制品处理
    private String disposewk;
    // 变更前b64
    private String photoold;
    // 变更后b64
    private String photonew;
    // 明细json(备用)
    private String detailjson;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 审核员
    private String assessor;
    // 审核员id
    private String assessorid;
    // 审核日期
    private Date assessdate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 编码
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    // 单据类型
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 单据标题
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }

    // 变更原由
    public String getCausation() {
        return causation;
    }

    public void setCausation(String causation) {
        this.causation = causation;
    }

    // ECL关联
    public String getEclcode() {
        return eclcode;
    }

    public void setEclcode(String eclcode) {
        this.eclcode = eclcode;
    }

    // 描述
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // 关联货品
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 产品编码
    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    // 产品名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 产品规格
    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    // 产品单位
    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    // 关联单位id
    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    // 旧编码
    public String getGoodsuidold() {
        return goodsuidold;
    }

    public void setGoodsuidold(String goodsuidold) {
        this.goodsuidold = goodsuidold;
    }

    // 新编码
    public String getGoodsuidnew() {
        return goodsuidnew;
    }

    public void setGoodsuidnew(String goodsuidnew) {
        this.goodsuidnew = goodsuidnew;
    }

    // 旧版本处理
    public String getDisposeold() {
        return disposeold;
    }

    public void setDisposeold(String disposeold) {
        this.disposeold = disposeold;
    }

    // 新版本执行
    public String getDisposenew() {
        return disposenew;
    }

    public void setDisposenew(String disposenew) {
        this.disposenew = disposenew;
    }

    // 旧库存处理
    public String getDisposesto() {
        return disposesto;
    }

    public void setDisposesto(String disposesto) {
        this.disposesto = disposesto;
    }

    // 在制品处理
    public String getDisposewk() {
        return disposewk;
    }

    public void setDisposewk(String disposewk) {
        this.disposewk = disposewk;
    }

    // 变更前b64
    public String getPhotoold() {
        return photoold;
    }

    public void setPhotoold(String photoold) {
        this.photoold = photoold;
    }

    // 变更后b64
    public String getPhotonew() {
        return photonew;
    }

    public void setPhotonew(String photonew) {
        this.photonew = photonew;
    }

    // 明细json(备用)
    public String getDetailjson() {
        return detailjson;
    }

    public void setDetailjson(String detailjson) {
        this.detailjson = detailjson;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 审核员
    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    // 审核员id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

