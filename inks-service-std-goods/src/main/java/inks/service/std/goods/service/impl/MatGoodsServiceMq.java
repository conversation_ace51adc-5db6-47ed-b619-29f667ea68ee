//package inks.service.std.goods.service.impl;
//
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.rabbitmq.client.Channel;
//import inks.service.std.goods.service.MatGoodsService;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.rabbit.annotation.Queue;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//
///**
// * <AUTHOR>
// * @date 2023年02月22日 16:49
// */
////用于接收MQ消息
//@Service
//public class MatGoodsServiceMq {
//    int i = 0;
//    @Resource
//    private MatGoodsService matGoodsService;
//
//    //MQ消息监听 队列名：updateGoodsQty 用于更新货品信息
//    @RabbitListener(queuesToDeclare = @Queue(value = "updateGoodsQty"))
//    public void process(String msg, Channel channel, Message message) {
//        try {
//            JSONObject jsonObject = JSONArray.parseObject(msg);
//            //key为goodsid
//            String key = jsonObject.getString("key");
//            //tid为租户id
//            String tid = jsonObject.getString("tid");
//            //更新类型
//            String type = jsonObject.getString("type");
//            if (StringUtils.isNotBlank(type)) {
//                switch (type) {
//                    //刷新销售待出数
//                    case "BusRemQty":
//                        this.matGoodsService.updateGoodsBusRemQty(key, tid);
//                        break;
//                    //刷新收货待入数
//                    case "BuyRemQty":
//                        this.matGoodsService.updateGoodsBuyRemQty(key, tid);
//                        break;
//                    //刷新生产待入数
//                    case "WkWsRemQty":
//                        this.matGoodsService.updateGoodsWkWsRemQty(key, tid);
//                        break;
//                    //刷新加工待入数
//                    case "WkScRemQty":
//                        this.matGoodsService.updateGoodsWkScRemQty(key, tid);
//                        break;
//                    //刷新领料待出数
//                    case "RequRemQty":
//                        this.matGoodsService.updateGoodsRequRemQty(key, tid);
//                        break;
//                    //刷新当前库存数和单价
//                    case "IvQuantity":
//                        this.matGoodsService.updateGoodsIvQuantity(key, tid);
//                        break;
//                    default:
//                        throw new IllegalStateException("非法type: " + type);
//                }
//            }
//        } catch (Exception e) {
//            System.out.println("\u001B[31m============catch (Exception e)错误生产者消息为" + msg + "\u001B[0m");
//        }
//        try {
//            i++;
//            LocalDateTime now = LocalDateTime.now();
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
//            String formattedNow = now.format(formatter);
//            // 输出黄色的文本
//            System.out.println("\u001B[33m============channel.basicAck消费第" + i + "条消息:" + msg + "\u001B[0m");
//            System.out.println("\u001B[36m============当前时间：" + formattedNow + "\u001B[0m");
//            //告诉MQ服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
//            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//    }
//}
