package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatBomorderPojo;
import inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo;
import inks.service.std.goods.domain.pojo.MatBomordertreePojo;

import java.util.List;

/**
 * 订单Bom(MatBomorder)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-11 14:40:11
 */
public interface MatBomorderService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomorderPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBomorderitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomorderPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBomorderPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBomorderPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matBomorderPojo 实例对象
     * @return 实例对象
     */
    MatBomorderPojo insert(MatBomorderPojo matBomorderPojo);

    /**
     * 修改数据
     *
     * @param matBomorderpojo 实例对象
     * @return 实例对象
     */
    MatBomorderPojo update(MatBomorderPojo matBomorderpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matBomorderPojo 实例对象
     * @return 实例对象
     */
    MatBomorderPojo approval(MatBomorderPojo matBomorderPojo);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomordertreePojo getTreeEntity(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<MatBomorderitemdetailPojo> getListByItemGoodsid(String key, String tid);
}
