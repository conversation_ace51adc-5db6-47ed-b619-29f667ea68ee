package inks.service.std.goods.service.impl;

import inks.service.std.goods.domain.export.SpecDrlVO;
import inks.service.std.goods.utils.PrintColor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;

@Service
public class DRLFileProcessorService {
    private List<SpecDrlVO> drlList;
    //@Test // 测试方法，用于验证DRLFileProcessor的功能 直接传入文件路径即可
    //public void testProcessDRLFile() {
    //
    //    String filepath = "D:\\nanno\\zhuan.drl";
    //    // 确保文件存在
    //    File file = new File(filepath);
    //    assertTrue("测试文件不存在", file.exists());
    //    inks.service.std.goods.controller.drl.DRLFileProcessor processor = new inks.service.std.goods.controller.drl.DRLFileProcessor();
    //    // 处理文件
    //    processor.processDRLFile(filepath);
    //    // 获取处理后的数据
    //    List<SpecDrlEntity> drlList = processor.getDrlList();
    //    for (SpecDrlEntity specDrlEntity : drlList) {
    //        PrintColor.zi(specDrlEntity.toString());
    //    }
    //}
    /**
     * 处理DRL文件的主方法，支持文件路径或MultipartFile
     * @param input 文件路径(String)或MultipartFile对象
     */
    public void processDRLFile(Object input) {
        // 每次处理文件时初始化列表，避免上次处理的残留数据
        drlList = new ArrayList<>();

        if (input instanceof String) {
            processDRLFileFromPath((String) input);
        } else if (input instanceof MultipartFile) {
            processDRLFileFromMultipartFile((MultipartFile) input);
        } else {
            throw new IllegalArgumentException("不支持的输入类型，请提供文件路径或MultipartFile对象");
        }
    }

    private void processDRLFileFromPath(String filepath) {
        File file = new File(filepath);
        if (!file.exists()) {
            throw new IllegalArgumentException("文件不存在: " + filepath);
        }

        String fileExt = getFileExtension(filepath);
        if (!isValidFileExtension(fileExt)) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileExt);
        }

        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            processFileContent(br);
        } catch (IOException e) {
            throw new RuntimeException("处理文件时出错: " + e.getMessage(), e);
        }
    }

    private void processDRLFileFromMultipartFile(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("无效的文件名");
        }

        String fileExt = getFileExtension(originalFilename);
        if (!isValidFileExtension(fileExt)) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileExt);
        }

        try (BufferedReader br = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            processFileContent(br);
        } catch (IOException e) {
            throw new RuntimeException("处理文件时出错: " + e.getMessage(), e);
        }
    }

    private void processFileContent(BufferedReader br) throws IOException {
        String line;
        String drlUnit = "I"; // 默认英制单位
        boolean readTool = false;
        boolean readDrl = false;
        int drlNo = -1;
        int drlTotal = 0;

        while ((line = br.readLine()) != null) {
            // 处理单位设置
            if (line.contains("METRIC")) {
                drlUnit = "M"; // 切换到公制单位
                continue;
            }

            // 处理工具信息开始标记
            if (line.contains("FMAT")) {
                readTool = true;
                continue;
            }

            // 处理工具信息结束标记
            if (line.contains("DETECT")) {
                readTool = false;
                continue;
            }

            // 处理钻孔数据开始标记
            if (line.contains("%")) {
                readTool = false;
                readDrl = true;
                continue;
            }

            // 处理工具信息
            if (readTool) {
                processTool(line, drlUnit);
                continue;
            }

            // 处理钻孔数据
            if (line.startsWith("T") && readDrl) {
                if (drlNo != -1 && drlTotal > 0) {
                    drlList.get(drlNo).setPnltotal(drlTotal);
                    drlTotal = 0;
                }

                drlNo = findDrlNo(line);
                continue;
            }

            // 处理文件结束标记
            if (line.contains("M30")) {
                if (drlNo != -1 && drlTotal > 0) {
                    drlList.get(drlNo).setPnltotal(drlTotal);
                }
                readDrl = false;
                continue;
            }

            // 累计钻孔数量
            if (readDrl) {
                drlTotal++;
            }
        }

        // 处理解析后的数据
        for (int i = 0; i < drlList.size(); i++) {
            drlList.get(i).setRowid(i + 1);
        }

        PrintColor.red("DRLFileProcessorService:数据导入成功");
    }

    private int findDrlNo(String symbol) {
        for (int i = 0; i < drlList.size(); i++) {
            if (drlList.get(i).getSymbol().equals(symbol)) {
                return i;
            }
        }
        return -1; // 如果没有找到匹配的符号，返回-1
    }

    private void processTool(String line, String drlUnit) {
        // 只处理 T开头且包含C 的工具定义行，其它直接跳过
        if (!line.startsWith("T") || !line.contains("C")) {
            return;
        }
        SpecDrlVO entity = new SpecDrlVO();
        String strDrl;
        double drlD;

        // 解析符号
        entity.setSymbol(line.substring(0, line.indexOf("C")));
        int fIndex = line.indexOf("F");
        if (fIndex > 0) {
            strDrl = line.substring(line.indexOf("C") + 1, fIndex);
        } else {
            strDrl = line.substring(line.indexOf("C") + 1);
        }

        // 转换单位
        drlD = Double.parseDouble(strDrl);
        if (drlUnit.equals("I")) {
            drlD = drlD / 0.03937; // 英寸转毫米
        }

        // 设置钻孔大小
        entity.setDrill(drlD);

        drlList.add(entity);
    }

    private String getFileExtension(String filename) {
        int lastIndexOf = filename.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return ""; // 空扩展名
        }
        return filename.substring(lastIndexOf);
    }

    private boolean isValidFileExtension(String fileExt) {
        return fileExt.equals(".drl") || fileExt.equals(".d") || fileExt.equals(".txt");
    }

    // drlList的getter方法
    public List<SpecDrlVO> getDrlList() {
        return drlList;
    }
}
