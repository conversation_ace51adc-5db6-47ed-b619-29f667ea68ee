package inks.service.std.goods.domain.pojo;

import java.util.List;

/**
 * PCB画图 pojo
 *
 * <AUTHOR>
 */
public class DrawPnlMapPojo {
    /**
     * 名称
     */
    private String name = "";

    /**
     * 背景总宽度
     */
    private double pnlx;

    /**
     * 背景总高度
     */
    private double pnly;

    /**
     * 具体规则
     */
    private List<DrawPnlMapItemPojo> item;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getPnlx() {
        return pnlx;
    }

    public void setPnlx(double pnlx) {
        this.pnlx = pnlx;
    }

    public double getPnly() {
        return pnly;
    }

    public void setPnly(double pnly) {
        this.pnly = pnly;
    }

    public List<DrawPnlMapItemPojo> getItem() {
        return item;
    }

    public void setItem(List<DrawPnlMapItemPojo> item) {
        this.item = item;
    }
}
