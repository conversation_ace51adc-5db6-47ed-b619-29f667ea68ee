package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSpecpcbitemEntity;
import inks.service.std.goods.domain.pojo.MatSpecpcbitemPojo;
import inks.service.std.goods.domain.pojo.MatSpecpcbitemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * PCB工艺项目(MatSpecpcbitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:57
 */
@Mapper
public interface MatSpecpcbitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecpcbitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecpcbitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecpcbitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);

    List<MatSpecpcbitemVo> getListVO(@Param("Pid") String Pid, @Param("tid") String tid);

    /**
     * 新增数据
     *
     * @param matSpecpcbitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpecpcbitemEntity matSpecpcbitemEntity);


    /**
     * 修改数据
     *
     * @param matSpecpcbitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpecpcbitemEntity matSpecpcbitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    String getWpidByWpNameIgnoreCase(String wpname, String tid);
}

