package inks.service.std.goods.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.MatBomdetailPojo;
import inks.common.core.domain.MrpBomdetailPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.MatBomEntity;
import inks.service.std.goods.domain.MatBomitemEntity;
import inks.service.std.goods.domain.export.MatBomlayerPojo;
import inks.service.std.goods.domain.export.MatBomlayeritemPojo;
import inks.service.std.goods.domain.pojo.MatBomPojo;
import inks.service.std.goods.domain.pojo.MatBomitemPojo;
import inks.service.std.goods.domain.pojo.MatBomitemdetailPojo;
import inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo;
import inks.service.std.goods.mapper.MatBomMapper;
import inks.service.std.goods.mapper.MatBomitemMapper;
import inks.service.std.goods.mapper.MatGoodsMapper;
import inks.service.std.goods.service.MatBomService;
import inks.service.std.goods.service.MatBomitemService;
import inks.service.std.goods.constant.MyConstant;
import inks.service.std.goods.utils.PrintColor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 物料Bom(MatBom)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-08 11:24:37
 */
@Service("matBomService")
public class MatBomServiceImpl implements MatBomService {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(MatBomServiceImpl.class);
    @Resource
    private MatBomMapper matBomMapper;
    @Resource
    private MatBomitemMapper matBomitemMapper;
    @Resource
    private RedisService redisService;
    /**
     * 服务对象Item
     */
    @Resource
    private MatBomitemService matBomitemService;
    @Resource
    private TokenService tokenService;
    @Resource
    private MatGoodsMapper matGoodsMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomPojo getEntity(String key, String tid) {
        return this.matBomMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBomitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBomitemdetailPojo> lst = matBomMapper.getPageList(queryParam);
            PageInfo<MatBomitemdetailPojo> pageInfo = new PageInfo<MatBomitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatBomPojo matBomPojo = this.matBomMapper.getEntity(key, tid);
            //读取子表
            matBomPojo.setItem(matBomitemMapper.getList(matBomPojo.getId(), matBomPojo.getTenantid()));
            return matBomPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBomPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBomPojo> lst = matBomMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matBomitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatBomPojo> pageInfo = new PageInfo<MatBomPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBomPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBomPojo> lst = matBomMapper.getPageTh(queryParam);
            PageInfo<MatBomPojo> pageInfo = new PageInfo<MatBomPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matBomPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatBomPojo insert(MatBomPojo matBomPojo) {
        String tid = matBomPojo.getTenantid();
//初始化NULL字段
        if (matBomPojo.getGoodsid() == null) matBomPojo.setGoodsid("");
        if (matBomPojo.getItemcode() == null) matBomPojo.setItemcode("");
        if (matBomPojo.getItemname() == null) matBomPojo.setItemname("");
        if (matBomPojo.getItemspec() == null) matBomPojo.setItemspec("");
        if (matBomPojo.getItemunit() == null) matBomPojo.setItemunit("");
        if (matBomPojo.getQuotahour() == null) matBomPojo.setQuotahour(0D);
        if (matBomPojo.getProgroupid() == null) matBomPojo.setProgroupid("");
        if (matBomPojo.getMintime() == null) matBomPojo.setMintime(0D);
        if (matBomPojo.getSummary() == null) matBomPojo.setSummary("");
        if (matBomPojo.getEnabledmark() == null) matBomPojo.setEnabledmark(0);
        if (matBomPojo.getVersionnum() == null) matBomPojo.setVersionnum("");
        if (matBomPojo.getDeletemark() == null) matBomPojo.setDeletemark(0);
        if (matBomPojo.getDeletelisterid() == null) matBomPojo.setDeletelisterid("");
        if (matBomPojo.getDeletelister() == null) matBomPojo.setDeletelister("");
        if (matBomPojo.getDeletedate() == null) matBomPojo.setDeletedate(new Date());
        if (matBomPojo.getCreateby() == null) matBomPojo.setCreateby("");
        if (matBomPojo.getCreatebyid() == null) matBomPojo.setCreatebyid("");
        if (matBomPojo.getCreatedate() == null) matBomPojo.setCreatedate(new Date());
        if (matBomPojo.getLister() == null) matBomPojo.setLister("");
        if (matBomPojo.getListerid() == null) matBomPojo.setListerid("");
        if (matBomPojo.getModifydate() == null) matBomPojo.setModifydate(new Date());
        if (matBomPojo.getAssessor() == null) matBomPojo.setAssessor("");
        if (matBomPojo.getAssessorid() == null) matBomPojo.setAssessorid("");
        if (matBomPojo.getAssessdate() == null) matBomPojo.setAssessdate(new Date());
        if (matBomPojo.getCustom1() == null) matBomPojo.setCustom1("");
        if (matBomPojo.getCustom2() == null) matBomPojo.setCustom2("");
        if (matBomPojo.getCustom3() == null) matBomPojo.setCustom3("");
        if (matBomPojo.getCustom4() == null) matBomPojo.setCustom4("");
        if (matBomPojo.getCustom5() == null) matBomPojo.setCustom5("");
        if (matBomPojo.getCustom6() == null) matBomPojo.setCustom6("");
        if (matBomPojo.getCustom7() == null) matBomPojo.setCustom7("");
        if (matBomPojo.getCustom8() == null) matBomPojo.setCustom8("");
        if (matBomPojo.getCustom9() == null) matBomPojo.setCustom9("");
        if (matBomPojo.getCustom10() == null) matBomPojo.setCustom10("");
        if (tid == null) matBomPojo.setTenantid("");
        if (matBomPojo.getRevision() == null) matBomPojo.setRevision(0);
        // 加入ItemCount  Eric 20220907
        if (matBomPojo.getItem() != null) matBomPojo.setItemcount(matBomPojo.getItem().size());
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatBomEntity matBomEntity = new MatBomEntity();
        BeanUtils.copyProperties(matBomPojo, matBomEntity);
        //设置id和新建日期
        matBomEntity.setId(id);
        matBomEntity.setRevision(1);  //乐观锁

        //查询是否已有BOM
        MatBomPojo bomPojo = this.matBomMapper.getEntityByGoodsid(matBomPojo.getGoodsid(), tid);
        if (bomPojo != null) {
            throw new RuntimeException(matBomPojo.getGoodsuid() + ": 已有BOM表,禁止新增");
        }
        //插入主表
        this.matBomMapper.insert(matBomEntity);

        //Item子表处理
        List<MatBomitemPojo> lst = matBomPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (MatBomitemPojo matBomitemPojo : lst) {

                // 检查子表的Bom不能是上几层的Bom,避免死循环
                this.checkBomIdCyclic(matBomPojo.getId(), matBomitemPojo.getBomid(), tid);

                //初始化item的NULL
                MatBomitemPojo itemPojo = MatBomitemServiceImpl.clearNullStatic(matBomitemPojo);
                MatBomitemEntity matBomitemEntity = new MatBomitemEntity();
                BeanUtils.copyProperties(itemPojo, matBomitemEntity);
                //设置id和Pid
                if (matBomitemEntity.getId() == null) matBomitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matBomitemEntity.setPid(id);
                matBomitemEntity.setTenantid(tid);
                matBomitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matBomitemMapper.insert(matBomitemEntity);
            }
        }

        // 更新Goods中的bomid Eric 20220415
        this.matBomMapper.updateGoodsBomid(matBomEntity.getGoodsid(), matBomEntity.getId(), matBomEntity.getTenantid());

        //返回Bill实例
        return this.getBillEntity(matBomEntity.getId(), matBomEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matBomPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatBomPojo update(MatBomPojo matBomPojo) {
        String tid = matBomPojo.getTenantid();
        //主表更改
        // 加入ItemCount  Eric 20220907
        if (matBomPojo.getItem() != null) matBomPojo.setItemcount(matBomPojo.getItem().size());
        MatBomEntity matBomEntity = new MatBomEntity();
        BeanUtils.copyProperties(matBomPojo, matBomEntity);
        this.matBomMapper.update(matBomEntity);
        if (matBomPojo.getItem() != null) {
            //Item子表处理
            List<MatBomitemPojo> lst = matBomPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = matBomMapper.getDelItemIds(matBomPojo);
            if (CollectionUtils.isNotEmpty(lstDelIds)) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.matBomitemMapper.delete(lstDelId, matBomEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (MatBomitemPojo matBomitemPojo : lst) {

                    // 检查子表的Bom不能是上几层的Bom,避免死循环
                    this.checkBomIdCyclic(matBomPojo.getId(), matBomitemPojo.getBomid(), tid);

                    MatBomitemEntity matBomitemEntity = new MatBomitemEntity();
                    if (isBlank(matBomitemPojo.getId())) {
                        //初始化item的NULL
                        MatBomitemPojo itemPojo = MatBomitemServiceImpl.clearNullStatic(matBomitemPojo);
                        BeanUtils.copyProperties(itemPojo, matBomitemEntity);
                        //设置id和Pid
                        if (matBomitemEntity.getId() == null)
                            matBomitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matBomitemEntity.setPid(matBomEntity.getId());  // 主表 id
                        matBomitemEntity.setTenantid(tid);   // 租户id
                        matBomitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matBomitemMapper.insert(matBomitemEntity);
                    } else {
                        BeanUtils.copyProperties(matBomitemPojo, matBomitemEntity);
                        matBomitemEntity.setTenantid(tid);
                        this.matBomitemMapper.update(matBomitemEntity);
                    }
                }
            }
        }
        // 更新Goods中的bomid Eric 20220415
        this.matBomMapper.updateGoodsBomid(matBomPojo.getGoodsid(), matBomPojo.getId(), tid);
        //返回Bill实例
        return this.getBillEntity(matBomEntity.getId(), matBomEntity.getTenantid());
    }

    // 检查itemBomid作为bom时，下级bom是否包含bomid
    private void checkBomIdCyclic(String bomid, String itemBomid, String tid) {
        PrintColor.red("checkBomIdCyclic bomid=" + bomid + " itemBomid=" + itemBomid + " tid=" + tid);
        if (StringUtils.isBlank(itemBomid)) return;
        // 构建传入递归的空List和计数器
        List<MatBomPojo> allBomList = new ArrayList<>();
        AtomicInteger atomicCount = new AtomicInteger(0);
        this.getAllBomListRecursive(itemBomid, allBomList, atomicCount, tid);
        // 检查allBomList里的bomid是否包含bomid
        for (MatBomPojo matBomPojo : allBomList) {
            if (bomid.equals(matBomPojo.getId())) {
                throw new BaseBusinessException(matGoodsMapper.getEntity(matBomPojo.getGoodsid(), tid).getGoodsuid() + "是父级Bom,禁止添加");
            }
        }
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        MatBomPojo matBomPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatBomitemPojo> lst = matBomPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (MatBomitemPojo matBomitemPojo : lst) {
                this.matBomitemMapper.delete(matBomitemPojo.getId(), tid);
            }
        }
        this.matBomMapper.delete(key, tid);
        // 更新Goods中的bomid Eric 20220415
        this.matBomMapper.updateGoodsBomid(matBomPojo.getGoodsid(), null, matBomPojo.getTenantid());
        return "goodsid:" + matBomPojo.getGoodsid() + " goodsname:" + matBomPojo.getItemname();
    }


    /**
     * 审核数据
     *
     * @param matBomPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatBomPojo approval(MatBomPojo matBomPojo) {
        //主表更改
        MatBomEntity matBomEntity = new MatBomEntity();
        BeanUtils.copyProperties(matBomPojo, matBomEntity);
        this.matBomMapper.approval(matBomEntity);
        //返回Bill实例
        return this.getBillEntity(matBomEntity.getId(), matBomEntity.getTenantid());
    }

//    /**
//     * 通过ID查询单条数据
//     *
//     * @param key 主键
//     * @return 实例对象
//     */
//    @Override
//    public MatBomtreePojo getTreeEntity(String key, String tid) {
//        try {
//            //读取主表
//            MatBomPojo matBomPojo = this.matBomMapper.getEntity(key, tid);
//            MatBomtreePojo matBomtreePojo = new MatBomtreePojo();
//            BeanUtils.copyProperties(matBomPojo, matBomtreePojo);
//            List<MatBomtreePojo> ls = getTreeitem(matBomPojo.getId(), matBomPojo.getTenantid());
//
//            return matBomtreePojo;
//        } catch (Exception e) {
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }

//    /**
//     * 通过ID查询单条数据
//     *
//     * @param key 主键
//     * @return 实例对象
//     */
//    @Override
//    public List<MatBomdetailPojo> getBomDetailByGoodsid(String key, Double qty, String tid) {
//        try {
//            if (qty == null) qty = 1D;
//            List<MatBomdetailPojo> lst = new ArrayList<>();
//            //读取主表
//            MatBomPojo matBomPojo = this.matBomMapper.getEntityByGoodsid(key, tid);
//            MatBomdetailPojo matBomdetailPojo = new MatBomdetailPojo();
//            BeanUtils.copyProperties(matBomPojo, matBomdetailPojo);
//            matBomdetailPojo.setTreeid(inksSnowflake.getSnowflake().nextIdStr());
//            matBomdetailPojo.setBomqty(qty);
//            matBomdetailPojo.setLevelnum(1);
//            lst.add(matBomdetailPojo);
//            // List<MatBomdetailPojo> ls =
//            getDetailitem(lst, matBomPojo.getId(), matBomdetailPojo.getTreeid(), qty, 2, matBomPojo.getTenantid());
//            // lst.addAll(ls);
//            return lst;
//        } catch (Exception e) {
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomPojo getEntityByGoodsid(String key, String tid) {
        return this.matBomMapper.getEntityByGoodsid(key, tid);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomPojo getBillEntityByGoodsid(String key, Double qty, Integer need, String tid) {
        try {
            //读取主表
            MatBomPojo matBomPojo = this.matBomMapper.getEntityByGoodsid(key, tid);
            if (matBomPojo == null) {
                throw new BaseBusinessException("未找到货品相关标准Bom");
            }
            //读取子表item list
            List<MatBomitemPojo> lst = matBomitemMapper.getList(matBomPojo.getId(), matBomPojo.getTenantid());
            List<MatBomitemPojo> lstNew = new ArrayList<>();
            for (MatBomitemPojo matBomitemPojo : lst) {
                // BOM物料数量 .divide()除以 .multiply()乘
                BigDecimal bomqty = BigDecimal.valueOf(matBomitemPojo.getSubqty()).divide(BigDecimal.valueOf(matBomitemPojo.getMainqty())).multiply(BigDecimal.valueOf(qty));
                if (matBomitemPojo.getLossrate() > 0)
                    bomqty = bomqty.multiply(BigDecimal.valueOf((1 + matBomitemPojo.getLossrate() / 100)));
                matBomitemPojo.setBomqty(bomqty.doubleValue());
                //设置可用数量---仓库+在途-待出
//                MatGoodsPojo matBomitemPojo = matGoodsService.getEntity(matBomitemPojo.getGoodsid(), tid);
                double avaiqtr = matBomitemPojo.getIvquantity() + matBomitemPojo.getBuyremqty() + matBomitemPojo.getWkwsremqty() + matBomitemPojo.getWkscremqty()
                        - matBomitemPojo.getBusremqty() - matBomitemPojo.getMrpremqty() - matBomitemPojo.getRequremqty();
                matBomitemPojo.setAvaiqty(avaiqtr);
                //设置Mrp建议应需数量---if 可用量大于Bom量，等BOM量，否， BOM量-可用量；
                double needqty = bomqty.doubleValue();
                if (need == 1) {
                    needqty = avaiqtr > bomqty.doubleValue() ? bomqty.doubleValue() : bomqty.subtract(BigDecimal.valueOf(avaiqtr)).doubleValue();
                }
                matBomitemPojo.setNeedqty(needqty);
                //设置计划领用数量---if 可用量大于Bom量，等BOM量，否， 可用量；
                matBomitemPojo.setStoplanqty(avaiqtr > bomqty.doubleValue() ? bomqty.doubleValue() : avaiqtr);
                //设置实际需求数量---BOM量-计划领用
                matBomitemPojo.setRealqty(matBomitemPojo.getBomqty() - matBomitemPojo.getStoplanqty());
                lstNew.add(matBomitemPojo);
            }
            matBomPojo.setItem(lstNew);
            return matBomPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public List<MatBomorderitemdetailPojo> getListByItemGoodsid(String key, String tid) {
        try {
            return this.matBomMapper.getListByItemGoodsid(key, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public List<MatBomdetailPojo> getBomDetail(String key, Double qty, String tid) {
        try {

            List<MatBomdetailPojo> lst = new ArrayList<>();
            //读取主表
            MatBomPojo matBomPojo = this.matBomMapper.getEntity(key, tid);
            if (matBomPojo == null) {
                throw new BaseBusinessException("标准BOM表丢失");
            }
            // 加入boot级
            MatBomdetailPojo matBomdetailPojo = new MatBomdetailPojo();
            BeanUtils.copyProperties(matBomPojo, matBomdetailPojo);
            matBomdetailPojo.setTreeid(inksSnowflake.getSnowflake().nextIdStr());
            matBomdetailPojo.setBomqty(qty);
            matBomdetailPojo.setGoodsbomid(matBomPojo.getId());
            matBomdetailPojo.setLevelnum(1);
            lst.add(matBomdetailPojo);
            // 加入第二层，及子层
            getDetailitem(lst, matBomPojo.getId(), matBomdetailPojo.getTreeid(), qty, 2, matBomPojo.getTenantid());
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /*
     bom明细加入子层
     lst 为总清单；
     key  Bomid;
     TreeParentid: 临时树主键
     qty 生产数量；
     tid 租户
     */
    private List<MatBomdetailPojo> getDetailitem(List<MatBomdetailPojo> lst, String key, String Treeid, Double qty, Integer level, String tid) {

        int mrpqtydec = 0;// MRP数量小数位
//        boolean mrpqtyupper = true;// MRP数量上进位
        //读取bom子表
        List<MatBomdetailPojo> lstdetail = this.matBomitemMapper.getDetailList(key, Treeid, tid);// 有查安全库存Mat_Goods.SafeStock
        if (!lstdetail.isEmpty()) {
            for (MatBomdetailPojo matBomdetailPojo : lstdetail) {
                // 临时树id
                matBomdetailPojo.setTreeid(inksSnowflake.getSnowflake().nextIdStr());
                // 物料数量
                Double bomqty = matBomdetailPojo.getSubqty() / matBomdetailPojo.getMainqty() * qty;
                if (matBomdetailPojo.getLossrate() > 0) bomqty = bomqty * (1 + matBomdetailPojo.getLossrate() / 100);

                bomqty = Math.ceil(bomqty * Math.pow(10, mrpqtydec)) / Math.pow(10, mrpqtydec);
                PrintColor.red("=====================bomqty=" + bomqty);
                matBomdetailPojo.setBomqty(bomqty);
                matBomdetailPojo.setLevelnum(level);
                if (matBomdetailPojo.getGoodsbomid() == null) matBomdetailPojo.setGoodsbomid("");
                lst.add(matBomdetailPojo);

                // 货品有Bomid，并在10层内
                if (matBomdetailPojo.getGoodsbomid() != null && !matBomdetailPojo.getGoodsbomid().isEmpty() && level < 10) {
                    logger.info("Bom明细子集 level:" + level);
                    getDetailitem(lst, matBomdetailPojo.getGoodsbomid(), matBomdetailPojo.getTreeid(), bomqty, level + 1, tid);
                }

            }
        }

        return lstdetail;
    }


    @Override
    public MatBomlayerPojo createBomByLayer(MatBomlayerPojo matBomlayerPojo) {
        List<MatBomlayeritemPojo> item = matBomlayerPojo.getItem();
        //添加第一层BOM
        MatBomPojo matBomPojo = new MatBomPojo();
        List<MatBomitemPojo> bomitem = new ArrayList<>();
        List<MatBomlayeritemPojo> list1 = item.stream().filter(a -> "1".equals(a.getLayer1())).collect(Collectors.toList());
        for (int i = 0; i < list1.size(); i++) {
            MatBomitemPojo matBomitemPojo = new MatBomitemPojo();
            BeanUtils.copyProperties(list1.get(i), matBomitemPojo);
            matBomitemPojo.setRownum(i);
            bomitem.add(matBomitemPojo);
        }
        BeanUtils.copyProperties(matBomlayerPojo, matBomPojo);
        matBomPojo.setItem(bomitem);
        addTid(matBomPojo);
        matBomPojo = insert(matBomPojo);
        findBomL2(item);
        findBomL3(item);
        findBomL4(item);
        findBomL5(item);
        findBomL6(item);
//
//        Thread thread1 = new Thread(() -> {
//            findBomL2(item);
//        });
//        thread1.start();
//        Thread thread2 = new Thread(() -> {
//            findBomL3(item);
//        });
//        thread2.start();
//        Thread thread3 = new Thread(() -> {
//            findBomL4(item);
//        });
//        thread3.start();
//        Thread thread4 = new Thread(() -> {
//            findBomL5(item);
//        });
//        thread4.start();
//        Thread thread5 = new Thread(() -> {
//            findBomL6(item);
//        });
//        thread5.start();

        return getBillEntityByLayer(matBomPojo.getId(), matBomPojo.getTenantid());
    }


    @Override   //key:bom主表的id
    public MatBomlayerPojo getBillEntityByLayer(String key, String tenantid) {
        MatBomPojo matBomPojo = getBillEntity(key, tenantid);
        //BOM主表转为BOMlayer主表
        MatBomlayerPojo matBomlayerPojo = new MatBomlayerPojo();
        BeanUtils.copyProperties(matBomPojo, matBomlayerPojo);
        //layeritems用来存储所有BOMlayer子表
        List<MatBomlayeritemPojo> layeritems = new ArrayList<>();

        List<MatBomitemPojo> item = matBomPojo.getItem();
        //查询第一层
        for (MatBomitemPojo matBomitemPojo : item) {
            MatBomlayeritemPojo newlayitem = new MatBomlayeritemPojo();
            BeanUtils.copyProperties(matBomitemPojo, newlayitem);
            //设置层级：
            newlayitem.setLayernum(1);
            newlayitem.setLayer1("1");
            layeritems.add(newlayitem);
            //获取当前L1作为主表时的id，作为Pid传入getList
            MatBomPojo entityByGoodsid = matBomMapper.getEntityByGoodsid(matBomitemPojo.getGoodsid(), tenantid);
            if (entityByGoodsid != null) {
                String Bomid = entityByGoodsid.getId();
                List<MatBomitemPojo> list = matBomitemMapper.getList(Bomid, tenantid);
                //查询第二层
                for (MatBomitemPojo bomitemPojo2 : list) {
                    MatBomlayeritemPojo newlayitem2 = new MatBomlayeritemPojo();
                    BeanUtils.copyProperties(bomitemPojo2, newlayitem2);
                    //设置层级：
                    newlayitem2.setLayernum(2);
                    newlayitem2.setLayer2("2");
                    layeritems.add(newlayitem2);
                    MatBomPojo entityByGoodsid2 = matBomMapper.getEntityByGoodsid(bomitemPojo2.getGoodsid(), tenantid);
                    if (entityByGoodsid2 != null) {
//                String Bomid2 = entityByGoodsid2.getId();
                        List<MatBomitemPojo> list2 = matBomitemMapper.getList(entityByGoodsid2.getId(), tenantid);
                        //查询第三层
                        for (MatBomitemPojo bomitemPojo3 : list2) {
                            MatBomlayeritemPojo newlayitem3 = new MatBomlayeritemPojo();
                            BeanUtils.copyProperties(bomitemPojo3, newlayitem3);
                            //设置层级：
                            newlayitem3.setLayernum(3);
                            newlayitem3.setLayer3("3");
                            layeritems.add(newlayitem3);
                            MatBomPojo entityByGoodsid3 = matBomMapper.getEntityByGoodsid(bomitemPojo3.getGoodsid(), tenantid);
                            if (entityByGoodsid3 != null) {
                                String Bomid3 = entityByGoodsid3.getId();
                                List<MatBomitemPojo> list3 = matBomitemMapper.getList(Bomid3, tenantid);
                                //查询第四层
                                for (MatBomitemPojo bomitemPojo4 : list3) {
                                    MatBomlayeritemPojo newlayitem4 = new MatBomlayeritemPojo();
                                    BeanUtils.copyProperties(bomitemPojo4, newlayitem4);
                                    //设置层级：
                                    newlayitem4.setLayernum(4);
                                    newlayitem4.setLayer4("4");
                                    layeritems.add(newlayitem4);
                                    MatBomPojo entityByGoodsid4 = matBomMapper.getEntityByGoodsid(bomitemPojo4.getGoodsid(), tenantid);
                                    if (entityByGoodsid4 != null) {
                                        String Bomid4 = entityByGoodsid4.getId();
                                        List<MatBomitemPojo> list4 = matBomitemMapper.getList(Bomid4, tenantid);
                                        //查询第5层
                                        for (MatBomitemPojo bomitemPojo5 : list4) {
                                            MatBomlayeritemPojo newlayitem5 = new MatBomlayeritemPojo();
                                            BeanUtils.copyProperties(bomitemPojo5, newlayitem5);
                                            //设置层级：
                                            newlayitem5.setLayernum(5);
                                            newlayitem5.setLayer5("5");
                                            layeritems.add(newlayitem5);
                                            MatBomPojo entityByGoodsid5 = matBomMapper.getEntityByGoodsid(bomitemPojo5.getGoodsid(), tenantid);
                                            if (entityByGoodsid5 != null) {
                                                String Bomid5 = entityByGoodsid5.getId();
                                                List<MatBomitemPojo> list5 = matBomitemMapper.getList(Bomid5, tenantid);
                                                //查询第6层
                                                for (MatBomitemPojo bomitemPojo6 : list5) {
                                                    MatBomlayeritemPojo newlayitem6 = new MatBomlayeritemPojo();
                                                    BeanUtils.copyProperties(bomitemPojo6, newlayitem6);
                                                    //设置层级：
                                                    newlayitem6.setLayernum(6);
                                                    newlayitem6.setLayer6("6");
                                                    layeritems.add(newlayitem6);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        //设置物料setMatmark
        layeritems.stream().forEach(a -> {
            if ("".equals(a.getBomid())) {
                a.setMatmark(1);
            } else {
                a.setMatmark(0);
            }
        });
        matBomlayerPojo.setItem(layeritems);
        return matBomlayerPojo;
    }

//    private void getBomL1() {
//        for (MatBomitemPojo matBomitemPojo : item) {
//            MatBomlayeritemPojo newlayitem = new MatBomlayeritemPojo();
//            BeanUtils.copyProperties(matBomitemPojo, newlayitem);
//            //设置层级：
//            newlayitem.setLayernum(1);
//            newlayitem.setLayer1("1");
//            layeritems.add(newlayitem);
//            //获取当前L1作为主表时的id，作为Pid传入getList
//            MatBomPojo entityByGoodsid = matBomMapper.getEntityByGoodsid(matBomitemPojo.getGoodsid(), tenantid);
//            String Bomid = entityByGoodsid.getId();
//            List<MatBomitemPojo> list = matBomitemMapper.getList(Bomid, tenantid);
//        }
//    }


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public List<MrpBomdetailPojo> getMrpBomDetail(String key, Double qty, String tid) {
        try {
            if (qty == null) qty = 1D;
            List<MatBomdetailPojo> lst = new ArrayList<>();
            //读取主表
            MatBomPojo matBomPojo = this.matBomMapper.getEntity(key, tid);
            MatBomdetailPojo matBomdetailPojo = new MatBomdetailPojo();
            BeanUtils.copyProperties(matBomPojo, matBomdetailPojo);
            matBomdetailPojo.setTreeid(inksSnowflake.getSnowflake().nextIdStr());
            matBomdetailPojo.setBomqty(qty);
            matBomdetailPojo.setLevelnum(1);
            lst.add(matBomdetailPojo);
            getDetailitem(lst, matBomPojo.getId(), matBomdetailPojo.getTreeid(), qty, 2, matBomPojo.getTenantid());
            return null;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override   //key:bom主表的id
    public MatBomlayerPojo getItemAllByLayer(String key, String tenantid, Double qty, Integer need) {
        // 关联查询了BomItem.Goodsid的货品信息(Bomid,库存数量+采购在订+生产在制+加工在制-销售待出-运算占用-领料待出)
        MatBomPojo matBomPojo = getBillEntity(key, tenantid);
        //BOM主表转为BOMlayer主表
        MatBomlayerPojo matBomlayerPojo = new MatBomlayerPojo();
        BeanUtils.copyProperties(matBomPojo, matBomlayerPojo);
        //layeritems用来存储所有BOMlayer子表
        List<MatBomlayeritemPojo> layeritems = new ArrayList<>();
        List<MatBomitemPojo> item = matBomPojo.getItem();
        //查询第一层
        for (MatBomitemPojo matBomitemPojo : item) {
            MatBomlayeritemPojo newlayitem = new MatBomlayeritemPojo();
            BeanUtils.copyProperties(matBomitemPojo, newlayitem);
            //设置层级：
            newlayitem.setLayernum(1);
            newlayitem.setLayer1("1");
            // 物料数量（BOM量） .divide()除以 .multiply()乘
            BigDecimal bomqty = BigDecimal.valueOf(newlayitem.getSubqty()).divide(BigDecimal.valueOf(newlayitem.getMainqty())).multiply(BigDecimal.valueOf(qty));
            if (newlayitem.getLossrate() > 0)
                bomqty = bomqty.multiply(BigDecimal.valueOf((1 + newlayitem.getLossrate() / 100)));
            newlayitem.setBomqty(bomqty.doubleValue());
            //设置可用数量---仓库+在途-待出 (库存数量+采购在订+生产在制+加工在制-销售待出-运算占用-领料待出)
            Double avaiqtr = matBomitemPojo.getIvquantity() + matBomitemPojo.getBuyremqty() + matBomitemPojo.getWkwsremqty() + matBomitemPojo.getWkscremqty()
                    - matBomitemPojo.getBusremqty() - matBomitemPojo.getMrpremqty() - matBomitemPojo.getRequremqty();
            newlayitem.setAvaiqty(avaiqtr);
            //设置Mrp建议应需数量---if 可用量大于Bom量，等BOM量，否， BOM量-可用量；
            Double needqty = bomqty.doubleValue();
            if (need == 1) {
                needqty = avaiqtr > bomqty.doubleValue() ? bomqty.doubleValue() : bomqty.subtract(BigDecimal.valueOf(avaiqtr)).doubleValue();
            }
            newlayitem.setNeedqty(needqty);
            //设置计划领用数量---if 可用量大于Bom量，等BOM量，否， 可用量；
            newlayitem.setStoplanqty(avaiqtr > bomqty.doubleValue() ? bomqty.doubleValue() : avaiqtr);
            //设置实际需求数量---BOM量-计划领用
            newlayitem.setRealqty(newlayitem.getBomqty() - newlayitem.getStoplanqty());
            newlayitem.setGoodsstate(matBomitemPojo.getGoodsstate());
            layeritems.add(newlayitem);

            if (isNotBlank(newlayitem.getBomid())) {
                BigDecimal subBomqty2 = null;
                if (need == 1) {
                    subBomqty2 = BigDecimal.valueOf(newlayitem.getRealqty());
                }
                List<MatBomitemPojo> list = matBomitemMapper.getList(newlayitem.getBomid(), tenantid);
                //查询第二层
                for (MatBomitemPojo bomitemPojo2 : list) {
                    MatBomlayeritemPojo newlayitem2 = new MatBomlayeritemPojo();
                    BeanUtils.copyProperties(bomitemPojo2, newlayitem2);
                    //设置层级：
                    newlayitem2.setLayernum(2);
                    newlayitem2.setLayer2("2");
                    // 物料数量
                    BigDecimal bomqty2 = BigDecimal.valueOf(newlayitem2.getSubqty()).divide(BigDecimal.valueOf(newlayitem2.getMainqty())).multiply(subBomqty2);
                    if (newlayitem2.getLossrate() > 0)
                        bomqty2 = bomqty2.multiply(BigDecimal.valueOf((1 + newlayitem2.getLossrate() / 100)));
                    newlayitem2.setBomqty(bomqty2.doubleValue());
                    //设置可用数量------
                    Double avaiqtr2 = bomitemPojo2.getIvquantity() + bomitemPojo2.getBuyremqty() + bomitemPojo2.getWkwsremqty() + bomitemPojo2.getWkscremqty()
                            - bomitemPojo2.getBusremqty() - bomitemPojo2.getMrpremqty() - bomitemPojo2.getRequremqty();
                    //设置Mrp应需数量------
                    Double needqty2 = bomqty2.doubleValue();
                    if (need == 1) {
                        needqty2 = avaiqtr2 > bomqty2.doubleValue() ? bomqty2.doubleValue() : bomqty2.subtract(BigDecimal.valueOf(avaiqtr2)).doubleValue();
                    }
                    newlayitem2.setNeedqty(needqty2);
                    //设置计划领用数量------
                    newlayitem2.setStoplanqty(avaiqtr2 > bomqty2.doubleValue() ? bomqty2.doubleValue() : avaiqtr2);
                    //设置实际需求数量------
                    newlayitem2.setRealqty(newlayitem2.getBomqty() - newlayitem2.getStoplanqty());
                    newlayitem2.setGoodsstate(bomitemPojo2.getGoodsstate());
                    layeritems.add(newlayitem2);

                    if (isNotBlank(newlayitem2.getBomid())) {
                        BigDecimal subBomqty3 = null;
                        if (need == 1) {
                            subBomqty3 = BigDecimal.valueOf(newlayitem2.getRealqty());
                        }
                        List<MatBomitemPojo> list2 = matBomitemMapper.getList(newlayitem2.getBomid(), tenantid);
                        //查询第三层
                        for (MatBomitemPojo bomitemPojo3 : list2) {
                            MatBomlayeritemPojo newlayitem3 = new MatBomlayeritemPojo();
                            BeanUtils.copyProperties(bomitemPojo3, newlayitem3);
                            //设置层级：
                            newlayitem3.setLayernum(3);
                            newlayitem3.setLayer3("3");
                            // 物料数量
                            BigDecimal bomqty3 = BigDecimal.valueOf(newlayitem3.getSubqty()).divide(BigDecimal.valueOf(newlayitem3.getMainqty())).multiply(subBomqty3);
                            if (newlayitem3.getLossrate() > 0)
                                bomqty3 = bomqty3.multiply(BigDecimal.valueOf((1 + newlayitem3.getLossrate() / 100)));
                            newlayitem3.setBomqty(bomqty3.doubleValue());

                            //设置可用数量------
                            Double avaiqtr3 = bomitemPojo3.getIvquantity() + bomitemPojo3.getBuyremqty() + bomitemPojo3.getWkwsremqty() + bomitemPojo3.getWkscremqty()
                                    - bomitemPojo3.getBusremqty() - bomitemPojo3.getMrpremqty() - bomitemPojo3.getRequremqty();
                            //设置Mrp应需数量------
                            Double needqty3 = bomqty3.doubleValue();
                            if (need == 1) {
                                needqty3 = avaiqtr3 > bomqty3.doubleValue() ? bomqty3.doubleValue() : bomqty3.subtract(BigDecimal.valueOf(avaiqtr3)).doubleValue();
                            }
                            newlayitem3.setNeedqty(needqty3);
                            //设置计划领用数量------
                            newlayitem3.setStoplanqty(avaiqtr3 > bomqty3.doubleValue() ? bomqty3.doubleValue() : avaiqtr3);
                            //设置实际需求数量------
                            newlayitem3.setRealqty(newlayitem3.getBomqty() - newlayitem3.getStoplanqty());
                            newlayitem3.setGoodsstate(bomitemPojo3.getGoodsstate());
                            layeritems.add(newlayitem3);

                            if (isNotBlank(newlayitem3.getBomid())) {
                                BigDecimal subBomqty4 = null;
                                if (need == 1) {
                                    subBomqty4 = BigDecimal.valueOf(newlayitem3.getRealqty());
                                }
//                                String Bomid3 = entityByGoodsid3.getId();
                                List<MatBomitemPojo> list3 = matBomitemMapper.getList(newlayitem3.getBomid(), tenantid);
                                //查询第四层
                                for (MatBomitemPojo bomitemPojo4 : list3) {
                                    MatBomlayeritemPojo newlayitem4 = new MatBomlayeritemPojo();
                                    BeanUtils.copyProperties(bomitemPojo4, newlayitem4);
                                    //设置层级：
                                    newlayitem4.setLayernum(4);
                                    newlayitem4.setLayer4("4");
                                    // 物料数量
                                    BigDecimal bomqty4 = BigDecimal.valueOf(newlayitem4.getSubqty()).divide(BigDecimal.valueOf(newlayitem4.getMainqty())).multiply(subBomqty4);
                                    if (newlayitem4.getLossrate() > 0)
                                        bomqty4 = bomqty4.multiply(BigDecimal.valueOf((1 + newlayitem4.getLossrate() / 100)));
                                    newlayitem4.setBomqty(bomqty4.doubleValue());

                                    //设置可用数量------
                                    Double avaiqtr4 = bomitemPojo4.getIvquantity() + bomitemPojo4.getBuyremqty() + bomitemPojo4.getWkwsremqty() + bomitemPojo4.getWkscremqty()
                                            - bomitemPojo4.getBusremqty() - bomitemPojo4.getMrpremqty() - bomitemPojo4.getRequremqty();
                                    newlayitem4.setAvaiqty(avaiqtr4);
                                    //设置Mrp应需数量------
                                    Double needqty4 = bomqty4.doubleValue();
                                    if (need == 1) {
                                        needqty4 = avaiqtr4 > bomqty4.doubleValue() ? bomqty4.doubleValue() : bomqty4.subtract(BigDecimal.valueOf(avaiqtr4)).doubleValue();
                                    }
                                    newlayitem4.setNeedqty(needqty4);
                                    //设置计划领用数量------
                                    newlayitem4.setStoplanqty(avaiqtr4 > bomqty4.doubleValue() ? bomqty4.doubleValue() : avaiqtr4);
                                    //设置实际需求数量------
                                    newlayitem4.setRealqty(newlayitem4.getBomqty() - newlayitem4.getStoplanqty());
                                    newlayitem4.setGoodsstate(bomitemPojo4.getGoodsstate());

                                    layeritems.add(newlayitem4);
                                    String Bomid4 = matBomMapper.getBomidByGoodsid(bomitemPojo4.getGoodsid(), tenantid);
                                    if (isNotBlank(newlayitem4.getBomid())) {
                                        BigDecimal subBomqty5 = null;
                                        if (need == 1) {
                                            subBomqty5 = BigDecimal.valueOf(newlayitem4.getRealqty());
                                        }
//                                        String Bomid4 = entityByGoodsid4.getId();
                                        List<MatBomitemPojo> list4 = matBomitemMapper.getList(newlayitem4.getBomid(), tenantid);
                                        //查询第5层
                                        for (MatBomitemPojo bomitemPojo5 : list4) {
                                            MatBomlayeritemPojo newlayitem5 = new MatBomlayeritemPojo();
                                            BeanUtils.copyProperties(bomitemPojo5, newlayitem5);
                                            //设置层级：
                                            newlayitem5.setLayernum(5);
                                            newlayitem5.setLayer5("5");
                                            // 物料数量
                                            BigDecimal bomqty5 = BigDecimal.valueOf(newlayitem5.getSubqty()).divide(BigDecimal.valueOf(newlayitem5.getMainqty())).multiply(subBomqty5);
                                            if (newlayitem5.getLossrate() > 0)
                                                bomqty5 = bomqty5.multiply(BigDecimal.valueOf((1 + newlayitem5.getLossrate() / 100)));
                                            newlayitem5.setBomqty(bomqty5.doubleValue());

                                            //设置可用数量------
                                            Double avaiqtr5 = bomitemPojo5.getIvquantity() + bomitemPojo5.getBuyremqty() + bomitemPojo5.getWkwsremqty() + bomitemPojo5.getWkscremqty()
                                                    - bomitemPojo5.getBusremqty() - bomitemPojo5.getMrpremqty() - bomitemPojo5.getRequremqty();
                                            newlayitem5.setAvaiqty(avaiqtr5);
                                            //设置Mrp应需数量------
                                            Double needqty5 = bomqty5.doubleValue();
                                            if (need == 1) {
                                                needqty5 = avaiqtr5 > bomqty5.doubleValue() ? bomqty5.doubleValue() : bomqty5.subtract(BigDecimal.valueOf(avaiqtr5)).doubleValue();
                                            }
                                            newlayitem5.setNeedqty(needqty5);
                                            //设置计划领用数量------
                                            newlayitem5.setStoplanqty(avaiqtr5 > bomqty5.doubleValue() ? bomqty5.doubleValue() : avaiqtr5);
                                            //设置实际需求数量------
                                            newlayitem5.setRealqty(newlayitem5.getBomqty() - newlayitem5.getStoplanqty());
                                            newlayitem5.setGoodsstate(bomitemPojo5.getGoodsstate());

                                            layeritems.add(newlayitem5);
                                            String Bomid5 = matBomMapper.getBomidByGoodsid(bomitemPojo5.getGoodsid(), tenantid);
                                            if (isNotBlank(newlayitem5.getBomid())) {
                                                BigDecimal subBomqty6 = null;
                                                if (need == 1) {
                                                    subBomqty6 = BigDecimal.valueOf(newlayitem5.getRealqty());
                                                }
//                                                String Bomid5 = entityByGoodsid5.getId();
                                                List<MatBomitemPojo> list5 = matBomitemMapper.getList(newlayitem5.getBomid(), tenantid);
                                                //查询第6层
                                                for (MatBomitemPojo bomitemPojo6 : list5) {
                                                    MatBomlayeritemPojo newlayitem6 = new MatBomlayeritemPojo();
                                                    BeanUtils.copyProperties(bomitemPojo6, newlayitem6);
                                                    //设置层级：
                                                    newlayitem6.setLayernum(6);
                                                    newlayitem6.setLayer6("6");
                                                    // 物料数量
                                                    BigDecimal bomqty6 = BigDecimal.valueOf(newlayitem6.getSubqty()).divide(BigDecimal.valueOf(newlayitem6.getMainqty())).multiply(subBomqty6);
                                                    if (newlayitem6.getLossrate() > 0)
                                                        bomqty6 = bomqty6.multiply(BigDecimal.valueOf((1 + newlayitem6.getLossrate() / 100)));
                                                    newlayitem6.setBomqty(bomqty6.doubleValue());

                                                    //设置可用数量------
                                                    Double avaiqtr6 = bomitemPojo6.getIvquantity() + bomitemPojo6.getBuyremqty() + bomitemPojo6.getWkwsremqty() + bomitemPojo6.getWkscremqty()
                                                            - bomitemPojo6.getBusremqty() - bomitemPojo6.getMrpremqty() - bomitemPojo6.getRequremqty();
                                                    newlayitem6.setAvaiqty(avaiqtr6);
                                                    //设置Mrp应需数量------
                                                    Double needqty6 = bomqty6.doubleValue();
                                                    if (need == 1) {
                                                        needqty6 = avaiqtr6 > bomqty6.doubleValue() ? bomqty6.doubleValue() : bomqty6.subtract(BigDecimal.valueOf(avaiqtr6)).doubleValue();
                                                    }
                                                    newlayitem6.setNeedqty(needqty6);
                                                    //设置计划领用数量------
                                                    newlayitem6.setStoplanqty(avaiqtr6 > bomqty6.doubleValue() ? bomqty6.doubleValue() : avaiqtr6);
                                                    //设置实际需求数量------
                                                    newlayitem6.setRealqty(newlayitem6.getBomqty() - newlayitem6.getStoplanqty());
                                                    newlayitem6.setGoodsstate(bomitemPojo6.getGoodsstate());

                                                    layeritems.add(newlayitem6);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        //设置物料setMatmark
        layeritems.forEach(a -> {
            if ("".equals(a.getBomid())) {
                a.setMatmark(1);
            } else {
                a.setMatmark(0);
            }
            //            String goodsStates = matGoodsService.getGoodsstate(a.getGoodsid(), tenantid);
//            a.setGoodsstate(goodsStates);  // 改为上面设置了
        });
        matBomlayerPojo.setItem(layeritems);
        return matBomlayerPojo;
    }


    @Override
    @Async//key:bom主表的id
    public void getItemAllByLayerStart(String hKey, String key, String tenantid, Double qty, Integer need) {
        // 关联查询了BomItem.Goodsid的货品信息(Bomid,库存数量+采购在订+生产在制+加工在制-销售待出-运算占用-领料待出)
        MatBomPojo matBomPojo = getBillEntity(key, tenantid);
        //BOM主表转为BOMlayer主表
        MatBomlayerPojo matBomlayerPojo = new MatBomlayerPojo();
        BeanUtils.copyProperties(matBomPojo, matBomlayerPojo);
        //layeritems用来存储所有BOMlayer子表
        List<MatBomlayeritemPojo> layeritems = new ArrayList<>();
        List<MatBomitemPojo> item = matBomPojo.getItem();
        int totalCount = item.size();
        PrintColor.red("=============总数totalCount：" + totalCount);
        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "100"); //开始处理代码
        missionMsg.put("msg", "任务开始处理");
        missionMsg.put("totalCount", totalCount);
        missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
        this.redisService.setCacheMapValue(MyConstant.BOM_CALCULATION, hKey, missionMsg);
        int successCount = 0;
        int failCount = 0;

        //查询第一层
        for (MatBomitemPojo matBomitemPojo : item) {
            MatBomlayeritemPojo newlayitem = new MatBomlayeritemPojo();
            BeanUtils.copyProperties(matBomitemPojo, newlayitem);
            //设置层级：
            newlayitem.setLayernum(1);
            newlayitem.setLayer1("1");
            // 物料数量 .divide()除以 .multiply()乘
            BigDecimal bomqty = BigDecimal.valueOf(newlayitem.getSubqty()).divide(BigDecimal.valueOf(newlayitem.getMainqty())).multiply(BigDecimal.valueOf(qty));
            if (newlayitem.getLossrate() > 0)
                bomqty = bomqty.multiply(BigDecimal.valueOf((1 + newlayitem.getLossrate() / 100)));
            newlayitem.setBomqty(bomqty.doubleValue());
            //设置可用数量---仓库+在途-待出
//            MatGoodsPojo goodsPojo = matGoodsService.getEntity(matBomitemPojo.getGoodsid(), tenantid);
            Double avaiqtr = matBomitemPojo.getIvquantity() + matBomitemPojo.getBuyremqty() + matBomitemPojo.getWkwsremqty() + matBomitemPojo.getWkscremqty()
                    - matBomitemPojo.getBusremqty() - matBomitemPojo.getMrpremqty() - matBomitemPojo.getRequremqty();
            newlayitem.setAvaiqty(avaiqtr);
            //设置Mrp建议应需数量---if 可用量大于Bom量，等BOM量，否， BOM量-可用量；
            Double needqty = bomqty.doubleValue();
            if (need == 1) {
                needqty = avaiqtr > bomqty.doubleValue() ? bomqty.doubleValue() : bomqty.subtract(BigDecimal.valueOf(avaiqtr)).doubleValue();
            }
            newlayitem.setNeedqty(needqty);
            //设置计划领用数量---if 可用量大于Bom量，等BOM量，否， 可用量；
            newlayitem.setStoplanqty(avaiqtr > bomqty.doubleValue() ? bomqty.doubleValue() : avaiqtr);
            //设置实际需求数量---BOM量-计划领用
            newlayitem.setRealqty(newlayitem.getBomqty() - newlayitem.getStoplanqty());
            newlayitem.setGoodsstate(matBomitemPojo.getGoodsstate());
            layeritems.add(newlayitem);
            successCount++;
            saveCountInRedis(hKey, missionMsg, totalCount, successCount);
            //获取当前L1作为主表时的id，作为Pid传入getList
            if (isNotBlank(newlayitem.getBomid())) {
                BigDecimal subBomqty2 = null;
                if (need == 1) {
                    subBomqty2 = BigDecimal.valueOf(newlayitem.getRealqty());
                }
//                String Bomid = entityByGoodsid.getId();
                List<MatBomitemPojo> list = matBomitemMapper.getList(newlayitem.getBomid(), tenantid);
                //查询第二层
                for (MatBomitemPojo bomitemPojo2 : list) {
                    MatBomlayeritemPojo newlayitem2 = new MatBomlayeritemPojo();
                    BeanUtils.copyProperties(bomitemPojo2, newlayitem2);
                    //设置层级：
                    newlayitem2.setLayernum(2);
                    newlayitem2.setLayer2("2");
                    // 物料数量
                    BigDecimal bomqty2 = BigDecimal.valueOf(newlayitem2.getSubqty()).divide(BigDecimal.valueOf(newlayitem2.getMainqty())).multiply(subBomqty2);
                    if (newlayitem2.getLossrate() > 0)
                        bomqty2 = bomqty2.multiply(BigDecimal.valueOf((1 + newlayitem2.getLossrate() / 100)));
                    newlayitem2.setBomqty(bomqty2.doubleValue());
                    //设置可用数量------
//                    MatGoodsPojo goodsPojo2 = matGoodsService.getEntity(bomitemPojo2.getGoodsid(), tenantid);
                    Double avaiqtr2 = bomitemPojo2.getIvquantity() + bomitemPojo2.getBuyremqty() + bomitemPojo2.getWkwsremqty() + bomitemPojo2.getWkscremqty()
                            - bomitemPojo2.getBusremqty() - bomitemPojo2.getMrpremqty() - bomitemPojo2.getRequremqty();
                    //设置Mrp应需数量------
                    Double needqty2 = bomqty2.doubleValue();
                    if (need == 1) {
                        needqty2 = avaiqtr2 > bomqty2.doubleValue() ? bomqty2.doubleValue() : bomqty2.subtract(BigDecimal.valueOf(avaiqtr2)).doubleValue();
                    }
                    newlayitem2.setNeedqty(needqty2);
                    //设置计划领用数量------
                    newlayitem2.setStoplanqty(avaiqtr2 > bomqty2.doubleValue() ? bomqty2.doubleValue() : avaiqtr2);
                    //设置实际需求数量------
                    newlayitem2.setRealqty(newlayitem2.getBomqty() - newlayitem2.getStoplanqty());
                    newlayitem2.setGoodsstate(bomitemPojo2.getGoodsstate());
                    layeritems.add(newlayitem2);
                    successCount++;
                    saveCountInRedis(hKey, missionMsg, totalCount, successCount);
                    if (isNotBlank(newlayitem2.getBomid())) {
                        BigDecimal subBomqty3 = null;
                        if (need == 1) {
                            subBomqty3 = BigDecimal.valueOf(newlayitem2.getRealqty());
                        }
                        List<MatBomitemPojo> list2 = matBomitemMapper.getList(newlayitem2.getBomid(), tenantid);
                        //查询第三层
                        for (MatBomitemPojo bomitemPojo3 : list2) {
                            MatBomlayeritemPojo newlayitem3 = new MatBomlayeritemPojo();
                            BeanUtils.copyProperties(bomitemPojo3, newlayitem3);
                            //设置层级：
                            newlayitem3.setLayernum(3);
                            newlayitem3.setLayer3("3");
                            // 物料数量
                            BigDecimal bomqty3 = BigDecimal.valueOf(newlayitem3.getSubqty()).divide(BigDecimal.valueOf(newlayitem3.getMainqty())).multiply(subBomqty3);
                            if (newlayitem3.getLossrate() > 0)
                                bomqty3 = bomqty3.multiply(BigDecimal.valueOf((1 + newlayitem3.getLossrate() / 100)));
                            newlayitem3.setBomqty(bomqty3.doubleValue());

                            //设置可用数量------
//                            MatGoodsPojo goodsPojo3 = matGoodsService.getEntity(bomitemPojo3.getGoodsid(), tenantid);
                            Double avaiqtr3 = bomitemPojo3.getIvquantity() + bomitemPojo3.getBuyremqty() + bomitemPojo3.getWkwsremqty() + bomitemPojo3.getWkscremqty()
                                    - bomitemPojo3.getBusremqty() - bomitemPojo3.getMrpremqty() - bomitemPojo3.getRequremqty();
                            //设置Mrp应需数量------
                            Double needqty3 = bomqty3.doubleValue();
                            if (need == 1) {
                                needqty3 = avaiqtr3 > bomqty3.doubleValue() ? bomqty3.doubleValue() : bomqty3.subtract(BigDecimal.valueOf(avaiqtr3)).doubleValue();
                            }
                            newlayitem3.setNeedqty(needqty3);
                            //设置计划领用数量------
                            newlayitem3.setStoplanqty(avaiqtr3 > bomqty3.doubleValue() ? bomqty3.doubleValue() : avaiqtr3);
                            //设置实际需求数量------
                            newlayitem3.setRealqty(newlayitem3.getBomqty() - newlayitem3.getStoplanqty());
                            newlayitem3.setGoodsstate(bomitemPojo3.getGoodsstate());
                            layeritems.add(newlayitem3);
                            successCount++;
                            saveCountInRedis(hKey, missionMsg, totalCount, successCount);
                            if (isNotBlank(newlayitem3.getBomid())) {
                                BigDecimal subBomqty4 = null;
                                if (need == 1) {
                                    subBomqty4 = BigDecimal.valueOf(newlayitem3.getRealqty());
                                }
//                                String Bomid3 = entityByGoodsid3.getId();
                                List<MatBomitemPojo> list3 = matBomitemMapper.getList(newlayitem3.getBomid(), tenantid);
                                //查询第四层
                                for (MatBomitemPojo bomitemPojo4 : list3) {
                                    MatBomlayeritemPojo newlayitem4 = new MatBomlayeritemPojo();
                                    BeanUtils.copyProperties(bomitemPojo4, newlayitem4);
                                    //设置层级：
                                    newlayitem4.setLayernum(4);
                                    newlayitem4.setLayer4("4");
                                    // 物料数量
                                    BigDecimal bomqty4 = BigDecimal.valueOf(newlayitem4.getSubqty()).divide(BigDecimal.valueOf(newlayitem4.getMainqty())).multiply(subBomqty4);
                                    if (newlayitem4.getLossrate() > 0)
                                        bomqty4 = bomqty4.multiply(BigDecimal.valueOf((1 + newlayitem4.getLossrate() / 100)));
                                    newlayitem4.setBomqty(bomqty4.doubleValue());

                                    //设置可用数量------
//                                    MatGoodsPojo goodsPojo4 = matGoodsService.getEntity(bomitemPojo4.getGoodsid(), tenantid);
                                    Double avaiqtr4 = bomitemPojo4.getIvquantity() + bomitemPojo4.getBuyremqty() + bomitemPojo4.getWkwsremqty() + bomitemPojo4.getWkscremqty()
                                            - bomitemPojo4.getBusremqty() - bomitemPojo4.getMrpremqty() - bomitemPojo4.getRequremqty();
                                    newlayitem4.setAvaiqty(avaiqtr4);
                                    //设置Mrp应需数量------
                                    Double needqty4 = bomqty4.doubleValue();
                                    if (need == 1) {
                                        needqty4 = avaiqtr4 > bomqty4.doubleValue() ? bomqty4.doubleValue() : bomqty4.subtract(BigDecimal.valueOf(avaiqtr4)).doubleValue();
                                    }
                                    newlayitem4.setNeedqty(needqty4);
                                    //设置计划领用数量------
                                    newlayitem4.setStoplanqty(avaiqtr4 > bomqty4.doubleValue() ? bomqty4.doubleValue() : avaiqtr4);
                                    //设置实际需求数量------
                                    newlayitem4.setRealqty(newlayitem4.getBomqty() - newlayitem4.getStoplanqty());
                                    newlayitem4.setGoodsstate(bomitemPojo4.getGoodsstate());

                                    layeritems.add(newlayitem4);
                                    successCount++;
                                    saveCountInRedis(hKey, missionMsg, totalCount, successCount);
                                    if (isNotBlank(newlayitem4.getBomid())) {
                                        BigDecimal subBomqty5 = null;
                                        if (need == 1) {
                                            subBomqty5 = BigDecimal.valueOf(newlayitem4.getRealqty());
                                        }
//                                        String Bomid4 = entityByGoodsid4.getId();
                                        List<MatBomitemPojo> list4 = matBomitemMapper.getList(newlayitem4.getBomid(), tenantid);
                                        //查询第5层
                                        for (MatBomitemPojo bomitemPojo5 : list4) {
                                            MatBomlayeritemPojo newlayitem5 = new MatBomlayeritemPojo();
                                            BeanUtils.copyProperties(bomitemPojo5, newlayitem5);
                                            //设置层级：
                                            newlayitem5.setLayernum(5);
                                            newlayitem5.setLayer5("5");
                                            // 物料数量
                                            BigDecimal bomqty5 = BigDecimal.valueOf(newlayitem5.getSubqty()).divide(BigDecimal.valueOf(newlayitem5.getMainqty())).multiply(subBomqty5);
                                            if (newlayitem5.getLossrate() > 0)
                                                bomqty5 = bomqty5.multiply(BigDecimal.valueOf((1 + newlayitem5.getLossrate() / 100)));
                                            newlayitem5.setBomqty(bomqty5.doubleValue());

                                            //设置可用数量------
//                                            MatGoodsPojo goodsPojo5 = matGoodsService.getEntity(bomitemPojo5.getGoodsid(), tenantid);
                                            Double avaiqtr5 = bomitemPojo5.getIvquantity() + bomitemPojo5.getBuyremqty() + bomitemPojo5.getWkwsremqty() + bomitemPojo5.getWkscremqty()
                                                    - bomitemPojo5.getBusremqty() - bomitemPojo5.getMrpremqty() - bomitemPojo5.getRequremqty();
                                            newlayitem5.setAvaiqty(avaiqtr5);
                                            //设置Mrp应需数量------
                                            Double needqty5 = bomqty5.doubleValue();
                                            if (need == 1) {
                                                needqty5 = avaiqtr5 > bomqty5.doubleValue() ? bomqty5.doubleValue() : bomqty5.subtract(BigDecimal.valueOf(avaiqtr5)).doubleValue();
                                            }
                                            newlayitem5.setNeedqty(needqty5);
                                            //设置计划领用数量------
                                            newlayitem5.setStoplanqty(avaiqtr5 > bomqty5.doubleValue() ? bomqty5.doubleValue() : avaiqtr5);
                                            //设置实际需求数量------
                                            newlayitem5.setRealqty(newlayitem5.getBomqty() - newlayitem5.getStoplanqty());
                                            newlayitem5.setGoodsstate(bomitemPojo5.getGoodsstate());

                                            layeritems.add(newlayitem5);
                                            successCount++;
                                            saveCountInRedis(hKey, missionMsg, totalCount, successCount);
                                            String Bomid5 = matBomMapper.getBomidByGoodsid(bomitemPojo5.getGoodsid(), tenantid);
                                            if (isNotBlank(newlayitem5.getBomid())) {
                                                BigDecimal subBomqty6 = null;
                                                if (need == 1) {
                                                    subBomqty6 = BigDecimal.valueOf(newlayitem5.getRealqty());
                                                }
//                                                String Bomid5 = entityByGoodsid5.getId();
                                                List<MatBomitemPojo> list5 = matBomitemMapper.getList(newlayitem5.getBomid(), tenantid);
                                                //查询第6层
                                                for (MatBomitemPojo bomitemPojo6 : list5) {
                                                    MatBomlayeritemPojo newlayitem6 = new MatBomlayeritemPojo();
                                                    BeanUtils.copyProperties(bomitemPojo6, newlayitem6);
                                                    //设置层级：
                                                    newlayitem6.setLayernum(6);
                                                    newlayitem6.setLayer6("6");
                                                    // 物料数量
                                                    BigDecimal bomqty6 = BigDecimal.valueOf(newlayitem6.getSubqty()).divide(BigDecimal.valueOf(newlayitem6.getMainqty())).multiply(subBomqty6);
                                                    if (newlayitem6.getLossrate() > 0)
                                                        bomqty6 = bomqty6.multiply(BigDecimal.valueOf((1 + newlayitem6.getLossrate() / 100)));
                                                    newlayitem6.setBomqty(bomqty6.doubleValue());

                                                    //设置可用数量------
//                                                    MatGoodsPojo goodsPojo6 = matGoodsService.getEntity(bomitemPojo6.getGoodsid(), tenantid);
                                                    Double avaiqtr6 = bomitemPojo6.getIvquantity() + bomitemPojo6.getBuyremqty() + bomitemPojo6.getWkwsremqty() + bomitemPojo6.getWkscremqty()
                                                            - bomitemPojo6.getBusremqty() - bomitemPojo6.getMrpremqty() - bomitemPojo6.getRequremqty();
                                                    newlayitem6.setAvaiqty(avaiqtr6);
                                                    //设置Mrp应需数量------
                                                    Double needqty6 = bomqty6.doubleValue();
                                                    if (need == 1) {
                                                        needqty6 = avaiqtr6 > bomqty6.doubleValue() ? bomqty6.doubleValue() : bomqty6.subtract(BigDecimal.valueOf(avaiqtr6)).doubleValue();
                                                    }
                                                    newlayitem6.setNeedqty(needqty6);
                                                    //设置计划领用数量------
                                                    newlayitem6.setStoplanqty(avaiqtr6 > bomqty6.doubleValue() ? bomqty6.doubleValue() : avaiqtr6);
                                                    //设置实际需求数量------
                                                    newlayitem6.setRealqty(newlayitem6.getBomqty() - newlayitem6.getStoplanqty());
                                                    newlayitem6.setGoodsstate(bomitemPojo6.getGoodsstate());

                                                    layeritems.add(newlayitem6);
                                                    successCount++;
                                                    saveCountInRedis(hKey, missionMsg, totalCount, successCount);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        PrintColor.red("=============总数成功successCount：" + successCount);
        //设置物料setMatmark
        layeritems.forEach(a -> {
            if ("".equals(a.getBomid())) {
                a.setMatmark(1);
            } else {
                a.setMatmark(0);
            }
        });
        matBomlayerPojo.setItem(layeritems);

        // 计算完成 结果保存到redis
        String resultKey = MyConstant.BOM_CALCULATION_RESULT + hKey;
        this.redisService.setCacheObject(resultKey, JSONObject.toJSONString(matBomlayerPojo), 10L, TimeUnit.MINUTES);

        missionMsg.put("code", "200");//任务完成代码
        missionMsg.put("msg", "任务处理完成");
        missionMsg.put("successCount", successCount);
        missionMsg.put("failCount", failCount);
        missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
        this.redisService.setCacheMapValue(MyConstant.BOM_CALCULATION, hKey, missionMsg);
    }

    //  保存任务进度到redis
    private void saveCountInRedis(String hKey, Map<String, Object> missionMsg, int totalCount, int successCount) {
        missionMsg.put("code", "150"); //任务处理中代码
        missionMsg.put("msg", "任务处理中");
        missionMsg.put("totalCount", totalCount);
        missionMsg.put("successCount", successCount);
        this.redisService.setCacheMapValue(MyConstant.BOM_CALCULATION, hKey, missionMsg);
    }


    @Override
    public String findBomidByGoodsid(String key, String tid) {
        return matBomMapper.findBomidByGoodsid(key, tid);
    }


    /**
     * 递归获取bom.id下所有未审核的Bom列表
     * count 是一个形参，每次递归调用时都会增加。但是，根据递归的性质，每一次递归调用都会有自己独立的 count，而不会反映整体的子表数量。
     * 如果你想要在整个递归过程中统计总共有多少个子表，你可以将 count 设计为一个对象，使其在递归的过程中保持引用。这样，递归调用会共享同一个 count 对象。
     * 当然，还有其他一些方法可以实现相同的效果。另一种常见的方法是使用数组int[] count，因为数组是对象，可以在递归中共享状态。
     * 此外你可以使用 AtomicInteger count类型的对象，它是线程安全的，适合在多线程环境下共享计数。
     *
     * @time 2023/12/13 10:00
     */
    @Override
    public void getUnAssessBomListRecursive(String matBomId, List<MatBomPojo> unAssessBomList, AtomicInteger count, String tid) {
        // 关联查询了BomItem.Goodsid的货品信息(Bomid,库存数量+采购在订+生产在制+加工在制-销售待出-运算占用-领料待出)
        MatBomPojo matBomPojo = getBillEntity(matBomId, tid);
        if (isBlank(matBomPojo.getAssessor())) {
            unAssessBomList.add(matBomPojo);
        }
        for (MatBomitemPojo bomItem : matBomPojo.getItem()) {
            count.incrementAndGet(); // 使用AtomicInteger提供的原子性的增加方法// 计算matBomId下6层总共有多少个子表
            // 如果子表的Bomid不为空且未审核，添加到结果列表
            if (isNotBlank(bomItem.getBomid())) {
                // 递归查询子表的子表
                getUnAssessBomListRecursive(bomItem.getBomid(), unAssessBomList, count, tid);
            }
        }
    }


    // 递归获取bom.id下所有的Bom列表
    public void getAllBomListRecursive(String matBomId, List<MatBomPojo> allBomList, AtomicInteger count, String tid) {
        // 关联查询了BomItem.Goodsid的货品信息(Bomid,库存数量+采购在订+生产在制+加工在制-销售待出-运算占用-领料待出)
        MatBomPojo matBomPojo = getBillEntity(matBomId, tid);
        allBomList.add(matBomPojo);
        for (MatBomitemPojo bomItem : matBomPojo.getItem()) {
            count.incrementAndGet(); // 使用AtomicInteger提供的原子性的增加方法// 计算matBomId下6层总共有多少个子表
            // 如果子表的Bomid不为空且未审核，添加到结果列表
            if (isNotBlank(bomItem.getBomid())) {
                // 递归查询子表的子表
                getUnAssessBomListRecursive(bomItem.getBomid(), allBomList, count, tid);
            }
        }
    }

    @Override
    public int approvalBatch(List<String> keys, LoginUser loginUser, Date date) {
        return matBomMapper.approvalBatch(keys, loginUser, date);
    }

    private List<MatBomdetailPojo> getDetailitem2(List<MatBomdetailPojo> lst, String key, String Treeid, Double qty, Integer level, String tid) {
        //读取bom子表
        List<MatBomdetailPojo> lstdetail = this.matBomitemMapper.getDetailList(key, Treeid, tid);
        if (lstdetail.size() > 0) {
            for (Integer i = 0; i < lstdetail.size(); i++) {
                // 临时树id
                lstdetail.get(i).setTreeid(inksSnowflake.getSnowflake().nextIdStr());
                // 物料数量
                Double bomqty = lstdetail.get(i).getSubqty() / lstdetail.get(i).getMainqty() * qty;
                if (lstdetail.get(i).getLossrate() > 0) bomqty = bomqty * (1 + lstdetail.get(i).getLossrate() / 100);

                lstdetail.get(i).setBomqty(bomqty);
                lstdetail.get(i).setLevelnum(level);
                if (lstdetail.get(i).getGoodsbomid() == null) lstdetail.get(i).setGoodsbomid("");
                lst.add(lstdetail.get(i));

                // 货品有Bomid，并在10层内
                if (lstdetail.get(i).getGoodsbomid() != null && lstdetail.get(i).getGoodsbomid().length() > 0 && level < 10) {
                    logger.info("Bom明细子集 level:" + level);
                    getDetailitem2(lst, lstdetail.get(i).getGoodsbomid(), lstdetail.get(i).getTreeid(), bomqty, level + 1, tid);
                }
            }
        }
        return lstdetail;
    }


    private void findBomL2(List<MatBomlayeritemPojo> item) {
        List<MatBomlayeritemPojo> list1 = item.stream().filter(a -> "1".equals(a.getLayer1()))
                .filter(a -> a.getMatmark() == 0)
                .filter(a -> "".equals(a.getBomid()) || null == a.getBomid())
                .collect(Collectors.toList());

        for (int i = 0; i < list1.size(); i++) {
            //当前L1的起始行号
            Integer rownumStart1 = list1.get(i).getRownum();
            //当前L1的下一个L1终止行号
            Integer rownumNext1 = 10000;
            if (i + 1 < list1.size()) rownumNext1 = list1.get(i + 1).getRownum();
            Integer finalRownumNext = rownumNext1;
            //当前L1作为BOM主表
            MatBomPojo matBomPojo2 = new MatBomPojo();
            BeanUtils.copyProperties(list1.get(i), matBomPojo2);
            //获取当前L1的L2作为BOMitem
            List<MatBomitemPojo> bomitem2 = new ArrayList<>();
            List<MatBomlayeritemPojo> list2 = item.stream().filter(a -> a.getRownum() > rownumStart1)
                    .filter(a -> a.getRownum() < finalRownumNext)
                    .filter(a -> "2".equals(a.getLayer2()))
                    .collect(Collectors.toList());
            for (int i1 = 0; i1 < list2.size(); i1++) {
                MatBomitemPojo matBomitemPojo = new MatBomitemPojo();
                BeanUtils.copyProperties(list2.get(i1), matBomitemPojo);
                matBomitemPojo.setRownum(i1);
                bomitem2.add(matBomitemPojo);
            }
            matBomPojo2.setItem(bomitem2);
            if (bomitem2.size() > 0) {
                addTid(matBomPojo2);
                matBomPojo2 = insert(matBomPojo2);
            }

        }
    }

    //"".equals(a.getBomid())   && StringUtils.isNotBlank(a.getBomid())
    private void findBomL3(List<MatBomlayeritemPojo> item) {
        List<MatBomlayeritemPojo> list1 = item.stream().filter(a -> "2".equals(a.getLayer2()))
                .filter(a -> a.getMatmark() == 0)
                .filter(a -> "".equals(a.getBomid()) || null == a.getBomid()).collect(Collectors.toList());
        for (int i = 0; i < list1.size(); i++) {
            Integer rownumStart1 = list1.get(i).getRownum();
            Integer rownumNext1 = 10000;
            if (i + 1 < list1.size()) rownumNext1 = list1.get(i + 1).getRownum();
            Integer finalRownumNext = rownumNext1;
            MatBomPojo matBomPojo2 = new MatBomPojo();
            BeanUtils.copyProperties(list1.get(i), matBomPojo2);
            List<MatBomitemPojo> bomitem2 = new ArrayList<>();
            List<MatBomlayeritemPojo> list2 = item.stream().filter(a -> a.getRownum() > rownumStart1)
                    .filter(a -> a.getRownum() < finalRownumNext)
                    .filter(a -> "3".equals(a.getLayer3()))
                    .collect(Collectors.toList());
            for (int i1 = 0; i1 < list2.size(); i1++) {
                MatBomitemPojo matBomitemPojo = new MatBomitemPojo();
                BeanUtils.copyProperties(list2.get(i1), matBomitemPojo);
                matBomitemPojo.setRownum(i1);
                bomitem2.add(matBomitemPojo);
            }
            matBomPojo2.setItem(bomitem2);
            if (bomitem2.size() > 0) {
                addTid(matBomPojo2);
                matBomPojo2 = insert(matBomPojo2);
            }
        }
    }

    private void findBomL4(List<MatBomlayeritemPojo> item) {
        List<MatBomlayeritemPojo> list1 = item.stream().filter(a -> "3".equals(a.getLayer3()))
                .filter(a -> a.getMatmark() == 0)
                .filter(a -> "".equals(a.getBomid()) || null == a.getBomid()).collect(Collectors.toList());
        for (int i = 0; i < list1.size(); i++) {
            Integer rownumStart1 = list1.get(i).getRownum();
            Integer rownumNext1 = 10000;
            if (i + 1 < list1.size()) rownumNext1 = list1.get(i + 1).getRownum();
            Integer finalRownumNext = rownumNext1;
            MatBomPojo matBomPojo2 = new MatBomPojo();
            BeanUtils.copyProperties(list1.get(i), matBomPojo2);
            List<MatBomitemPojo> bomitem2 = new ArrayList<>();
            List<MatBomlayeritemPojo> list2 = item.stream().filter(a -> a.getRownum() > rownumStart1)
                    .filter(a -> a.getRownum() < finalRownumNext)
                    .filter(a -> "4".equals(a.getLayer4()))
                    .collect(Collectors.toList());
            for (int i1 = 0; i1 < list2.size(); i1++) {
                MatBomitemPojo matBomitemPojo = new MatBomitemPojo();
                BeanUtils.copyProperties(list2.get(i1), matBomitemPojo);
                matBomitemPojo.setRownum(i1);
                bomitem2.add(matBomitemPojo);
            }
            matBomPojo2.setItem(bomitem2);
            if (bomitem2.size() > 0) {
                addTid(matBomPojo2);
                matBomPojo2 = insert(matBomPojo2);
            }
        }
    }

    private void findBomL5(List<MatBomlayeritemPojo> item) {
        List<MatBomlayeritemPojo> list1 = item.stream().filter(a -> "4".equals(a.getLayer4()))
                .filter(a -> a.getMatmark() == 0)
                .filter(a -> "".equals(a.getBomid()) || null == a.getBomid()).collect(Collectors.toList());
        for (int i = 0; i < list1.size(); i++) {
            Integer rownumStart1 = list1.get(i).getRownum();
            Integer rownumNext1 = 10000;
            if (i + 1 < list1.size()) rownumNext1 = list1.get(i + 1).getRownum();
            Integer finalRownumNext = rownumNext1;
            MatBomPojo matBomPojo2 = new MatBomPojo();
            BeanUtils.copyProperties(list1.get(i), matBomPojo2);
            List<MatBomitemPojo> bomitem2 = new ArrayList<>();
            List<MatBomlayeritemPojo> list2 = item.stream().filter(a -> a.getRownum() > rownumStart1)
                    .filter(a -> a.getRownum() < finalRownumNext)
                    .filter(a -> "5".equals(a.getLayer5()))
                    .collect(Collectors.toList());
            for (int i1 = 0; i1 < list2.size(); i1++) {
                MatBomitemPojo matBomitemPojo = new MatBomitemPojo();
                BeanUtils.copyProperties(list2.get(i1), matBomitemPojo);
                matBomitemPojo.setRownum(i1);
                bomitem2.add(matBomitemPojo);
            }
            matBomPojo2.setItem(bomitem2);
            if (bomitem2.size() > 0) {
                addTid(matBomPojo2);
                matBomPojo2 = insert(matBomPojo2);
            }
        }
    }

    private void findBomL6(List<MatBomlayeritemPojo> item) {
        List<MatBomlayeritemPojo> list1 = item.stream().filter(a -> "5".equals(a.getLayer5()))
                .filter(a -> a.getMatmark() == 0)
                .filter(a -> "".equals(a.getBomid()) || null == a.getBomid()).collect(Collectors.toList());
        for (int i = 0; i < list1.size(); i++) {
            Integer rownumStart1 = list1.get(i).getRownum();
            Integer rownumNext1 = 10000;
            if (i + 1 < list1.size()) rownumNext1 = list1.get(i + 1).getRownum();
            Integer finalRownumNext = rownumNext1;
            MatBomPojo matBomPojo2 = new MatBomPojo();
            BeanUtils.copyProperties(list1.get(i), matBomPojo2);
            List<MatBomitemPojo> bomitem2 = new ArrayList<>();
            List<MatBomlayeritemPojo> list2 = item.stream().filter(a -> a.getRownum() > rownumStart1)
                    .filter(a -> a.getRownum() < finalRownumNext)
                    .filter(a -> "6".equals(a.getLayer6()))
                    .collect(Collectors.toList());
            for (int i1 = 0; i1 < list2.size(); i1++) {
                MatBomitemPojo matBomitemPojo = new MatBomitemPojo();
                BeanUtils.copyProperties(list2.get(i1), matBomitemPojo);
                matBomitemPojo.setRownum(i1);
                bomitem2.add(matBomitemPojo);
            }
            matBomPojo2.setItem(bomitem2);
            if (bomitem2.size() > 0) {
                addTid(matBomPojo2);
                matBomPojo2 = insert(matBomPojo2);
            }
        }
    }


    public void addTid(MatBomPojo matBomPojo) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        matBomPojo.setCreateby(loginUser.getRealname());   // 创建者
        matBomPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        matBomPojo.setCreatedate(new Date());   // 创建时间
        matBomPojo.setLister(loginUser.getRealname());   // 制表
        matBomPojo.setListerid(loginUser.getUserid());    // 制表id
        matBomPojo.setModifydate(new Date());   //修改时间
        matBomPojo.setTenantid(loginUser.getTenantid());   //租户id
        matBomPojo.setTenantname(loginUser.getTenantinfo().getTenantname());

    }

    //    @Override   //key:bom主表的id
//    public MatBomlayerPojo getItemAllByLayer(String key, String tenantid, Double qty, Integer need) {
//        MatBomPojo matBomPojo = getBillEntity(key, tenantid);
//        //BOM主表转为BOMlayer主表
//        MatBomlayerPojo matBomlayerPojo = new MatBomlayerPojo();
//        BeanUtils.copyProperties(matBomPojo, matBomlayerPojo);
//        //layeritems用来存储所有BOMlayer子表
//        List<MatBomlayeritemPojo> layeritems = new ArrayList<>();
//        List<MatBomitemPojo> item = matBomPojo.getItem();
//        //查询第一层
//        for (MatBomitemPojo matBomitemPojo : item) {
//            MatBomlayeritemPojo newlayitem = new MatBomlayeritemPojo();
//            BeanUtils.copyProperties(matBomitemPojo, newlayitem);
//            //设置层级：
//            newlayitem.setLayernum(1);
//            newlayitem.setLayer1("1");
//            // 物料数量 .divide()除以 .multiply()乘
//            BigDecimal bomqty = BigDecimal.valueOf(newlayitem.getSubqty()).divide(BigDecimal.valueOf(newlayitem.getMainqty())).multiply(BigDecimal.valueOf(qty));
//            if (newlayitem.getLossrate() > 0)
//                bomqty = bomqty.multiply(BigDecimal.valueOf((1 + newlayitem.getLossrate() / 100)));
//            newlayitem.setBomqty(bomqty.doubleValue());
//            //设置可用数量---仓库+在途-待出
////            MatGoodsPojo goodsPojo = matGoodsService.getEntity(matBomitemPojo.getGoodsid(), tenantid);
//            Double avaiqtr = matBomitemPojo.getIvquantity() + matBomitemPojo.getBuyremqty() + matBomitemPojo.getWkwsremqty() + matBomitemPojo.getWkscremqty()
//                    - matBomitemPojo.getBusremqty() - matBomitemPojo.getMrpremqty() - matBomitemPojo.getRequremqty();
//            newlayitem.setAvaiqty(avaiqtr);
//            //设置Mrp建议应需数量---if 可用量大于Bom量，等BOM量，否， BOM量-可用量；
//            Double needqty = bomqty.doubleValue();
//            if (need == 1) {
//                needqty = avaiqtr > bomqty.doubleValue() ? bomqty.doubleValue() : bomqty.subtract(BigDecimal.valueOf(avaiqtr)).doubleValue();
//            }
//            newlayitem.setNeedqty(needqty);
//            //设置计划领用数量---if 可用量大于Bom量，等BOM量，否， 可用量；
//            newlayitem.setStoplanqty(avaiqtr > bomqty.doubleValue() ? bomqty.doubleValue() : avaiqtr);
//            //设置实际需求数量---BOM量-计划领用
//            newlayitem.setRealqty(newlayitem.getBomqty() - newlayitem.getStoplanqty());
//            layeritems.add(newlayitem);
//            //获取当前L1作为主表时的id，作为Pid传入getList
//            MatBomPojo entityByGoodsid = matBomMapper.getEntityByGoodsid(matBomitemPojo.getGoodsid(), tenantid);
//            if (entityByGoodsid != null) {
//                BigDecimal subBomqty2 = null;
//                if (need == 1) {
//                    subBomqty2 = BigDecimal.valueOf(newlayitem.getRealqty());
//                }
//                String Bomid = entityByGoodsid.getId();
//                List<MatBomitemPojo> list = matBomitemMapper.getList(Bomid, tenantid);
//                //查询第二层
//                for (MatBomitemPojo bomitemPojo2 : list) {
//                    MatBomlayeritemPojo newlayitem2 = new MatBomlayeritemPojo();
//                    BeanUtils.copyProperties(bomitemPojo2, newlayitem2);
//                    //设置层级：
//                    newlayitem2.setLayernum(2);
//                    newlayitem2.setLayer2("2");
//                    // 物料数量
//                    BigDecimal bomqty2 = BigDecimal.valueOf(newlayitem2.getSubqty()).divide(BigDecimal.valueOf(newlayitem2.getMainqty())).multiply(subBomqty2);
//                    if (newlayitem2.getLossrate() > 0)
//                        bomqty2 = bomqty2.multiply(BigDecimal.valueOf((1 + newlayitem2.getLossrate() / 100)));
//                    newlayitem2.setBomqty(bomqty2.doubleValue());
//                    //设置可用数量------
////                    MatGoodsPojo goodsPojo2 = matGoodsService.getEntity(bomitemPojo2.getGoodsid(), tenantid);
//                    Double avaiqtr2 = bomitemPojo2.getIvquantity() + bomitemPojo2.getBuyremqty() + bomitemPojo2.getWkwsremqty() + bomitemPojo2.getWkscremqty()
//                            - bomitemPojo2.getBusremqty() - bomitemPojo2.getMrpremqty() - bomitemPojo2.getRequremqty();
//                    //设置Mrp应需数量------
//                    Double needqty2 = bomqty2.doubleValue();
//                    if (need == 1) {
//                        needqty2 = avaiqtr2 > bomqty2.doubleValue() ? bomqty2.doubleValue() : bomqty2.subtract(BigDecimal.valueOf(avaiqtr2)).doubleValue();
//                    }
//                    newlayitem2.setNeedqty(needqty2);
//                    //设置计划领用数量------
//                    newlayitem2.setStoplanqty(avaiqtr2 > bomqty2.doubleValue() ? bomqty2.doubleValue() : avaiqtr2);
//                    //设置实际需求数量------
//                    newlayitem2.setRealqty(newlayitem2.getBomqty() - newlayitem2.getStoplanqty());
//                    layeritems.add(newlayitem2);
//                    MatBomPojo entityByGoodsid2 = matBomMapper.getEntityByGoodsid(bomitemPojo2.getGoodsid(), tenantid);
//                    if (entityByGoodsid2 != null) {
//                        BigDecimal subBomqty3 = null;
//                        if (need == 1) {
//                            subBomqty3 = BigDecimal.valueOf(newlayitem2.getRealqty());
//                        }
//                        List<MatBomitemPojo> list2 = matBomitemMapper.getList(entityByGoodsid2.getId(), tenantid);
//                        //查询第三层
//                        for (MatBomitemPojo bomitemPojo3 : list2) {
//                            MatBomlayeritemPojo newlayitem3 = new MatBomlayeritemPojo();
//                            BeanUtils.copyProperties(bomitemPojo3, newlayitem3);
//                            //设置层级：
//                            newlayitem3.setLayernum(3);
//                            newlayitem3.setLayer3("3");
//                            // 物料数量
//                            BigDecimal bomqty3 = BigDecimal.valueOf(newlayitem3.getSubqty()).divide(BigDecimal.valueOf(newlayitem3.getMainqty())).multiply(subBomqty3);
//                            if (newlayitem3.getLossrate() > 0)
//                                bomqty3 = bomqty3.multiply(BigDecimal.valueOf((1 + newlayitem3.getLossrate() / 100)));
//                            newlayitem3.setBomqty(bomqty3.doubleValue());
//
//                            //设置可用数量------
////                            MatGoodsPojo goodsPojo3 = matGoodsService.getEntity(bomitemPojo3.getGoodsid(), tenantid);
//                            Double avaiqtr3 = bomitemPojo3.getIvquantity() + bomitemPojo3.getBuyremqty() + bomitemPojo3.getWkwsremqty() + bomitemPojo3.getWkscremqty()
//                                    - bomitemPojo3.getBusremqty() - bomitemPojo3.getMrpremqty() - bomitemPojo3.getRequremqty();
//                            //设置Mrp应需数量------
//                            Double needqty3 = bomqty3.doubleValue();
//                            if (need == 1) {
//                                needqty3 = avaiqtr3 > bomqty3.doubleValue() ? bomqty3.doubleValue() : bomqty3.subtract(BigDecimal.valueOf(avaiqtr3)).doubleValue();
//                            }
//                            newlayitem3.setNeedqty(needqty3);
//                            //设置计划领用数量------
//                            newlayitem3.setStoplanqty(avaiqtr3 > bomqty3.doubleValue() ? bomqty3.doubleValue() : avaiqtr3);
//                            //设置实际需求数量------
//                            newlayitem3.setRealqty(newlayitem3.getBomqty() - newlayitem3.getStoplanqty());
//                            layeritems.add(newlayitem3);
//                            MatBomPojo entityByGoodsid3 = matBomMapper.getEntityByGoodsid(bomitemPojo3.getGoodsid(), tenantid);
//                            if (entityByGoodsid3 != null) {
//                                BigDecimal subBomqty4 = null;
//                                if (need == 1) {
//                                    subBomqty4 = BigDecimal.valueOf(newlayitem3.getRealqty());
//                                }
//                                String Bomid3 = entityByGoodsid3.getId();
//                                List<MatBomitemPojo> list3 = matBomitemMapper.getList(Bomid3, tenantid);
//                                //查询第四层
//                                for (MatBomitemPojo bomitemPojo4 : list3) {
//                                    MatBomlayeritemPojo newlayitem4 = new MatBomlayeritemPojo();
//                                    BeanUtils.copyProperties(bomitemPojo4, newlayitem4);
//                                    //设置层级：
//                                    newlayitem4.setLayernum(4);
//                                    newlayitem4.setLayer4("4");
//                                    // 物料数量
//                                    BigDecimal bomqty4 = BigDecimal.valueOf(newlayitem4.getSubqty()).divide(BigDecimal.valueOf(newlayitem4.getMainqty())).multiply(subBomqty4);
//                                    if (newlayitem4.getLossrate() > 0)
//                                        bomqty4 = bomqty4.multiply(BigDecimal.valueOf((1 + newlayitem4.getLossrate() / 100)));
//                                    newlayitem4.setBomqty(bomqty4.doubleValue());
//
//                                    //设置可用数量------
////                                    MatGoodsPojo goodsPojo4 = matGoodsService.getEntity(bomitemPojo4.getGoodsid(), tenantid);
//                                    Double avaiqtr4 = bomitemPojo4.getIvquantity() + bomitemPojo4.getBuyremqty() + bomitemPojo4.getWkwsremqty() + bomitemPojo4.getWkscremqty()
//                                            - bomitemPojo4.getBusremqty() - bomitemPojo4.getMrpremqty() - bomitemPojo4.getRequremqty();
//                                    newlayitem4.setAvaiqty(avaiqtr4);
//                                    //设置Mrp应需数量------
//                                    Double needqty4 = bomqty4.doubleValue();
//                                    if (need == 1) {
//                                        needqty4 = avaiqtr4 > bomqty4.doubleValue() ? bomqty4.doubleValue() : bomqty4.subtract(BigDecimal.valueOf(avaiqtr4)).doubleValue();
//                                    }
//                                    newlayitem4.setNeedqty(needqty4);
//                                    //设置计划领用数量------
//                                    newlayitem4.setStoplanqty(avaiqtr4 > bomqty4.doubleValue() ? bomqty4.doubleValue() : avaiqtr4);
//                                    //设置实际需求数量------
//                                    newlayitem4.setRealqty(newlayitem4.getBomqty() - newlayitem4.getStoplanqty());
//
//                                    layeritems.add(newlayitem4);
//                                    MatBomPojo entityByGoodsid4 = matBomMapper.getEntityByGoodsid(bomitemPojo4.getGoodsid(), tenantid);
//                                    if (entityByGoodsid4 != null) {
//                                        BigDecimal subBomqty5 = null;
//                                        if (need == 1) {
//                                            subBomqty5 = BigDecimal.valueOf(newlayitem4.getRealqty());
//                                        }
//                                        String Bomid4 = entityByGoodsid4.getId();
//                                        List<MatBomitemPojo> list4 = matBomitemMapper.getList(Bomid4, tenantid);
//                                        //查询第5层
//                                        for (MatBomitemPojo bomitemPojo5 : list4) {
//                                            MatBomlayeritemPojo newlayitem5 = new MatBomlayeritemPojo();
//                                            BeanUtils.copyProperties(bomitemPojo5, newlayitem5);
//                                            //设置层级：
//                                            newlayitem5.setLayernum(5);
//                                            newlayitem5.setLayer5("5");
//                                            // 物料数量
//                                            BigDecimal bomqty5 = BigDecimal.valueOf(newlayitem5.getSubqty()).divide(BigDecimal.valueOf(newlayitem5.getMainqty())).multiply(subBomqty5);
//                                            if (newlayitem5.getLossrate() > 0)
//                                                bomqty5 = bomqty5.multiply(BigDecimal.valueOf((1 + newlayitem5.getLossrate() / 100)));
//                                            newlayitem5.setBomqty(bomqty5.doubleValue());
//
//                                            //设置可用数量------
////                                            MatGoodsPojo goodsPojo5 = matGoodsService.getEntity(bomitemPojo5.getGoodsid(), tenantid);
//                                            Double avaiqtr5 = bomitemPojo5.getIvquantity() + bomitemPojo5.getBuyremqty() + bomitemPojo5.getWkwsremqty() + bomitemPojo5.getWkscremqty()
//                                                    - bomitemPojo5.getBusremqty() - bomitemPojo5.getMrpremqty() - bomitemPojo5.getRequremqty();
//                                            newlayitem5.setAvaiqty(avaiqtr5);
//                                            //设置Mrp应需数量------
//                                            Double needqty5 = bomqty5.doubleValue();
//                                            if (need == 1) {
//                                                needqty5 = avaiqtr5 > bomqty5.doubleValue() ? bomqty5.doubleValue() : bomqty5.subtract(BigDecimal.valueOf(avaiqtr5)).doubleValue();
//                                            }
//                                            newlayitem5.setNeedqty(needqty5);
//                                            //设置计划领用数量------
//                                            newlayitem5.setStoplanqty(avaiqtr5 > bomqty5.doubleValue() ? bomqty5.doubleValue() : avaiqtr5);
//                                            //设置实际需求数量------
//                                            newlayitem5.setRealqty(newlayitem5.getBomqty() - newlayitem5.getStoplanqty());
//
//                                            layeritems.add(newlayitem5);
//                                            MatBomPojo entityByGoodsid5 = matBomMapper.getEntityByGoodsid(bomitemPojo5.getGoodsid(), tenantid);
//                                            if (entityByGoodsid5 != null) {
//                                                BigDecimal subBomqty6 = null;
//                                                if (need == 1) {
//                                                    subBomqty6 = BigDecimal.valueOf(newlayitem5.getRealqty());
//                                                }
//                                                String Bomid5 = entityByGoodsid5.getId();
//                                                List<MatBomitemPojo> list5 = matBomitemMapper.getList(Bomid5, tenantid);
//                                                //查询第6层
//                                                for (MatBomitemPojo bomitemPojo6 : list5) {
//                                                    MatBomlayeritemPojo newlayitem6 = new MatBomlayeritemPojo();
//                                                    BeanUtils.copyProperties(bomitemPojo6, newlayitem6);
//                                                    //设置层级：
//                                                    newlayitem6.setLayernum(6);
//                                                    newlayitem6.setLayer6("6");
//                                                    // 物料数量
//                                                    BigDecimal bomqty6 = BigDecimal.valueOf(newlayitem6.getSubqty()).divide(BigDecimal.valueOf(newlayitem6.getMainqty())).multiply(subBomqty6);
//                                                    if (newlayitem6.getLossrate() > 0)
//                                                        bomqty6 = bomqty6.multiply(BigDecimal.valueOf((1 + newlayitem6.getLossrate() / 100)));
//                                                    newlayitem6.setBomqty(bomqty6.doubleValue());
//
//                                                    //设置可用数量------
////                                                    MatGoodsPojo goodsPojo6 = matGoodsService.getEntity(bomitemPojo6.getGoodsid(), tenantid);
//                                                    Double avaiqtr6 = bomitemPojo6.getIvquantity() + bomitemPojo6.getBuyremqty() + bomitemPojo6.getWkwsremqty() + bomitemPojo6.getWkscremqty()
//                                                            - bomitemPojo6.getBusremqty() - bomitemPojo6.getMrpremqty() - bomitemPojo6.getRequremqty();
//                                                    newlayitem6.setAvaiqty(avaiqtr6);
//                                                    //设置Mrp应需数量------
//                                                    Double needqty6 = bomqty6.doubleValue();
//                                                    if (need == 1) {
//                                                        needqty6 = avaiqtr6 > bomqty6.doubleValue() ? bomqty6.doubleValue() : bomqty6.subtract(BigDecimal.valueOf(avaiqtr6)).doubleValue();
//                                                    }
//                                                    newlayitem6.setNeedqty(needqty6);
//                                                    //设置计划领用数量------
//                                                    newlayitem6.setStoplanqty(avaiqtr6 > bomqty6.doubleValue() ? bomqty6.doubleValue() : avaiqtr6);
//                                                    //设置实际需求数量------
//                                                    newlayitem6.setRealqty(newlayitem6.getBomqty() - newlayitem6.getStoplanqty());
//
//                                                    layeritems.add(newlayitem6);
//                                                }
//                                            }
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        //设置物料setMatmark
//        layeritems.stream().forEach(a -> {
//            if ("".equals(a.getBomid())) {
//                a.setMatmark(1);
//            } else {
//                a.setMatmark(0);
//            }
//            ;
//            String goodsStates = matGoodsService.getGoodsstate(a.getGoodsid(), tenantid);
//            a.setGoodsstate(goodsStates);
//        });
//        matBomlayerPojo.setItem(layeritems);
//        return matBomlayerPojo;
//    }
}
