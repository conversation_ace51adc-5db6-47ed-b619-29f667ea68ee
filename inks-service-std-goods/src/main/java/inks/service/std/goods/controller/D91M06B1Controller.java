package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatSubstitutePojo;
import inks.service.std.goods.service.MatSubstituteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 替代物料(Mat_Substitute)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-06 11:07:59
 */
@RestController
@RequestMapping("D91M06B1")
@Api(tags = "D91M06B1:替代品管理")
public class D91M06B1Controller extends MatSubstituteController {
    private final String moduleCode = "D91M06B1";
    /**
     * 服务对象
     */
    @Resource
    private MatSubstituteService matSubstituteService;
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取替代品管理详细信息", notes = "获取替代品管理详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByGoodsid", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Mat_Substitute.List")
    public R<MatSubstitutePojo> getBillEntityByGoodsid(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matSubstituteService.getBillEntityByGoodsid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增替代品管理", notes = "新增替代品管理", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Substitute.Add")
    public R<MatSubstitutePojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatSubstitutePojo matSubstitutePojo = JSONArray.parseObject(json, MatSubstitutePojo.class);

            //重名检查
            MatSubstitutePojo matSubstitutePojo1 = this.matSubstituteService.getEntityByGoodsid(matSubstitutePojo.getGoodsid(), loginUser.getTenantid());
            if (matSubstitutePojo1 != null) {
                return R.fail(403, "主料货品重复");
            }

            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Mat_Substitute", null, loginUser.getTenantid());
            matSubstitutePojo.setRefno(refno);
            matSubstitutePojo.setCreateby(loginUser.getRealname());   // 创建者
            matSubstitutePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matSubstitutePojo.setCreatedate(new Date());   // 创建时间
            matSubstitutePojo.setLister(loginUser.getRealname());   // 制表
            matSubstitutePojo.setListerid(loginUser.getUserid());    // 制表id
            matSubstitutePojo.setModifydate(new Date());   //修改时间
            matSubstitutePojo.setTenantid(loginUser.getTenantid());   //租户id
            MatSubstitutePojo insert = this.matSubstituteService.insert(matSubstitutePojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param mkey 主键
     * @return 单条数据
     */
    @ApiOperation(value = "检查是否有替代关系", notes = "检查是否有替代关系", produces = "application/json")
    @RequestMapping(value = "/chkSubstitute", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Substitute.List")
    public R<MatSubstitutePojo> chkSubstitute(String mkey, String skey, String bomid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matSubstituteService.chkSubstitute(mkey, skey, bomid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
