package inks.service.std.goods.constant;


// 存放全局常量
public class MyConstant {

    public static final String BOM_CALCULATION = "bom_calculation_state:";//bom计算 用于/getItemAllByLayerStart
    public static final String BOM_CALCULATION_RESULT = "bom_calculation_result:";//bom计算的结果 用于/getItemAllByLayerResult
    //客户应收款报表
    public static final String RECEIPT_PAGES = "receipt_pages:";
    //"refno_lock:" 生成(释放)编码的Redis锁   String redisLock_Key = MyConstant.REFNO_LOCK + moduleCode + tid;
    public static final String REFNO_LOCK = "refno_lock:";

}
