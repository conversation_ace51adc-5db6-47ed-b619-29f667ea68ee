package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSubstitutebomEntity;
import inks.service.std.goods.domain.pojo.MatSubstitutebomPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 替代料BOM表(MatSubstitutebom)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-08 10:11:56
 */
 @Mapper
public interface MatSubstitutebomMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstitutebomPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSubstitutebomPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSubstitutebomPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matSubstitutebomEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSubstitutebomEntity matSubstitutebomEntity);

    
    /**
     * 修改数据
     *
     * @param matSubstitutebomEntity 实例对象
     * @return 影响行数
     */
    int update(MatSubstitutebomEntity matSubstitutebomEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

