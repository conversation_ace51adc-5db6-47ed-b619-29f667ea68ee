package inks.service.std.goods.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatBomorderitemPojo;
import inks.service.std.goods.domain.pojo.MatBomordertreePojo;
import inks.service.std.goods.service.MatBomorderService;
import inks.service.std.goods.service.MatBomorderitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单Bom(Mat_BomOrder)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-11 14:40:09
 */
@RestController
@RequestMapping("D91M04B1")
@Api(tags = "D91M04B1:订单BOM")
public class D91M04B1Controller extends MatBomorderController {

    /**
     * 服务对象
     */
    @Resource
    private MatBomorderService matBomorderService;

    /**
     * 服务对象Item
     */
    @Resource
    private MatBomorderitemService matBomorderitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /*
     *
     * esay poi导入导出 时间2021-12-13 song
     *
     *
     *
     * */
    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<MatBomordertreePojo> list = new ArrayList<>();
            //创建表格
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("订单BOM", ""),
                    MatBomorderitemPojo.class, list);
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "订单BOM模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_BomOrder.List")
    public void exportList(@RequestBody String json, HttpServletRequest request, HttpServletResponse response) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "") {
                queryParam.setOrderBy("CreateDate");
            }
            queryParam.setTenantid(loginUser.getTenantid());
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("订单BOM", "订单BOM"),
                    MatBomorderitemPojo.class, this.matBomorderitemService.getPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "订单BOM信息");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<MatBomorderitemPojo>> importExecl(String pid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<MatBomorderitemPojo> list = POIUtil.importExcel(file.getInputStream(), MatBomorderitemPojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
