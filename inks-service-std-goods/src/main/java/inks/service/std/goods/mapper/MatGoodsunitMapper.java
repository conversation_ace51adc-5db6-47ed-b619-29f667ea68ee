package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatGoodsunitPojo;
import inks.service.std.goods.domain.MatGoodsunitEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 货品单位(Mat_GoodsUnit)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-08 11:18:18
 */
@Mapper
public interface MatGoodsunitMapper {

    MatGoodsunitPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<MatGoodsunitPojo> getPageList(QueryParam queryParam);

    int insert(MatGoodsunitEntity matGoodsunitEntity);

    int update(MatGoodsunitEntity matGoodsunitEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<MatGoodsunitPojo> getList(Integer type, String tid);

    int countUnitname(String lowerCaseUnitName, String id, String tid);
}

