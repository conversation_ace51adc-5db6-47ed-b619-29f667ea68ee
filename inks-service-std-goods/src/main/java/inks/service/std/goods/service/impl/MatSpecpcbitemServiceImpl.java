package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSpecpcbitemEntity;
import inks.service.std.goods.domain.pojo.MatSpecpcbitemPojo;
import inks.service.std.goods.mapper.MatSpecpcbitemMapper;
import inks.service.std.goods.service.MatSpecpcbitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * PCB工艺项目(MatSpecpcbitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:58
 */
@Service("matSpecpcbitemService")
public class MatSpecpcbitemServiceImpl implements MatSpecpcbitemService {
    @Resource
    private MatSpecpcbitemMapper matSpecpcbitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSpecpcbitemPojo getEntity(String key, String tid) {
        return this.matSpecpcbitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSpecpcbitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSpecpcbitemPojo> lst = matSpecpcbitemMapper.getPageList(queryParam);
            PageInfo<MatSpecpcbitemPojo> pageInfo = new PageInfo<MatSpecpcbitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatSpecpcbitemPojo> getList(String Pid, String tid) {
        try {
            List<MatSpecpcbitemPojo> lst = matSpecpcbitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matSpecpcbitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbitemPojo insert(MatSpecpcbitemPojo matSpecpcbitemPojo) {
        //初始化item的NULL
        MatSpecpcbitemPojo itempojo = this.clearNull(matSpecpcbitemPojo);
        MatSpecpcbitemEntity matSpecpcbitemEntity = new MatSpecpcbitemEntity();
        BeanUtils.copyProperties(itempojo, matSpecpcbitemEntity);

        matSpecpcbitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSpecpcbitemEntity.setRevision(1);  //乐观锁
        this.matSpecpcbitemMapper.insert(matSpecpcbitemEntity);
        return this.getEntity(matSpecpcbitemEntity.getId(), matSpecpcbitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSpecpcbitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbitemPojo update(MatSpecpcbitemPojo matSpecpcbitemPojo) {
        MatSpecpcbitemEntity matSpecpcbitemEntity = new MatSpecpcbitemEntity();
        BeanUtils.copyProperties(matSpecpcbitemPojo, matSpecpcbitemEntity);
        this.matSpecpcbitemMapper.update(matSpecpcbitemEntity);
        return this.getEntity(matSpecpcbitemEntity.getId(), matSpecpcbitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSpecpcbitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matSpecpcbitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSpecpcbitemPojo clearNull(MatSpecpcbitemPojo matSpecpcbitemPojo) {
        //初始化NULL字段
        if (matSpecpcbitemPojo.getPid() == null) matSpecpcbitemPojo.setPid("");
        if (matSpecpcbitemPojo.getWpid() == null) matSpecpcbitemPojo.setWpid("");
     if(matSpecpcbitemPojo.getWpcode()==null) matSpecpcbitemPojo.setWpcode("");
        if (matSpecpcbitemPojo.getWpname() == null) matSpecpcbitemPojo.setWpname("");
        if (matSpecpcbitemPojo.getDescription() == null) matSpecpcbitemPojo.setDescription("");
        if (matSpecpcbitemPojo.getDetailjson() == null) matSpecpcbitemPojo.setDetailjson("");
        if (matSpecpcbitemPojo.getRownum() == null) matSpecpcbitemPojo.setRownum(0);
        if (matSpecpcbitemPojo.getAreamult() == null) matSpecpcbitemPojo.setAreamult(0);
        if (matSpecpcbitemPojo.getFlowcode() == null) matSpecpcbitemPojo.setFlowcode("");
        if (matSpecpcbitemPojo.getToolscode() == null) matSpecpcbitemPojo.setToolscode("");
        if (matSpecpcbitemPojo.getRemark() == null) matSpecpcbitemPojo.setRemark("");
        if (matSpecpcbitemPojo.getCustom1() == null) matSpecpcbitemPojo.setCustom1("");
        if (matSpecpcbitemPojo.getCustom2() == null) matSpecpcbitemPojo.setCustom2("");
        if (matSpecpcbitemPojo.getCustom3() == null) matSpecpcbitemPojo.setCustom3("");
        if (matSpecpcbitemPojo.getCustom4() == null) matSpecpcbitemPojo.setCustom4("");
        if (matSpecpcbitemPojo.getCustom5() == null) matSpecpcbitemPojo.setCustom5("");
        if (matSpecpcbitemPojo.getCustom6() == null) matSpecpcbitemPojo.setCustom6("");
        if (matSpecpcbitemPojo.getCustom7() == null) matSpecpcbitemPojo.setCustom7("");
        if (matSpecpcbitemPojo.getCustom8() == null) matSpecpcbitemPojo.setCustom8("");
        if (matSpecpcbitemPojo.getCustom9() == null) matSpecpcbitemPojo.setCustom9("");
        if (matSpecpcbitemPojo.getCustom10() == null) matSpecpcbitemPojo.setCustom10("");
        if (matSpecpcbitemPojo.getTenantid() == null) matSpecpcbitemPojo.setTenantid("");
        if (matSpecpcbitemPojo.getTenantname() == null) matSpecpcbitemPojo.setTenantname("");
        if (matSpecpcbitemPojo.getRevision() == null) matSpecpcbitemPojo.setRevision(0);
        return matSpecpcbitemPojo;
    }
}
