package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatAttributePojo;
import inks.service.std.goods.domain.pojo.MatGoodscustPojo;
import inks.service.std.goods.domain.MatGoodscustEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 货品自定义(Mat_GoodsCust)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-15 16:25:25
 */
@Mapper
public interface MatGoodscustMapper {

    MatGoodscustPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<MatGoodscustPojo> getPageList(QueryParam queryParam);

    int insert(MatGoodscustEntity matGoodscustEntity);

    int update(MatGoodscustEntity matGoodscustEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<MatGoodscustPojo> getListByShow(String tid);
    List<MatGoodscustPojo> getList(String tid);
}

