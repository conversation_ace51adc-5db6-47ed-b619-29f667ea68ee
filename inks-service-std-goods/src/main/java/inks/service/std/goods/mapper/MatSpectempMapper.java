package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSpectempEntity;
import inks.service.std.goods.domain.pojo.MatSpectempPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MI模板(MatSpectemp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-10 16:32:06
 */
@Mapper
public interface MatSpectempMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpectempPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpectempPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param matSpectempEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpectempEntity matSpectempEntity);

    
    /**
     * 修改数据
     *
     * @param matSpectempEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpectempEntity matSpectempEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
    /**
     * 修改数据
     *
     * @param matSpectempEntity 实例对象
     * @return 影响行数
     */
    int approval(MatSpectempEntity matSpectempEntity);
}

