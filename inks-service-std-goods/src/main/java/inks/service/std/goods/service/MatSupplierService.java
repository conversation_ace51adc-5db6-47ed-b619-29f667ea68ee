package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSupplierPojo;

/**
 * 货品供应(MatSupplier)表服务接口
 *
 * <AUTHOR>
 * @since 2022-01-03 19:51:11
 */
public interface MatSupplierService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSupplierPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSupplierPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSupplierPojo 实例对象
     * @return 实例对象
     */
    MatSupplierPojo insert(MatSupplierPojo matSupplierPojo);

    /**
     * 修改数据
     *
     * @param matSupplierpojo 实例对象
     * @return 实例对象
     */
    MatSupplierPojo update(MatSupplierPojo matSupplierpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
