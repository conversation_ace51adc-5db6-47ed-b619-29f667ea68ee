package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatBomorderEntity;
import inks.service.std.goods.domain.MatBomorderitemEntity;
import inks.service.std.goods.domain.pojo.MatBomorderPojo;
import inks.service.std.goods.domain.pojo.MatBomorderitemPojo;
import inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo;
import inks.service.std.goods.domain.pojo.MatBomordertreePojo;
import inks.service.std.goods.mapper.MatBomorderMapper;
import inks.service.std.goods.mapper.MatBomorderitemMapper;
import inks.service.std.goods.service.MatBomorderService;
import inks.service.std.goods.service.MatBomorderitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单Bom(MatBomorder)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-08 11:27:05
 */
@Service("matBomorderService")
public class MatBomorderServiceImpl implements MatBomorderService {
    @Resource
    private MatBomorderMapper matBomorderMapper;

    @Resource
    private MatBomorderitemMapper matBomorderitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private MatBomorderitemService matBomorderitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomorderPojo getEntity(String key, String tid) {
        return this.matBomorderMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBomorderitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBomorderitemdetailPojo> lst = matBomorderMapper.getPageList(queryParam);
            PageInfo<MatBomorderitemdetailPojo> pageInfo = new PageInfo<MatBomorderitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomorderPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatBomorderPojo matBomorderPojo = this.matBomorderMapper.getEntity(key, tid);
            //读取子表
            matBomorderPojo.setItem(matBomorderitemMapper.getList(matBomorderPojo.getId(), matBomorderPojo.getTenantid()));
            return matBomorderPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBomorderPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBomorderPojo> lst = matBomorderMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matBomorderitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatBomorderPojo> pageInfo = new PageInfo<MatBomorderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBomorderPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBomorderPojo> lst = matBomorderMapper.getPageTh(queryParam);
            PageInfo<MatBomorderPojo> pageInfo = new PageInfo<MatBomorderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matBomorderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatBomorderPojo insert(MatBomorderPojo matBomorderPojo) {
//初始化NULL字段
        if (matBomorderPojo.getMachuid() == null) matBomorderPojo.setMachuid("");
        if (matBomorderPojo.getMachitemid() == null) matBomorderPojo.setMachitemid("");
        if (matBomorderPojo.getMachgroupid() == null) matBomorderPojo.setMachgroupid("");
        if (matBomorderPojo.getGoodsid() == null) matBomorderPojo.setGoodsid("");
        if (matBomorderPojo.getItemcode() == null) matBomorderPojo.setItemcode("");
        if (matBomorderPojo.getItemname() == null) matBomorderPojo.setItemname("");
        if (matBomorderPojo.getItemspec() == null) matBomorderPojo.setItemspec("");
        if (matBomorderPojo.getItemunit() == null) matBomorderPojo.setItemunit("");
        if (matBomorderPojo.getQuantity() == null) matBomorderPojo.setQuantity(0D);
        if (matBomorderPojo.getSummary() == null) matBomorderPojo.setSummary("");
        if (matBomorderPojo.getCreateby() == null) matBomorderPojo.setCreateby("");
        if (matBomorderPojo.getCreatebyid() == null) matBomorderPojo.setCreatebyid("");
        if (matBomorderPojo.getCreatedate() == null) matBomorderPojo.setCreatedate(new Date());
        if (matBomorderPojo.getListerid() == null) matBomorderPojo.setListerid("");
        if (matBomorderPojo.getLister() == null) matBomorderPojo.setLister("");
        if (matBomorderPojo.getModifydate() == null) matBomorderPojo.setModifydate(new Date());
        if (matBomorderPojo.getAssessor() == null) matBomorderPojo.setAssessor("");
        if (matBomorderPojo.getAssessorid() == null) matBomorderPojo.setAssessorid("");
        if (matBomorderPojo.getAssessdate() == null) matBomorderPojo.setAssessdate(new Date());
        if (matBomorderPojo.getCustom1() == null) matBomorderPojo.setCustom1("");
        if (matBomorderPojo.getCustom2() == null) matBomorderPojo.setCustom2("");
        if (matBomorderPojo.getCustom3() == null) matBomorderPojo.setCustom3("");
        if (matBomorderPojo.getCustom4() == null) matBomorderPojo.setCustom4("");
        if (matBomorderPojo.getCustom5() == null) matBomorderPojo.setCustom5("");
        if (matBomorderPojo.getCustom6() == null) matBomorderPojo.setCustom6("");
        if (matBomorderPojo.getCustom7() == null) matBomorderPojo.setCustom7("");
        if (matBomorderPojo.getCustom8() == null) matBomorderPojo.setCustom8("");
        if (matBomorderPojo.getCustom9() == null) matBomorderPojo.setCustom9("");
        if (matBomorderPojo.getCustom10() == null) matBomorderPojo.setCustom10("");
        if (matBomorderPojo.getTenantid() == null) matBomorderPojo.setTenantid("");
        if (matBomorderPojo.getRevision() == null) matBomorderPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatBomorderEntity matBomorderEntity = new MatBomorderEntity();
        BeanUtils.copyProperties(matBomorderPojo, matBomorderEntity);
        //设置id和新建日期
        matBomorderEntity.setId(id);
        matBomorderEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matBomorderMapper.insert(matBomorderEntity);
        //Item子表处理
        List<MatBomorderitemPojo> lst = matBomorderPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatBomorderitemPojo itemPojo = this.matBomorderitemService.clearNull(lst.get(i));
                MatBomorderitemEntity matBomorderitemEntity = new MatBomorderitemEntity();
                BeanUtils.copyProperties(itemPojo, matBomorderitemEntity);
                //设置id和Pid
                matBomorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matBomorderitemEntity.setPid(id);
                matBomorderitemEntity.setTenantid(matBomorderPojo.getTenantid());
                matBomorderitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matBomorderitemMapper.insert(matBomorderitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(matBomorderEntity.getId(), matBomorderEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matBomorderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatBomorderPojo update(MatBomorderPojo matBomorderPojo) {
        //主表更改
        MatBomorderEntity matBomorderEntity = new MatBomorderEntity();
        BeanUtils.copyProperties(matBomorderPojo, matBomorderEntity);
        this.matBomorderMapper.update(matBomorderEntity);
        if (matBomorderPojo.getItem() != null) {
            //Item子表处理
            List<MatBomorderitemPojo> lst = matBomorderPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = matBomorderMapper.getDelItemIds(matBomorderPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.matBomorderitemMapper.delete(lstDelIds.get(i), matBomorderEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    MatBomorderitemEntity matBomorderitemEntity = new MatBomorderitemEntity();
                    if (lst.get(i).getId() == "" || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        MatBomorderitemPojo itemPojo = this.matBomorderitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, matBomorderitemEntity);
                        //设置id和Pid
                        matBomorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matBomorderitemEntity.setPid(matBomorderEntity.getId());  // 主表 id
                        matBomorderitemEntity.setTenantid(matBomorderPojo.getTenantid());   // 租户id
                        matBomorderitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matBomorderitemMapper.insert(matBomorderitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), matBomorderitemEntity);
                        matBomorderitemEntity.setTenantid(matBomorderPojo.getTenantid());
                        this.matBomorderitemMapper.update(matBomorderitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(matBomorderEntity.getId(), matBomorderEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        MatBomorderPojo matBomorderPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatBomorderitemPojo> lst = matBomorderPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (MatBomorderitemPojo matBomorderitemPojo : lst) {
                this.matBomorderitemMapper.delete(matBomorderitemPojo.getId(), tid);
            }
        }
        this.matBomorderMapper.delete(key, tid);
        return matBomorderPojo.getMachuid();
    }


    /**
     * 审核数据
     *
     * @param matBomorderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatBomorderPojo approval(MatBomorderPojo matBomorderPojo) {
        //主表更改
        MatBomorderEntity matBomorderEntity = new MatBomorderEntity();
        BeanUtils.copyProperties(matBomorderPojo, matBomorderEntity);
        this.matBomorderMapper.approval(matBomorderEntity);
        //返回Bill实例
        return this.getBillEntity(matBomorderEntity.getId(), matBomorderEntity.getTenantid());
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomordertreePojo getTreeEntity(String key, String tid) {
        try {
            //读取主表
            MatBomorderPojo matBomorderPojo = this.matBomorderMapper.getEntity(key, tid);
            MatBomordertreePojo matBomordertreePojo = new MatBomordertreePojo();
            BeanUtils.copyProperties(matBomorderPojo, matBomordertreePojo);
            List<MatBomordertreePojo> ls = getTreeitem(matBomorderPojo.getId(), matBomorderPojo.getTenantid());
            return matBomordertreePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    private List<MatBomordertreePojo> getTreeitem(String key, String tid) {
        //读取子表
        List<MatBomorderitemPojo> lstitem = this.matBomorderitemMapper.getList(key, tid);
        List<MatBomordertreePojo> lsttree = new ArrayList<>();
        BeanUtils.copyProperties(lstitem, lsttree);
        return lsttree;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public List<MatBomorderitemdetailPojo> getListByItemGoodsid(String key, String tid) {
        try {
            return this.matBomorderMapper.getListByItemGoodsid(key, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }

    }

}
