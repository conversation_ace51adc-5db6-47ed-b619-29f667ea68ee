package inks.service.std.goods.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * Pcb工艺Drl(MatSpecpcbdrl)Pojo
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:46
 */
public class MatSpecpcbdrlPojo implements Serializable {
    private static final long serialVersionUID = -74797902883611163L;
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 符号
    @Excel(name = "符号")
    private String symbol;
    // 孔径
    @Excel(name = "孔径")
    private Double holesize;
    // 公差
    @Excel(name = "公差")
    private String tolerance;
    // 刀径
    @Excel(name = "刀径")
    private Double drillsize;
    // PCS合计
    @Excel(name = "PCS合计")
    private Integer pcstotal;
    // Set合计
    @Excel(name = "Set合计")
    private Integer settotal;
    // PNL合计
    @Excel(name = "PNL合计")
    private Integer pnltotal;
    // PTH
    @Excel(name = "PTH")
    private String pthmark;
    @Excel(name = "")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 符号
    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    // 孔径
    public Double getHolesize() {
        return holesize;
    }

    public void setHolesize(Double holesize) {
        this.holesize = holesize;
    }

    // 公差
    public String getTolerance() {
        return tolerance;
    }

    public void setTolerance(String tolerance) {
        this.tolerance = tolerance;
    }

    // 刀径
    public Double getDrillsize() {
        return drillsize;
    }

    public void setDrillsize(Double drillsize) {
        this.drillsize = drillsize;
    }

    // PCS合计
    public Integer getPcstotal() {
        return pcstotal;
    }

    public void setPcstotal(Integer pcstotal) {
        this.pcstotal = pcstotal;
    }

    // Set合计
    public Integer getSettotal() {
        return settotal;
    }

    public void setSettotal(Integer settotal) {
        this.settotal = settotal;
    }

    // PNL合计
    public Integer getPnltotal() {
        return pnltotal;
    }

    public void setPnltotal(Integer pnltotal) {
        this.pnltotal = pnltotal;
    }

    // PTH
    public String getPthmark() {
        return pthmark;
    }

    public void setPthmark(String pthmark) {
        this.pthmark = pthmark;
    }

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }


}

