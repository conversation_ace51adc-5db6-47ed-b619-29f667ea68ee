package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSpecEntity;
import inks.service.std.goods.domain.pojo.MatSpecPojo;
import inks.service.std.goods.domain.pojo.MatSpecitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工艺流程卡(MatSpec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-16 10:38:33
 */
@Mapper
public interface MatSpecMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param matSpecEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpecEntity matSpecEntity);

    
    /**
     * 修改数据
     *
     * @param matSpecEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpecEntity matSpecEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param matSpecPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(MatSpecPojo matSpecPojo);
                                                                                                                  /**
     * 修改数据
     *
     * @param matSpecEntity 实例对象
     * @return 影响行数
     */
    int approval(MatSpecEntity matSpecEntity);
                                                                                 }

