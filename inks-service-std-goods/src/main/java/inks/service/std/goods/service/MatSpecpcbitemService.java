package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecpcbitemPojo;

import java.util.List;

/**
 * PCB工艺项目(MatSpecpcbitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:57
 */
public interface MatSpecpcbitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecpcbitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecpcbitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecpcbitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSpecpcbitemPojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbitemPojo insert(MatSpecpcbitemPojo matSpecpcbitemPojo);

    /**
     * 修改数据
     *
     * @param matSpecpcbitempojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbitemPojo update(MatSpecpcbitemPojo matSpecpcbitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSpecpcbitempojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbitemPojo clearNull(MatSpecpcbitemPojo matSpecpcbitempojo);
}
