package inks.service.std.goods.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatGoodscustPojo;
import inks.service.std.goods.service.MatGoodscustService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 货品自定义(Mat_GoodsCust)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-15 16:25:19
 */
//@RestController
//@RequestMapping("matGoodscust")
public class MatGoodscustController {

    @Resource
    private MatGoodscustService matGoodscustService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(MatGoodscustController.class);


    @ApiOperation(value=" 获取货品自定义详细信息", notes="获取货品自定义详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_GoodsCust.List")
    public R<MatGoodscustPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matGoodscustService.getEntity(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_GoodsCust.List")
    public R<PageInfo<MatGoodscustPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Mat_GoodsCust.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodscustService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增货品自定义", notes="新增货品自定义", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_GoodsCust.Add")
    public R<MatGoodscustPojo> create(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
       MatGoodscustPojo matGoodscustPojo = JSONArray.parseObject(json,MatGoodscustPojo.class);       
            matGoodscustPojo.setCreateby(loginUser.getRealName());   // 创建者
            matGoodscustPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matGoodscustPojo.setCreatedate(new Date());   // 创建时间
            matGoodscustPojo.setLister(loginUser.getRealname());   // 制表
            matGoodscustPojo.setListerid(loginUser.getUserid());    // 制表id  
            matGoodscustPojo.setModifydate(new Date());   //修改时间
            matGoodscustPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.matGoodscustService.insert(matGoodscustPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改货品自定义", notes="修改货品自定义", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_GoodsCust.Edit")
    public R<MatGoodscustPojo> update(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
         MatGoodscustPojo matGoodscustPojo = JSONArray.parseObject(json,MatGoodscustPojo.class);
            matGoodscustPojo.setLister(loginUser.getRealname());   // 制表
            matGoodscustPojo.setListerid(loginUser.getUserid());    // 制表id  
            matGoodscustPojo.setTenantid(loginUser.getTenantid());   //租户id
            matGoodscustPojo.setModifydate(new Date());   //修改时间
//            matGoodscustPojo.setAssessor(""); // 审核员
//            matGoodscustPojo.setAssessorid(""); // 审核员id
//            matGoodscustPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.matGoodscustService.update(matGoodscustPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除货品自定义", notes="删除货品自定义", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_GoodsCust.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matGoodscustService.delete(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_GoodsCust.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatGoodscustPojo matGoodscustPojo = this.matGoodscustService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matGoodscustPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

