package inks.service.std.goods.mapper;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatBomEntity;
import inks.service.std.goods.domain.pojo.MatBomPojo;
import inks.service.std.goods.domain.pojo.MatBomitemdetailPojo;
import inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 物料Bom(MatBom)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-08 11:20:42
 */
@Mapper
public interface MatBomMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatBomitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatBomPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matBomEntity 实例对象
     * @return 影响行数
     */
    int insert(MatBomEntity matBomEntity);


    /**
     * 修改数据
     *
     * @param matBomEntity 实例对象
     * @return 影响行数
     */
    int update(MatBomEntity matBomEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matBomPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(MatBomPojo matBomPojo);

    /**
     * 修改数据
     *
     * @param matBomEntity 实例对象
     * @return 影响行数
     */
    int approval(MatBomEntity matBomEntity);

    int approvalBatch(@Param("keys") List<String> keys, @Param("loginUser") LoginUser loginUser, @Param("date") Date date);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomPojo getEntityByGoodsid(@Param("key") String key, @Param("tid") String tid);

    String getBomidByGoodsid(@Param("goodsid") String goodsid, @Param("tenantid") String tenantid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<MatBomorderitemdetailPojo> getListByItemGoodsid(@Param("key") String key, @Param("tid") String tid);


    /**
     * 刷新货品信息的Bomid
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateGoodsBomid(@Param("key") String key, @Param("bomid") String bomid,@Param("tid") String tid);

    String findBomidByGoodsid(@Param("key") String key, @Param("tid") String tid);

}

