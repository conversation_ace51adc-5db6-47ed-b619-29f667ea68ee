package inks.service.std.goods.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatAttributePojo;
import inks.service.std.goods.domain.pojo.MatGoodscustPojo;
import inks.service.std.goods.service.MatGoodscustService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 货品自定义(Mat_GoodsCust)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-15 16:25:19
 */
@RestController
@RequestMapping("D91M01S4")
@Api(tags = "D91M01S4:货品自定义")
public class D91M01S4Controller extends MatGoodscustController {

    @Resource
    private MatGoodscustService matGoodscustService;
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "获得显示的货品自定义 ShowMark=1 && EnabledMark=1", notes = "", produces = "application/json")
    @RequestMapping(value = "/getListByShow", method = RequestMethod.GET)
    public R<List<MatGoodscustPojo>> getListByShow() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matGoodscustService.getListByShow(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获得正常的货品自定义 EnabledMark=1", notes = "", produces = "application/json")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    public R<List<MatGoodscustPojo>> getList() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matGoodscustService.getList(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
