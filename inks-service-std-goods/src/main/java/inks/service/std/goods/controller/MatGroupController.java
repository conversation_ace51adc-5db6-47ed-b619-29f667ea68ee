package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatGoodsPojo;
import inks.service.std.goods.domain.pojo.MatGroupPojo;
import inks.service.std.goods.service.MatGoodsService;
import inks.service.std.goods.service.MatGroupService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 物料分组(Mat_Group)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-21 09:03:22
 */
public class MatGroupController {
    /**
     * 服务对象
     */
    @Resource
    private MatGroupService matGroupService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 服务对象
     */
    @Resource
    private MatGoodsService matGoodsService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取物料分组详细信息", notes = "获取物料分组详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Group.List")
    public R<MatGroupPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matGroupService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Group.List")
    public R<PageInfo<MatGroupPojo>> getPageList(@RequestBody String json, String statecode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Group.RowNum");
            String qpfilter = "";
            if (StringUtils.isNotBlank(statecode)) {
                qpfilter = " and Mat_Group.StateCode='" + statecode + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.matGroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增物料分组", notes = "新增物料分组", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Group.Add")
    public R<MatGroupPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatGroupPojo matGroupPojo = JSONArray.parseObject(json, MatGroupPojo.class);
            matGroupPojo.setCreateby(loginUser.getRealname());   // 创建者
            matGroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matGroupPojo.setCreatedate(new Date());   // 创建时间
            matGroupPojo.setLister(loginUser.getRealname());   // 制表
            matGroupPojo.setListerid(loginUser.getUserid());    // 制表id  
            matGroupPojo.setModifydate(new Date());   //修改时间
            matGroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            matGroupPojo.setTenantname(loginUser.getTenantinfo().getTenantname()); //
            return R.ok(this.matGroupService.insert(matGroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改物料分组", notes = "修改物料分组", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Group.Edit")
    public R<MatGroupPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatGroupPojo matGroupPojo = JSONArray.parseObject(json, MatGroupPojo.class);
            matGroupPojo.setLister(loginUser.getRealname());   // 制表
            matGroupPojo.setListerid(loginUser.getUserid());    // 制表id  
            matGroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            matGroupPojo.setTenantname(loginUser.getTenantinfo().getTenantname()); //
            matGroupPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.matGroupService.update(matGroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除物料分组", notes = "删除物料分组", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Group.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<MatGroupPojo> lst = this.matGroupService.getListByParentid(key, loginUser.getTenantid());
            if (lst != null && !lst.isEmpty()) {
                return R.fail(403, "请先删除子分组");
            }

            MatGoodsPojo matGoodsPojo = this.matGoodsService.getEntityByGroup(key, loginUser.getTenantid());
            if (matGoodsPojo != null) {
                return R.fail(403, "请先删除分组货品");
            }

            return R.ok(this.matGroupService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Group.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatGroupPojo matGroupPojo = this.matGroupService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matGroupPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

