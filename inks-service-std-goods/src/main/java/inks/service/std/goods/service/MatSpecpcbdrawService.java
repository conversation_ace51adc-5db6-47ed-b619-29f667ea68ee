package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecpcbdrawPojo;

import java.util.List;

/**
 * Pcb工艺画(MatSpecpcbdraw)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-16 10:33:26
 */
public interface MatSpecpcbdrawService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecpcbdrawPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecpcbdrawPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecpcbdrawPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSpecpcbdrawPojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbdrawPojo insert(MatSpecpcbdrawPojo matSpecpcbdrawPojo);

    /**
     * 修改数据
     *
     * @param matSpecpcbdrawpojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbdrawPojo update(MatSpecpcbdrawPojo matSpecpcbdrawpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSpecpcbdrawpojo 实例对象
     * @return 实例对象
     */
    MatSpecpcbdrawPojo clearNull(MatSpecpcbdrawPojo matSpecpcbdrawpojo);
}
