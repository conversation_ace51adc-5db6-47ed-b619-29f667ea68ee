package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatBomitemEntity;
import inks.service.std.goods.domain.pojo.MatBomPojo;
import inks.service.std.goods.domain.pojo.MatBomitemPojo;
import inks.service.std.goods.domain.pojo.MatGoodsPojo;
import inks.service.std.goods.mapper.MatBomitemMapper;
import inks.service.std.goods.mapper.MatGoodsMapper;
import inks.service.std.goods.service.MatBomService;
import inks.service.std.goods.service.MatBomitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * BOM子表(MatBomitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-22 20:23:12
 */
@Service("matBomitemService")
public class MatBomitemServiceImpl implements MatBomitemService {
    @Resource
    private MatBomitemMapper matBomitemMapper;
    @Resource
    @Lazy
    private MatBomService matBomService;
    @Resource
    private MatGoodsMapper matGoodsMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBomitemPojo getEntity(String key, String tid) {
        return this.matBomitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBomitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBomitemPojo> lst = matBomitemMapper.getPageList(queryParam);
            PageInfo<MatBomitemPojo> pageInfo = new PageInfo<MatBomitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatBomitemPojo> getList(String Pid, String tid) {
        try {
            List<MatBomitemPojo> lst = matBomitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matBomitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatBomitemPojo insert(MatBomitemPojo matBomitemPojo) {
        //初始化item的NULL
        MatBomitemPojo itempojo = this.clearNull(matBomitemPojo);
        MatBomitemEntity matBomitemEntity = new MatBomitemEntity();
        BeanUtils.copyProperties(itempojo, matBomitemEntity);

        matBomitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matBomitemEntity.setRevision(1);  //乐观锁
        this.matBomitemMapper.insert(matBomitemEntity);
        return this.getEntity(matBomitemEntity.getId(), matBomitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matBomitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatBomitemPojo update(MatBomitemPojo matBomitemPojo) {
        MatBomitemEntity matBomitemEntity = new MatBomitemEntity();
        BeanUtils.copyProperties(matBomitemPojo, matBomitemEntity);
        this.matBomitemMapper.update(matBomitemEntity);
        return this.getEntity(matBomitemEntity.getId(), matBomitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matBomitemMapper.delete(key, tid);
    }


    // 避免循环依赖  matBomService 和 matBomitemService 之间存在循环依赖关系
    public static MatBomitemPojo clearNullStatic(MatBomitemPojo matBomitemPojo) {
        //初始化NULL字段
        cleanNull(matBomitemPojo);
        return matBomitemPojo;
    }

    @Override
    public MatBomitemPojo clearNull(MatBomitemPojo matBomitemPojo) {
        //初始化NULL字段
        cleanNull(matBomitemPojo);
        return matBomitemPojo;
    }

    private static void cleanNull(MatBomitemPojo matBomitemPojo) {
        if (matBomitemPojo.getPid() == null) matBomitemPojo.setPid("");
        if (matBomitemPojo.getGoodsid() == null) matBomitemPojo.setGoodsid("");
        if (matBomitemPojo.getItemcode() == null) matBomitemPojo.setItemcode("");
        if (matBomitemPojo.getItemname() == null) matBomitemPojo.setItemname("");
        if (matBomitemPojo.getItemspec() == null) matBomitemPojo.setItemspec("");
        if (matBomitemPojo.getItemunit() == null) matBomitemPojo.setItemunit("");
        if (matBomitemPojo.getMainqty() == null) matBomitemPojo.setMainqty(0D);
        if (matBomitemPojo.getSubqty() == null) matBomitemPojo.setSubqty(0D);
        if (matBomitemPojo.getLossrate() == null) matBomitemPojo.setLossrate(0D);
        if (matBomitemPojo.getAttrcode() == null) matBomitemPojo.setAttrcode("");
        if (matBomitemPojo.getFlowcode() == null) matBomitemPojo.setFlowcode("");
        if (matBomitemPojo.getDescription() == null) matBomitemPojo.setDescription("");
        if (matBomitemPojo.getItemlabel() == null) matBomitemPojo.setItemlabel("");
        if (matBomitemPojo.getParentid() == null) matBomitemPojo.setParentid("");
        if (matBomitemPojo.getParentgoodsid() == null) matBomitemPojo.setParentgoodsid("");
        if (matBomitemPojo.getRownum() == null) matBomitemPojo.setRownum(0);
        if (matBomitemPojo.getSubcount() == null) matBomitemPojo.setSubcount(0);
        if (matBomitemPojo.getRemark() == null) matBomitemPojo.setRemark("");
     if(matBomitemPojo.getSublossqty()==null) matBomitemPojo.setSublossqty(0D);
     if(matBomitemPojo.getMattype()==null) matBomitemPojo.setMattype(0);
        if (matBomitemPojo.getCustom1() == null) matBomitemPojo.setCustom1("");
        if (matBomitemPojo.getCustom2() == null) matBomitemPojo.setCustom2("");
        if (matBomitemPojo.getCustom3() == null) matBomitemPojo.setCustom3("");
        if (matBomitemPojo.getCustom4() == null) matBomitemPojo.setCustom4("");
        if (matBomitemPojo.getCustom5() == null) matBomitemPojo.setCustom5("");
        if (matBomitemPojo.getCustom6() == null) matBomitemPojo.setCustom6("");
        if (matBomitemPojo.getCustom7() == null) matBomitemPojo.setCustom7("");
        if (matBomitemPojo.getCustom8() == null) matBomitemPojo.setCustom8("");
        if (matBomitemPojo.getCustom9() == null) matBomitemPojo.setCustom9("");
        if (matBomitemPojo.getCustom10() == null) matBomitemPojo.setCustom10("");
        if (matBomitemPojo.getTenantid() == null) matBomitemPojo.setTenantid("");
        if (matBomitemPojo.getRevision() == null) matBomitemPojo.setRevision(0);
    }




    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public List<MatBomitemPojo> getListByGoodsid(String key, String pid, String tid) {
        return this.matBomitemMapper.getListByGoodsid(key, pid, tid);
    }

    @Override
    public List<MatBomPojo> getListByItemGoodsid(String key, String tid) {
        List<String> pids = this.matBomitemMapper.getPidsByGoodsid(key, tid);
        // 判空pids
        if (pids.isEmpty()) {
            throw new RuntimeException("当前货品未被任何BOM主表引用过");
        }
        List<MatBomPojo> matBomPojoList = new ArrayList<>();
        for (String pid : pids) {
            MatBomPojo matBomPojo = this.matBomService.getBillEntity(pid, tid);
            matBomPojoList.add(matBomPojo);
        }
        return matBomPojoList;
    }

    @Override
    public List<MatGoodsPojo> getParentBom(String key, String tid) {
        List<String> pids = this.matBomitemMapper.getPidsByGoodsid(key, tid);
        // 判空pids
        if (pids.isEmpty()) {
            throw new RuntimeException("当前货品未被任何BOM主表引用过");
        }
        return matGoodsMapper.getListByBomids(pids, tid);
    }
}
