package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatCamprojectPojo;
import inks.service.std.goods.service.MatCamprojectService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 工程事务(Mat_CamProject)表控制层
 *
 * <AUTHOR>
 * @since 2024-08-21 09:17:15
 */
//@RestController
//@RequestMapping("matCamproject")
public class MatCamprojectController {

    private final String moduleCode = "D91M11B1";
    @Resource
    private MatCamprojectService matCamprojectService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private UtilsFeignService utilsFeignService;

    @ApiOperation(value = " 获取工程事务详细信息", notes = "获取工程事务详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.List")
    public R<MatCamprojectPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matCamprojectService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CamProject.List")
    public R<PageInfo<MatCamprojectPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_CamProject.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matCamprojectService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增工程事务", notes = "新增工程事务", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CamProject.Add")
    public R<MatCamprojectPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatCamprojectPojo matCamprojectPojo = JSONArray.parseObject(json, MatCamprojectPojo.class);
            String tid = loginUser.getTenantid();
            //生成单据编码
            String refno = RefNoUtils.generateRefNo(moduleCode, "Mat_CamProject", null, tid);
            matCamprojectPojo.setRefno(refno);
            matCamprojectPojo.setCreateby(loginUser.getRealName());   // 创建者
            matCamprojectPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matCamprojectPojo.setCreatedate(new Date());   // 创建时间
            matCamprojectPojo.setLister(loginUser.getRealname());   // 制表
            matCamprojectPojo.setListerid(loginUser.getUserid());    // 制表id
            matCamprojectPojo.setModifydate(new Date());   //修改时间
            matCamprojectPojo.setTenantid(loginUser.getTenantid());   //租户id
            MatCamprojectPojo insert = this.matCamprojectService.insert(matCamprojectPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, tid);
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 导入工程事务", notes = "新增工程事务", produces = "application/json")
    @RequestMapping(value = "/syncCreate", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CamProject.Add")
    public R<MatCamprojectPojo> syncCreate(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        synchronized (tid.intern()) {
            try {
                MatCamprojectPojo matCamprojectPojo = JSONArray.parseObject(json, MatCamprojectPojo.class);
                //生成单据编码
                String refno = RefNoUtils.generateRefNo(moduleCode, "Mat_CamProject", null, tid);
                matCamprojectPojo.setRefno(refno);
                matCamprojectPojo.setCreateby(loginUser.getRealName());   // 创建者
                matCamprojectPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                matCamprojectPojo.setCreatedate(new Date());   // 创建时间
                matCamprojectPojo.setLister(loginUser.getRealname());   // 制表
                matCamprojectPojo.setListerid(loginUser.getUserid());    // 制表id
                matCamprojectPojo.setModifydate(new Date());   //修改时间
                matCamprojectPojo.setTenantid(loginUser.getTenantid());   //租户id
                MatCamprojectPojo insert = this.matCamprojectService.insert(matCamprojectPojo);
                RefNoUtils.saveRedisRefNo(refno, moduleCode, tid);
                return R.ok(insert);
            } catch (Exception e) {
                return R.fail(e.getMessage());
            }
        }
    }


    @ApiOperation(value = "修改工程事务", notes = "修改工程事务", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CamProject.Edit")
    public R<MatCamprojectPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatCamprojectPojo matCamprojectPojo = JSONArray.parseObject(json, MatCamprojectPojo.class);
            matCamprojectPojo.setLister(loginUser.getRealname());   // 制表
            matCamprojectPojo.setListerid(loginUser.getUserid());    // 制表id  
            matCamprojectPojo.setTenantid(loginUser.getTenantid());   //租户id
            matCamprojectPojo.setModifydate(new Date());   //修改时间
//            matCamprojectPojo.setAssessor(""); // 审核员
//            matCamprojectPojo.setAssessorid(""); // 审核员id
//            matCamprojectPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matCamprojectService.update(matCamprojectPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除工程事务", notes = "删除工程事务", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            this.matCamprojectService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "审核工程事务", notes = "审核工程事务", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.Approval")
    public R<MatCamprojectPojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatCamprojectPojo matCamprojectPojo = this.matCamprojectService.getEntity(key, loginUser.getTenantid());
            if (matCamprojectPojo.getAssessor().equals("")) {
                matCamprojectPojo.setAssessor(loginUser.getRealname()); //审核员
                matCamprojectPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                matCamprojectPojo.setAssessor(""); //审核员
                matCamprojectPojo.setAssessorid(""); //审核员
            }
            matCamprojectPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matCamprojectService.approval(matCamprojectPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatCamprojectPojo matCamprojectPojo = this.matCamprojectService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matCamprojectPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CamProject.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            MatCamprojectPojo pojo = this.matCamprojectService.getEntity(key, loginUser.getTenantid());

            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved == null || !printapproved.equals("true")) {
                    throw new BaseBusinessException("printapproved:无打印权限");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(pojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            // 单据Item. 带属性List转为Map  EricRen 20220427

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "MatCamproject");
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量云打印MatCamproject单据(ids)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBatchBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CamProject.Print")
    public R<String> printWebBatchBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            StringBuilder ptRefNoMain = new StringBuilder();
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            for (String key : lstkeys) {
                //=========获取单据表头信息========
                MatCamprojectPojo matCamprojectPojo = this.matCamprojectService.getEntity(key, loginUser.getTenantid());
                if (matCamprojectPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                if (printapproved != null && printapproved.equals("true") && matCamprojectPojo.getAssessor().isEmpty()) {
                    throw new BaseBusinessException("请先审核单据");
                }
                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(matCamprojectPojo);

                // 获取单据表头.加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息 空的========
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = new ArrayList<>();
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
                ptRefNoMain.append(matCamprojectPojo.getRefno()).append(",");
                // 刷入打印Num++

            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.toString().split(",");
            mapPrint.put("msg", "MatCamproject：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

