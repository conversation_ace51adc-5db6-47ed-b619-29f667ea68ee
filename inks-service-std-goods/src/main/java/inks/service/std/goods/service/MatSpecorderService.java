package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSpecorderPojo;
import inks.service.std.goods.domain.pojo.MatSpecorderitemdetailPojo;

/**
 * 订单工艺卡(MatSpecorder)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-16 14:42:35
 */
public interface MatSpecorderService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecorderPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecorderitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecorderPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecorderPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSpecorderPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSpecorderPojo 实例对象
     * @return 实例对象
     */
    MatSpecorderPojo insert(MatSpecorderPojo matSpecorderPojo);

    /**
     * 修改数据
     *
     * @param matSpecorderpojo 实例对象
     * @return 实例对象
     */
    MatSpecorderPojo update(MatSpecorderPojo matSpecorderpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matSpecorderPojo 实例对象
     * @return 实例对象
     */
    MatSpecorderPojo approval(MatSpecorderPojo matSpecorderPojo);

}
