package inks.service.std.goods.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.MatSpecitemEntity;
import inks.service.std.goods.domain.pojo.MatSpecitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工艺项目(MatSpecitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-16 10:38:44
 */
 @Mapper
public interface MatSpecitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSpecitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSpecitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSpecitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matSpecitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSpecitemEntity matSpecitemEntity);

    
    /**
     * 修改数据
     *
     * @param matSpecitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatSpecitemEntity matSpecitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

