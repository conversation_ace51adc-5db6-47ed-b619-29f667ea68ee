package inks.service.std.goods.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.goods.domain.MatSubstituteEntity;
import inks.service.std.goods.domain.MatSubstitutebomEntity;
import inks.service.std.goods.domain.MatSubstituteitemEntity;
import inks.service.std.goods.domain.pojo.MatSubstitutePojo;
import inks.service.std.goods.domain.pojo.MatSubstitutebomPojo;
import inks.service.std.goods.domain.pojo.MatSubstituteitemPojo;
import inks.service.std.goods.domain.pojo.MatSubstituteitemdetailPojo;
import inks.service.std.goods.mapper.MatSubstituteMapper;
import inks.service.std.goods.mapper.MatSubstitutebomMapper;
import inks.service.std.goods.mapper.MatSubstituteitemMapper;
import inks.service.std.goods.service.MatSubstituteService;
import inks.service.std.goods.service.MatSubstitutebomService;
import inks.service.std.goods.service.MatSubstituteitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 替代品管理(MatSubstitute)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-06 11:17:34
 */
@Service("matSubstituteService")
public class MatSubstituteServiceImpl implements MatSubstituteService {
    @Resource
    private MatSubstituteMapper matSubstituteMapper;

    @Resource
    private MatSubstituteitemMapper matSubstituteitemMapper;

    @Resource
    private MatSubstitutebomMapper matSubstitutebomMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private MatSubstituteitemService matSubstituteitemService;

    /**
     * 服务对象Item
     */
    @Resource
    private MatSubstitutebomService matSubstitutebomService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSubstitutePojo getEntity(String key, String tid) {
        return this.matSubstituteMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSubstituteitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSubstituteitemdetailPojo> lst = matSubstituteMapper.getPageList(queryParam);
            PageInfo<MatSubstituteitemdetailPojo> pageInfo = new PageInfo<MatSubstituteitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSubstitutePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatSubstitutePojo matSubstitutePojo = this.matSubstituteMapper.getEntity(key, tid);
            //读取子表
            matSubstitutePojo.setItem(matSubstituteitemMapper.getList(matSubstitutePojo.getId(), matSubstitutePojo.getTenantid()));
            //读取Bom子表
            matSubstitutePojo.setBom(matSubstitutebomMapper.getList(matSubstitutePojo.getId(), matSubstitutePojo.getTenantid()));
            return matSubstitutePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSubstitutePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSubstitutePojo> lst = matSubstituteMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matSubstituteitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setBom(matSubstitutebomMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatSubstitutePojo> pageInfo = new PageInfo<MatSubstitutePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSubstitutePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSubstitutePojo> lst = matSubstituteMapper.getPageTh(queryParam);
            PageInfo<MatSubstitutePojo> pageInfo = new PageInfo<MatSubstitutePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matSubstitutePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSubstitutePojo insert(MatSubstitutePojo matSubstitutePojo) {
//初始化NULL字段
        if (matSubstitutePojo.getRefno() == null) matSubstitutePojo.setRefno("");
        if (matSubstitutePojo.getBilldate() == null) matSubstitutePojo.setBilldate(new Date());
        if (matSubstitutePojo.getBilltype() == null) matSubstitutePojo.setBilltype("");
        if (matSubstitutePojo.getBilltitle() == null) matSubstitutePojo.setBilltitle("");
        if (matSubstitutePojo.getGoodsid() == null) matSubstitutePojo.setGoodsid("");
        if (matSubstitutePojo.getItemcode() == null) matSubstitutePojo.setItemcode("");
        if (matSubstitutePojo.getItemname() == null) matSubstitutePojo.setItemname("");
        if (matSubstitutePojo.getItemspec() == null) matSubstitutePojo.setItemspec("");
        if (matSubstitutePojo.getItemunit() == null) matSubstitutePojo.setItemunit("");
        if (matSubstitutePojo.getBlendmark() == null) matSubstitutePojo.setBlendmark(0);
        if (matSubstitutePojo.getBommark() == null) matSubstitutePojo.setBommark(0);
        if (matSubstitutePojo.getEnabledmark() == null) matSubstitutePojo.setEnabledmark(0);
        if (matSubstitutePojo.getSummary() == null) matSubstitutePojo.setSummary("");
        if (matSubstitutePojo.getCreateby() == null) matSubstitutePojo.setCreateby("");
        if (matSubstitutePojo.getCreatebyid() == null) matSubstitutePojo.setCreatebyid("");
        if (matSubstitutePojo.getCreatedate() == null) matSubstitutePojo.setCreatedate(new Date());
        if (matSubstitutePojo.getLister() == null) matSubstitutePojo.setLister("");
        if (matSubstitutePojo.getListerid() == null) matSubstitutePojo.setListerid("");
        if (matSubstitutePojo.getModifydate() == null) matSubstitutePojo.setModifydate(new Date());
        if (matSubstitutePojo.getCustom1() == null) matSubstitutePojo.setCustom1("");
        if (matSubstitutePojo.getCustom2() == null) matSubstitutePojo.setCustom2("");
        if (matSubstitutePojo.getCustom3() == null) matSubstitutePojo.setCustom3("");
        if (matSubstitutePojo.getCustom4() == null) matSubstitutePojo.setCustom4("");
        if (matSubstitutePojo.getCustom5() == null) matSubstitutePojo.setCustom5("");
        if (matSubstitutePojo.getCustom6() == null) matSubstitutePojo.setCustom6("");
        if (matSubstitutePojo.getCustom7() == null) matSubstitutePojo.setCustom7("");
        if (matSubstitutePojo.getCustom8() == null) matSubstitutePojo.setCustom8("");
        if (matSubstitutePojo.getCustom9() == null) matSubstitutePojo.setCustom9("");
        if (matSubstitutePojo.getCustom10() == null) matSubstitutePojo.setCustom10("");
        if (matSubstitutePojo.getTenantid() == null) matSubstitutePojo.setTenantid("");
        if (matSubstitutePojo.getRevision() == null) matSubstitutePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatSubstituteEntity matSubstituteEntity = new MatSubstituteEntity();
        BeanUtils.copyProperties(matSubstitutePojo, matSubstituteEntity);
        //设置id和新建日期
        matSubstituteEntity.setId(id);
        matSubstituteEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matSubstituteMapper.insert(matSubstituteEntity);
        //Item子表处理
        List<MatSubstituteitemPojo> lst = matSubstitutePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatSubstituteitemPojo itemPojo = this.matSubstituteitemService.clearNull(lst.get(i));
                MatSubstituteitemEntity matSubstituteitemEntity = new MatSubstituteitemEntity();
                BeanUtils.copyProperties(itemPojo, matSubstituteitemEntity);
                //设置id和Pid
                matSubstituteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matSubstituteitemEntity.setPid(id);
                matSubstituteitemEntity.setTenantid(matSubstitutePojo.getTenantid());
                matSubstituteitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matSubstituteitemMapper.insert(matSubstituteitemEntity);
            }
        }

        //Item子表处理
        List<MatSubstitutebomPojo> lstbom = matSubstitutePojo.getBom();
        if (lstbom != null) {
            //循环每个item子表
            for (int i = 0; i < lstbom.size(); i++) {
                //初始化item的NULL
                MatSubstitutebomPojo bomPojo = this.matSubstitutebomService.clearNull(lstbom.get(i));
                MatSubstitutebomEntity matSubstitutebomEntity = new MatSubstitutebomEntity();
                BeanUtils.copyProperties(bomPojo, matSubstitutebomEntity);
                //设置id和Pid
                matSubstitutebomEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matSubstitutebomEntity.setPid(id);
                matSubstitutebomEntity.setTenantid(matSubstitutePojo.getTenantid());
                matSubstitutebomEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matSubstitutebomMapper.insert(matSubstitutebomEntity);
            }
        }

        //返回Bill实例
        return this.getBillEntity(matSubstituteEntity.getId(), matSubstituteEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSubstitutePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatSubstitutePojo update(MatSubstitutePojo matSubstitutePojo) {
        //主表更改
        MatSubstituteEntity matSubstituteEntity = new MatSubstituteEntity();
        BeanUtils.copyProperties(matSubstitutePojo, matSubstituteEntity);
        this.matSubstituteMapper.update(matSubstituteEntity);
        if (matSubstitutePojo.getItem() != null) {
            //Item子表处理
            List<MatSubstituteitemPojo> lst = matSubstitutePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = matSubstituteMapper.getDelItemIds(matSubstitutePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.matSubstituteitemMapper.delete(lstDelIds.get(i), matSubstituteEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    MatSubstituteitemEntity matSubstituteitemEntity = new MatSubstituteitemEntity();
                    if (lst.get(i).getId() == "" || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        MatSubstituteitemPojo itemPojo = this.matSubstituteitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, matSubstituteitemEntity);
                        //设置id和Pid
                        matSubstituteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matSubstituteitemEntity.setPid(matSubstituteEntity.getId());  // 主表 id
                        matSubstituteitemEntity.setTenantid(matSubstitutePojo.getTenantid());   // 租户id
                        matSubstituteitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matSubstituteitemMapper.insert(matSubstituteitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), matSubstituteitemEntity);
                        matSubstituteitemEntity.setTenantid(matSubstitutePojo.getTenantid());
                        this.matSubstituteitemMapper.update(matSubstituteitemEntity);
                    }
                }
            }
        }

        if (matSubstitutePojo.getBom() != null) {
            //Item子表处理
            List<MatSubstitutebomPojo> lstbom = matSubstitutePojo.getBom();
            //获取被删除的Item
            List<String> lstDelbomIds = matSubstituteMapper.getDelBomIds(matSubstitutePojo);
            if (lstDelbomIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelbomIds.size(); i++) {
                    this.matSubstitutebomMapper.delete(lstDelbomIds.get(i), matSubstituteEntity.getTenantid());
                }
            }
            if (lstbom != null) {
                //循环每个item子表
                for (int i = 0; i < lstbom.size(); i++) {
                    MatSubstitutebomEntity matSubstitutebomEntity = new MatSubstitutebomEntity();
                    if (lstbom.get(i).getId() == "" || lstbom.get(i).getId() == null) {
                        //初始化item的NULL
                        MatSubstitutebomPojo bomPojo = this.matSubstitutebomService.clearNull(lstbom.get(i));
                        BeanUtils.copyProperties(bomPojo, matSubstitutebomEntity);
                        //设置id和Pid
                        matSubstitutebomEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matSubstitutebomEntity.setPid(matSubstituteEntity.getId());  // 主表 id
                        matSubstitutebomEntity.setTenantid(matSubstitutePojo.getTenantid());   // 租户id
                        matSubstitutebomEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matSubstitutebomMapper.insert(matSubstitutebomEntity);
                    } else {
                        BeanUtils.copyProperties(lstbom.get(i), matSubstitutebomEntity);
                        matSubstitutebomEntity.setTenantid(matSubstitutePojo.getTenantid());
                        this.matSubstitutebomMapper.update(matSubstitutebomEntity);
                    }
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(matSubstituteEntity.getId(), matSubstituteEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        MatSubstitutePojo matSubstitutePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatSubstituteitemPojo> lst = matSubstitutePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (MatSubstituteitemPojo matSubstituteitemPojo : lst) {
                this.matSubstituteitemMapper.delete(matSubstituteitemPojo.getId(), tid);
            }
        }

        //Item子表处理
        List<MatSubstitutebomPojo> lstbom = matSubstitutePojo.getBom();
        if (lstbom != null) {
            //循环每个删除item子表
            for (MatSubstitutebomPojo matSubstitutebomPojo : lstbom) {
                this.matSubstitutebomMapper.delete(matSubstitutebomPojo.getId(), tid);
            }
        }

        this.matSubstituteMapper.delete(key, tid);
        return matSubstitutePojo.getRefno();

    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSubstitutePojo getEntityByGoodsid(String key, String tid) {
        return this.matSubstituteMapper.getEntityByGoodsid(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSubstitutePojo getBillEntityByGoodsid(String key, String tid) {
        try {
            //读取主表
            MatSubstitutePojo matSubstitutePojo = this.matSubstituteMapper.getEntityByGoodsid(key, tid);
            //读取子表
            matSubstitutePojo.setItem(matSubstituteitemMapper.getList(matSubstitutePojo.getId(), matSubstitutePojo.getTenantid()));
            //读取Bom子表
            matSubstitutePojo.setBom(matSubstitutebomMapper.getList(matSubstitutePojo.getId(), matSubstitutePojo.getTenantid()));
            return matSubstitutePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public MatSubstitutePojo chkSubstitute(String mkey, String skey, String bomid, String tid) {
        return this.matSubstituteMapper.chkSubstitute(mkey, skey, bomid, tid);
    }
}
