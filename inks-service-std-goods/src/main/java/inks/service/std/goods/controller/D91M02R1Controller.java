package inks.service.std.goods.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo;
import inks.service.std.goods.service.MatBomService;
import inks.service.std.goods.service.MatBomorderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单Bom(Mat_BomOrder)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-11 14:40:09
 */
@RestController
@RequestMapping("D91M02R1")
@Api(tags = "D91M02R1:BOM类混合报表")
public class D91M02R1Controller {
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 服务对象
     */
    @Resource
    private MatBomService matBomService;

    /**
     * 服务对象
     */
    @Resource
    private MatBomorderService matBomorderService;

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按物料id查询全BOM", notes = "按物料id查询全BOM", produces = "application/json")
    @RequestMapping(value = "/getListByItemGoodsid", method = RequestMethod.GET)
    public R<List<MatBomorderitemdetailPojo>> getListByItemGoodsid(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<MatBomorderitemdetailPojo> lst = this.matBomorderService.getListByItemGoodsid(key, loginUser.getTenantid());
            List<MatBomorderitemdetailPojo> lststd = this.matBomService.getListByItemGoodsid(key, loginUser.getTenantid());
            lst.addAll(lststd);
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
