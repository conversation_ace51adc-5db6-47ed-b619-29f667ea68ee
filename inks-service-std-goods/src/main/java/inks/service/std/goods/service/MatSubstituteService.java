package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSubstitutePojo;
import inks.service.std.goods.domain.pojo.MatSubstituteitemdetailPojo;

/**
 * 替代品管理(MatSubstitute)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-06 11:17:34
 */
public interface MatSubstituteService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstitutePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSubstituteitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstitutePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSubstitutePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSubstitutePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSubstitutePojo 实例对象
     * @return 实例对象
     */
    MatSubstitutePojo insert(MatSubstitutePojo matSubstitutePojo);

    /**
     * 修改数据
     *
     * @param matSubstitutepojo 实例对象
     * @return 实例对象
     */
    MatSubstitutePojo update(MatSubstitutePojo matSubstitutepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstitutePojo getEntityByGoodsid(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstitutePojo getBillEntityByGoodsid(String key, String tid);

    MatSubstitutePojo chkSubstitute(String mkey, String skey, String bomid, String tid);
}
