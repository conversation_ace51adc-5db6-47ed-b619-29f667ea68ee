package inks.service.std.goods.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.goods.domain.pojo.MatCamprojectPojo;
import inks.service.std.goods.domain.MatCamprojectEntity;
import inks.service.std.goods.mapper.MatCamprojectMapper;
import inks.service.std.goods.service.MatCamprojectService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工程事务(MatCamproject)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-21 09:17:15
 */
@Service("matCamprojectService")
public class MatCamprojectServiceImpl implements MatCamprojectService {
    @Resource
    private MatCamprojectMapper matCamprojectMapper;

    @Override
    public MatCamprojectPojo getEntity(String key, String tid) {
        return this.matCamprojectMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<MatCamprojectPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCamprojectPojo> lst = matCamprojectMapper.getPageList(queryParam);
            PageInfo<MatCamprojectPojo> pageInfo = new PageInfo<MatCamprojectPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public MatCamprojectPojo insert(MatCamprojectPojo matCamprojectPojo) {
        //初始化NULL字段
        cleanNull(matCamprojectPojo);
        MatCamprojectEntity matCamprojectEntity = new MatCamprojectEntity(); 
        BeanUtils.copyProperties(matCamprojectPojo,matCamprojectEntity);
          //生成雪花id
          matCamprojectEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matCamprojectEntity.setRevision(1);  //乐观锁
          this.matCamprojectMapper.insert(matCamprojectEntity);
          // citeitemid是销售订单子表id 同步销售订单Item表的EngStateText字段 Mat_CamProject创建时，/cancelHandler“导入”，/startProject/cancelFinish"处理"，/finishProject"完成"
        String citeitemid = matCamprojectPojo.getCiteitemid();
        if (StringUtils.isNotBlank(citeitemid)) {
            matCamprojectMapper.syncMachItemEngStateText(citeitemid, "导入", matCamprojectEntity.getTenantid());
        }
        return this.getEntity(matCamprojectEntity.getId(),matCamprojectEntity.getTenantid());
    }


    @Override
    public MatCamprojectPojo update(MatCamprojectPojo matCamprojectPojo) {
        MatCamprojectEntity matCamprojectEntity = new MatCamprojectEntity(); 
        BeanUtils.copyProperties(matCamprojectPojo,matCamprojectEntity);
        this.matCamprojectMapper.update(matCamprojectEntity);
        return this.getEntity(matCamprojectEntity.getId(),matCamprojectEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        MatCamprojectPojo matCamprojectPojo = this.getEntity(key,tid);
        // citeitemid是销售订单子表id 同步销售订单Item表的EngStateText字段
        String citeitemid = matCamprojectPojo.getCiteitemid();
        if (StringUtils.isNotBlank(citeitemid)) {
            matCamprojectMapper.syncMachItemEngStateText(citeitemid, "删除", tid);
        }
        return this.matCamprojectMapper.delete(key,tid) ;
    }
    
    @Override
    @Transactional
    public MatCamprojectPojo approval(MatCamprojectPojo matCamprojectPojo) {
        //主表更改
        MatCamprojectEntity matCamprojectEntity = new MatCamprojectEntity();
        BeanUtils.copyProperties(matCamprojectPojo,matCamprojectEntity);
        this.matCamprojectMapper.approval(matCamprojectEntity);
        //返回Bill实例
        return this.getEntity(matCamprojectEntity.getId(),matCamprojectEntity.getTenantid());
    }

    private static void cleanNull(MatCamprojectPojo matCamprojectPojo) {
        if(matCamprojectPojo.getRefno()==null) matCamprojectPojo.setRefno("");
        if(matCamprojectPojo.getBilltype()==null) matCamprojectPojo.setBilltype("");
        if(matCamprojectPojo.getBilltitle()==null) matCamprojectPojo.setBilltitle("");
        if(matCamprojectPojo.getBilldate()==null) matCamprojectPojo.setBilldate(new Date());
        if(matCamprojectPojo.getJobtype()==null) matCamprojectPojo.setJobtype("");
        if(matCamprojectPojo.getGoodsid()==null) matCamprojectPojo.setGoodsid("");
        if(matCamprojectPojo.getGoodsuid()==null) matCamprojectPojo.setGoodsuid("");
        if(matCamprojectPojo.getGoodsname()==null) matCamprojectPojo.setGoodsname("");
        if(matCamprojectPojo.getGoodsspec()==null) matCamprojectPojo.setGoodsspec("");
        if(matCamprojectPojo.getAccepter()==null) matCamprojectPojo.setAccepter("");
        if(matCamprojectPojo.getAimdate()==null) matCamprojectPojo.setAimdate(new Date());
        // Startdate和Enddate默认NULL
//        if(matCamprojectPojo.getStartdate()==null) matCamprojectPojo.setStartdate(new Date());
//        if(matCamprojectPojo.getEnddate()==null) matCamprojectPojo.setEnddate(new Date());
        if(matCamprojectPojo.getHandler()==null) matCamprojectPojo.setHandler("");
        if(matCamprojectPojo.getCiteuid()==null) matCamprojectPojo.setCiteuid("");
        if(matCamprojectPojo.getCiteitemid()==null) matCamprojectPojo.setCiteitemid("");
        if(matCamprojectPojo.getStatetext()==null) matCamprojectPojo.setStatetext("");
        if(matCamprojectPojo.getStatedate()==null) matCamprojectPojo.setStatedate(new Date());
        if(matCamprojectPojo.getEditioninfo()==null) matCamprojectPojo.setEditioninfo("");
        if(matCamprojectPojo.getRemark()==null) matCamprojectPojo.setRemark("");
        if(matCamprojectPojo.getCreateby()==null) matCamprojectPojo.setCreateby("");
        if(matCamprojectPojo.getCreatebyid()==null) matCamprojectPojo.setCreatebyid("");
        if(matCamprojectPojo.getCreatedate()==null) matCamprojectPojo.setCreatedate(new Date());
        if(matCamprojectPojo.getLister()==null) matCamprojectPojo.setLister("");
        if(matCamprojectPojo.getListerid()==null) matCamprojectPojo.setListerid("");
        if(matCamprojectPojo.getModifydate()==null) matCamprojectPojo.setModifydate(new Date());
        if(matCamprojectPojo.getAssessor()==null) matCamprojectPojo.setAssessor("");
        if(matCamprojectPojo.getAssessorid()==null) matCamprojectPojo.setAssessorid("");
        if(matCamprojectPojo.getAssessdate()==null) matCamprojectPojo.setAssessdate(new Date());
        if(matCamprojectPojo.getCustom1()==null) matCamprojectPojo.setCustom1("");
        if(matCamprojectPojo.getCustom2()==null) matCamprojectPojo.setCustom2("");
        if(matCamprojectPojo.getCustom3()==null) matCamprojectPojo.setCustom3("");
        if(matCamprojectPojo.getCustom4()==null) matCamprojectPojo.setCustom4("");
        if(matCamprojectPojo.getCustom5()==null) matCamprojectPojo.setCustom5("");
        if(matCamprojectPojo.getCustom6()==null) matCamprojectPojo.setCustom6("");
        if(matCamprojectPojo.getCustom7()==null) matCamprojectPojo.setCustom7("");
        if(matCamprojectPojo.getCustom8()==null) matCamprojectPojo.setCustom8("");
        if(matCamprojectPojo.getCustom9()==null) matCamprojectPojo.setCustom9("");
        if(matCamprojectPojo.getCustom10()==null) matCamprojectPojo.setCustom10("");
        if(matCamprojectPojo.getTenantid()==null) matCamprojectPojo.setTenantid("");
        if(matCamprojectPojo.getTenantname()==null) matCamprojectPojo.setTenantname("");
        if(matCamprojectPojo.getRevision()==null) matCamprojectPojo.setRevision(0);
   }

}
