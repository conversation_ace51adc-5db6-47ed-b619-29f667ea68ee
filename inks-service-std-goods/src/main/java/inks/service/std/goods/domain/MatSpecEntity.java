package inks.service.std.goods.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 工艺流程(MatSpec)实体类
 *
 * <AUTHOR>
 * @since 2023-09-06 12:35:16
 */
public class MatSpecEntity implements Serializable {
    private static final long serialVersionUID = 298667846981885705L;
    // id
    private String id;
    // 编码
    private String refno;
    // 单据日期
    private Date billdate;
    // 单据类型
    private String billtype;
    // 单据标题
    private String billtitle;
    // 货品ID
    private String goodsid;
    // 货品编码
    private String itemcode;
    // 货品名称
    private String itemname;
    // 货品规格
    private String itemspec;
    // 货品单位
    private String itemunit;
    // PnlX
    private Double pnlx;
    // PnlY
    private Double pnly;
    // Pnl2Pcs
    private Integer pnl2pcs;
    // PnlBX
    private Double pnlbx;
    // PnlBY
    private Double pnlby;
    // PnlB2Pcs
    private Integer pnlb2pcs;
    // 经办人员
    private String operator;
    // 经办人员id
    private String operatorid;
    // 快码
    private String quickkey;
    // 属性Josn
    private String attributejson;
    // 摘要
    private String summary;
    // 有效
    private Integer enabledmark;
    // 版本号
    private String versionnum;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 审核员
    private String assessor;
    // 审核员id
    private String assessorid;
    // 审核日期
    private Date assessdate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // 编码

    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }
    // 单据日期

    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
    // 单据类型

    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
    // 单据标题

    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
    // 货品ID

    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
    // 货品编码

    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
    // 货品名称

    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
    // 货品规格

    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
    // 货品单位

    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
    // PnlX

    public Double getPnlx() {
        return pnlx;
    }

    public void setPnlx(Double pnlx) {
        this.pnlx = pnlx;
    }
    // PnlY

    public Double getPnly() {
        return pnly;
    }

    public void setPnly(Double pnly) {
        this.pnly = pnly;
    }
    // Pnl2Pcs

    public Integer getPnl2pcs() {
        return pnl2pcs;
    }

    public void setPnl2pcs(Integer pnl2pcs) {
        this.pnl2pcs = pnl2pcs;
    }
    // PnlBX

    public Double getPnlbx() {
        return pnlbx;
    }

    public void setPnlbx(Double pnlbx) {
        this.pnlbx = pnlbx;
    }
    // PnlBY

    public Double getPnlby() {
        return pnlby;
    }

    public void setPnlby(Double pnlby) {
        this.pnlby = pnlby;
    }
    // PnlB2Pcs

    public Integer getPnlb2pcs() {
        return pnlb2pcs;
    }

    public void setPnlb2pcs(Integer pnlb2pcs) {
        this.pnlb2pcs = pnlb2pcs;
    }
    // 经办人员

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    // 经办人员id

    public String getOperatorid() {
        return operatorid;
    }

    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
    // 快码

    public String getQuickkey() {
        return quickkey;
    }

    public void setQuickkey(String quickkey) {
        this.quickkey = quickkey;
    }
    // 属性Josn

    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
    // 摘要

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }
    // 有效

    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
    // 版本号

    public String getVersionnum() {
        return versionnum;
    }

    public void setVersionnum(String versionnum) {
        this.versionnum = versionnum;
    }
    // 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
    // 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
    // 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
    // 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 审核员

    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
    // 审核员id

    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
    // 审核日期

    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 自定义6

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
    // 自定义7

    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
    // 自定义8

    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
    // 自定义9

    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
    // 自定义10

    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
    // 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 租户名称

    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

