package inks.service.std.goods.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.goods.domain.pojo.MatEcnPojo;
import inks.service.std.goods.service.MatEcnService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 物料变更(Mat_Ecn)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-29 15:03:35
 */
public class MatEcnController {
    private final String moduleCode = "D91M04B1";
    /**
     * 服务对象
     */
    @Resource
    private MatEcnService matEcnService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取物料变更详细信息", notes = "获取物料变更详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Ecn.List")
    public R<MatEcnPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.matEcnService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Ecn.List")
    public R<PageInfo<MatEcnPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Ecn.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matEcnService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增物料变更", notes = "新增物料变更", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Ecn.Add")
    public R<MatEcnPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatEcnPojo matEcnPojo = JSONArray.parseObject(json, MatEcnPojo.class);
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Mat_Ecn", null, loginUser.getTenantid());
            matEcnPojo.setRefno(refno);
            matEcnPojo.setCreateby(loginUser.getRealname());   // 创建者
            matEcnPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matEcnPojo.setCreatedate(new Date());   // 创建时间
            matEcnPojo.setLister(loginUser.getRealname());   // 制表
            matEcnPojo.setListerid(loginUser.getUserid());    // 制表id  
            matEcnPojo.setModifydate(new Date());   //修改时间
            matEcnPojo.setTenantid(loginUser.getTenantid());   //租户id
            MatEcnPojo insert = this.matEcnService.insert(matEcnPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改物料变更", notes = "修改物料变更", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Ecn.Edit")
    public R<MatEcnPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatEcnPojo matEcnPojo = JSONArray.parseObject(json, MatEcnPojo.class);
            matEcnPojo.setLister(loginUser.getRealname());   // 制表
            matEcnPojo.setListerid(loginUser.getUserid());    // 制表id  
            matEcnPojo.setTenantid(loginUser.getTenantid());   //租户id
            matEcnPojo.setModifydate(new Date());   //修改时间
            matEcnPojo.setAssessor(""); // 审核员
            matEcnPojo.setAssessorid(""); // 审核员id
            matEcnPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matEcnService.update(matEcnPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除物料变更", notes = "删除物料变更", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Ecn.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            int delete = this.matEcnService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(delete);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核物料变更", notes = "审核物料变更", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Ecn.Approval")
    public R<MatEcnPojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            MatEcnPojo matEcnPojo = this.matEcnService.getEntity(key, loginUser.getTenantid());
            if (matEcnPojo.getAssessor().equals("")) {
                matEcnPojo.setAssessor(loginUser.getRealname()); //审核员
                matEcnPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                matEcnPojo.setAssessor(""); //审核员
                matEcnPojo.setAssessorid(""); //审核员
            }
            matEcnPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matEcnService.approval(matEcnPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Ecn.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatEcnPojo matEcnPojo = this.matEcnService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matEcnPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        map.put("imageurl", "http://oss.demo.inksyun.com/C01/18dafc88-c95a-475a-86ee-cacabdd28678.jpg");
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        List<String> lst = new ArrayList<>();
        lst.add("虚拟Item");
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

