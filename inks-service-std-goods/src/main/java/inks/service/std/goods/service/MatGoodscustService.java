package inks.service.std.goods.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatAttributePojo;
import inks.service.std.goods.domain.pojo.MatGoodscustPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 货品自定义(Mat_GoodsCust)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-15 16:25:27
 */
public interface MatGoodscustService {

    MatGoodscustPojo getEntity(String key,String tid);

    PageInfo<MatGoodscustPojo> getPageList(QueryParam queryParam);

    MatGoodscustPojo insert(MatGoodscustPojo matGoodscustPojo);

    MatGoodscustPojo update(MatGoodscustPojo matGoodscustpojo);

    int delete(String key,String tid);

    List<MatGoodscustPojo> getListByShow(String tenantid);
    List<MatGoodscustPojo> getList(String tenantid);
}
