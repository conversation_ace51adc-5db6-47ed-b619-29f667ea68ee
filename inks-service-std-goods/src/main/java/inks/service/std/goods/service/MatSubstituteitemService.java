package inks.service.std.goods.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.goods.domain.pojo.MatSubstituteitemPojo;

import java.util.List;

/**
 * 替代料项目(MatSubstituteitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-06 11:08:38
 */
public interface MatSubstituteitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSubstituteitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSubstituteitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatSubstituteitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matSubstituteitemPojo 实例对象
     * @return 实例对象
     */
    MatSubstituteitemPojo insert(MatSubstituteitemPojo matSubstituteitemPojo);

    /**
     * 修改数据
     *
     * @param matSubstituteitempojo 实例对象
     * @return 实例对象
     */
    MatSubstituteitemPojo update(MatSubstituteitemPojo matSubstituteitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matSubstituteitempojo 实例对象
     * @return 实例对象
     */
    MatSubstituteitemPojo clearNull(MatSubstituteitemPojo matSubstituteitempojo);
}
