<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSubstituteitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSubstituteitemPojo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,
            Mat_SubstituteItem.id,
            Mat_SubstituteItem.Pid,
            Mat_SubstituteItem.Goodsid,
            Mat_SubstituteItem.ItemCode,
            Mat_SubstituteItem.ItemName,
            Mat_SubstituteItem.ItemSpec,
            Mat_SubstituteItem.ItemUnit,
            Mat_SubstituteItem.MainQty,
            Mat_SubstituteItem.SubQty,
            Mat_SubstituteItem.SubRate,
            Mat_SubstituteItem.StratDate,
            Mat_SubstituteItem.EndDate,
            Mat_SubstituteItem.Priority,
            Mat_SubstituteItem.Closed,
            Mat_SubstituteItem.RowNum,
            Mat_SubstituteItem.Remark,
            Mat_SubstituteItem.Custom1,
            Mat_SubstituteItem.Custom2,
            Mat_SubstituteItem.Custom3,
            Mat_SubstituteItem.Custom4,
            Mat_SubstituteItem.Custom5,
            Mat_SubstituteItem.Custom6,
            Mat_SubstituteItem.Custom7,
            Mat_SubstituteItem.Custom8,
            Mat_SubstituteItem.Custom9,
            Mat_SubstituteItem.Custom10,
            Mat_SubstituteItem.Tenantid,
            Mat_SubstituteItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Mat_SubstituteItem ON Mat_SubstituteItem.Goodsid = Mat_Goods.id
        where Mat_SubstituteItem.id = #{key}
          and Mat_SubstituteItem.Tenantid = #{tid}
    </select>
    <sql id="selectMatSubstituteitemVo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,
            Mat_SubstituteItem.id,
            Mat_SubstituteItem.Pid,
            Mat_SubstituteItem.Goodsid,
            Mat_SubstituteItem.ItemCode,
            Mat_SubstituteItem.ItemName,
            Mat_SubstituteItem.ItemSpec,
            Mat_SubstituteItem.ItemUnit,
            Mat_SubstituteItem.MainQty,
            Mat_SubstituteItem.SubQty,
            Mat_SubstituteItem.SubRate,
            Mat_SubstituteItem.StratDate,
            Mat_SubstituteItem.EndDate,
            Mat_SubstituteItem.Priority,
            Mat_SubstituteItem.Closed,
            Mat_SubstituteItem.RowNum,
            Mat_SubstituteItem.Remark,
            Mat_SubstituteItem.Custom1,
            Mat_SubstituteItem.Custom2,
            Mat_SubstituteItem.Custom3,
            Mat_SubstituteItem.Custom4,
            Mat_SubstituteItem.Custom5,
            Mat_SubstituteItem.Custom6,
            Mat_SubstituteItem.Custom7,
            Mat_SubstituteItem.Custom8,
            Mat_SubstituteItem.Custom9,
            Mat_SubstituteItem.Custom10,
            Mat_SubstituteItem.Tenantid,
            Mat_SubstituteItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Mat_SubstituteItem ON Mat_SubstituteItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSubstituteitemPojo">
        <include refid="selectMatSubstituteitemVo"/>
        where 1 = 1 and Mat_SubstituteItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_SubstituteItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_SubstituteItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_SubstituteItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_SubstituteItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_SubstituteItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_SubstituteItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_SubstituteItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_SubstituteItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_SubstituteItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_SubstituteItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_SubstituteItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_SubstituteItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_SubstituteItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_SubstituteItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_SubstituteItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_SubstituteItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_SubstituteItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_SubstituteItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_SubstituteItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_SubstituteItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_SubstituteItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_SubstituteItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_SubstituteItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_SubstituteItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_SubstituteItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_SubstituteItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_SubstituteItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_SubstituteItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_SubstituteItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_SubstituteItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_SubstituteItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_SubstituteItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_SubstituteItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_SubstituteItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_SubstituteItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatSubstituteitemPojo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,
            Mat_SubstituteItem.id,
            Mat_SubstituteItem.Pid,
            Mat_SubstituteItem.Goodsid,
            Mat_SubstituteItem.ItemCode,
            Mat_SubstituteItem.ItemName,
            Mat_SubstituteItem.ItemSpec,
            Mat_SubstituteItem.ItemUnit,
            Mat_SubstituteItem.MainQty,
            Mat_SubstituteItem.SubQty,
            Mat_SubstituteItem.SubRate,
            Mat_SubstituteItem.StratDate,
            Mat_SubstituteItem.EndDate,
            Mat_SubstituteItem.Priority,
            Mat_SubstituteItem.Closed,
            Mat_SubstituteItem.RowNum,
            Mat_SubstituteItem.Remark,
            Mat_SubstituteItem.Custom1,
            Mat_SubstituteItem.Custom2,
            Mat_SubstituteItem.Custom3,
            Mat_SubstituteItem.Custom4,
            Mat_SubstituteItem.Custom5,
            Mat_SubstituteItem.Custom6,
            Mat_SubstituteItem.Custom7,
            Mat_SubstituteItem.Custom8,
            Mat_SubstituteItem.Custom9,
            Mat_SubstituteItem.Custom10,
            Mat_SubstituteItem.Tenantid,
            Mat_SubstituteItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Mat_SubstituteItem ON Mat_SubstituteItem.Goodsid = Mat_Goods.id
        where Mat_SubstituteItem.Pid = #{Pid}
          and Mat_SubstituteItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_SubstituteItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, MainQty, SubQty,
                                       SubRate, StratDate, EndDate, Priority, Closed, RowNum, Remark, Custom1, Custom2,
                                       Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                       Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{mainqty}, #{subqty},
                #{subrate}, #{stratdate}, #{enddate}, #{priority}, #{closed}, #{rownum}, #{remark}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SubstituteItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="mainqty != null">
                MainQty = #{mainqty},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="subrate != null">
                SubRate = #{subrate},
            </if>
            <if test="stratdate != null">
                StratDate = #{stratdate},
            </if>
            <if test="enddate != null">
                EndDate = #{enddate},
            </if>
            <if test="priority != null">
                Priority = #{priority},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_SubstituteItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

