<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatGoodscustMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatGoodscustPojo">
        <include refid="selectMatGoodscustVo"/>
        where Mat_GoodsCust.id = #{key}
          and Mat_GoodsCust.Tenantid = #{tid}
    </select>
    <sql id="selectMatGoodscustVo">
        select id,
               CustGroupid,
               CustKey,
               CustName,
               ValueType,
               DefValue,
               ValueJson,
               ShowMark,
               EnabledMark,
               Remark,
               RowNum,
               CreateBy,
               C<PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               <PERSON>difyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Tenant<PERSON><PERSON>,
               Revision
        from Mat_GoodsCust
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatGoodscustPojo">
        <include refid="selectMatGoodscustVo"/>
        where 1 = 1 and Mat_GoodsCust.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Mat_GoodsCust.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.custgroupid != null">
            and Mat_GoodsCust.CustGroupid like concat('%', #{SearchPojo.custgroupid}, '%')
        </if>
        <if test="SearchPojo.custkey != null">
            and Mat_GoodsCust.CustKey like concat('%',
                #{SearchPojo.custkey}, '%')
        </if>
        <if test="SearchPojo.custname != null">
            and Mat_GoodsCust.CustName like concat('%',
                #{SearchPojo.custname}, '%')
        </if>
        <if test="SearchPojo.valuetype != null">
            and Mat_GoodsCust.ValueType like concat('%',
                #{SearchPojo.valuetype}, '%')
        </if>
        <if test="SearchPojo.defvalue != null">
            and Mat_GoodsCust.DefValue like concat('%',
                #{SearchPojo.defvalue}, '%')
        </if>
        <if test="SearchPojo.valuejson != null">
            and Mat_GoodsCust.ValueJson like concat('%',
                #{SearchPojo.valuejson}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Mat_GoodsCust.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Mat_GoodsCust.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Mat_GoodsCust.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Mat_GoodsCust.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Mat_GoodsCust.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Mat_GoodsCust.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Mat_GoodsCust.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Mat_GoodsCust.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Mat_GoodsCust.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Mat_GoodsCust.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Mat_GoodsCust.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Mat_GoodsCust.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Mat_GoodsCust.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Mat_GoodsCust.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Mat_GoodsCust.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Mat_GoodsCust.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.custgroupid != null">
                or Mat_GoodsCust.CustGroupid like concat('%', #{SearchPojo.custgroupid}, '%')
            </if>
            <if test="SearchPojo.custkey != null">
                or Mat_GoodsCust.CustKey like concat('%',
                    #{SearchPojo.custkey}, '%')
            </if>
            <if test="SearchPojo.custname != null">
                or Mat_GoodsCust.CustName like concat('%',
                    #{SearchPojo.custname}, '%')
            </if>
            <if test="SearchPojo.valuetype != null">
                or Mat_GoodsCust.ValueType like concat('%',
                    #{SearchPojo.valuetype}, '%')
            </if>
            <if test="SearchPojo.defvalue != null">
                or Mat_GoodsCust.DefValue like concat('%',
                    #{SearchPojo.defvalue}, '%')
            </if>
            <if test="SearchPojo.valuejson != null">
                or Mat_GoodsCust.ValueJson like concat('%',
                    #{SearchPojo.valuejson}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Mat_GoodsCust.Remark like concat('%',
                    #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Mat_GoodsCust.CreateBy like concat('%',
                    #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Mat_GoodsCust.CreateByid like concat('%',
                    #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Mat_GoodsCust.Lister like concat('%',
                    #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Mat_GoodsCust.Listerid like concat('%',
                    #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Mat_GoodsCust.Custom1 like concat('%',
                    #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Mat_GoodsCust.Custom2 like concat('%',
                    #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Mat_GoodsCust.Custom3 like concat('%',
                    #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Mat_GoodsCust.Custom4 like concat('%',
                    #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Mat_GoodsCust.Custom5 like concat('%',
                    #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Mat_GoodsCust.Custom6 like concat('%',
                    #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Mat_GoodsCust.Custom7 like concat('%',
                    #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Mat_GoodsCust.Custom8 like concat('%',
                    #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Mat_GoodsCust.Custom9 like concat('%',
                    #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Mat_GoodsCust.Custom10 like concat('%',
                    #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Mat_GoodsCust.TenantName like concat('%',
                    #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_GoodsCust(id, CustGroupid, CustKey, CustName, ValueType, DefValue, ValueJson, ShowMark,
                                  EnabledMark, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                                  ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                  Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{custgroupid}, #{custkey}, #{custname}, #{valuetype}, #{defvalue}, #{valuejson}, #{showmark},
                #{enabledmark}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_GoodsCust
        <set>
            <if test="custgroupid != null">
                CustGroupid =#{custgroupid},
            </if>
            <if test="custkey != null">
                CustKey =#{custkey},
            </if>
            <if test="custname != null">
                CustName =#{custname},
            </if>
            <if test="valuetype != null">
                ValueType =#{valuetype},
            </if>
            <if test="defvalue != null">
                DefValue =#{defvalue},
            </if>
            <if test="valuejson != null">
                ValueJson =#{valuejson},
            </if>
            <if test="showmark != null">
                ShowMark =#{showmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Mat_GoodsCust where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getListByShow" resultType="inks.service.std.goods.domain.pojo.MatGoodscustPojo">
        <include refid="selectMatGoodscustVo"/>
        where Mat_GoodsCust.ShowMark = 1
          and Mat_GoodsCust.EnabledMark = 1
          and Mat_GoodsCust.Tenantid = #{tid}
    </select>

    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatGoodscustPojo">
        <include refid="selectMatGoodscustVo"/>
        where Mat_GoodsCust.EnabledMark = 1
        and Mat_GoodsCust.Tenantid = #{tid}
    </select>
</mapper>

