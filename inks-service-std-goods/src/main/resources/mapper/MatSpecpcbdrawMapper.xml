<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSpecpcbdrawMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbdrawPojo">
        select id,
               Pid,
               DrawType,
               DrawTitle,
               DrawImage,
               DrawJson,
               DrawUrl,
               InsideMark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_SpecPcbDraw
        where Mat_SpecPcbDraw.id = #{key}
          and Mat_SpecPcbDraw.Tenantid = #{tid}
    </select>
    <sql id="selectMatSpecpcbdrawVo">
        select id,
               Pid,
               DrawType,
               DrawTitle,
               DrawImage,
               DrawJson,
               DrawUrl,
               InsideMark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_SpecPcbDraw
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSpecpcbdrawPojo">
        <include refid="selectMatSpecpcbdrawVo"/>
        where 1 = 1 and Mat_SpecPcbDraw.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_SpecPcbDraw.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_SpecPcbDraw.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.drawtype != null and SearchPojo.drawtype != ''">
            and Mat_SpecPcbDraw.drawtype like concat('%', #{SearchPojo.drawtype}, '%')
        </if>
        <if test="SearchPojo.drawtitle != null and SearchPojo.drawtitle != ''">
            and Mat_SpecPcbDraw.drawtitle like concat('%', #{SearchPojo.drawtitle}, '%')
        </if>
        <if test="SearchPojo.drawimage != null and SearchPojo.drawimage != ''">
            and Mat_SpecPcbDraw.drawimage like concat('%', #{SearchPojo.drawimage}, '%')
        </if>
        <if test="SearchPojo.drawjson != null and SearchPojo.drawjson != ''">
            and Mat_SpecPcbDraw.drawjson like concat('%', #{SearchPojo.drawjson}, '%')
        </if>
        <if test="SearchPojo.drawurl != null and SearchPojo.drawurl != ''">
            and Mat_SpecPcbDraw.drawurl like concat('%', #{SearchPojo.drawurl}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_SpecPcbDraw.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_SpecPcbDraw.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_SpecPcbDraw.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_SpecPcbDraw.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_SpecPcbDraw.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_SpecPcbDraw.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_SpecPcbDraw.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_SpecPcbDraw.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_SpecPcbDraw.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_SpecPcbDraw.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_SpecPcbDraw.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_SpecPcbDraw.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_SpecPcbDraw.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_SpecPcbDraw.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Mat_SpecPcbDraw.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_SpecPcbDraw.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.drawtype != null and SearchPojo.drawtype != ''">
                or Mat_SpecPcbDraw.DrawType like concat('%', #{SearchPojo.drawtype}, '%')
            </if>
            <if test="SearchPojo.drawtitle != null and SearchPojo.drawtitle != ''">
                or Mat_SpecPcbDraw.DrawTitle like concat('%', #{SearchPojo.drawtitle}, '%')
            </if>
            <if test="SearchPojo.drawimage != null and SearchPojo.drawimage != ''">
                or Mat_SpecPcbDraw.DrawImage like concat('%', #{SearchPojo.drawimage}, '%')
            </if>
            <if test="SearchPojo.drawjson != null and SearchPojo.drawjson != ''">
                or Mat_SpecPcbDraw.DrawJson like concat('%', #{SearchPojo.drawjson}, '%')
            </if>
            <if test="SearchPojo.drawurl != null and SearchPojo.drawurl != ''">
                or Mat_SpecPcbDraw.DrawUrl like concat('%', #{SearchPojo.drawurl}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_SpecPcbDraw.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_SpecPcbDraw.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_SpecPcbDraw.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_SpecPcbDraw.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_SpecPcbDraw.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_SpecPcbDraw.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_SpecPcbDraw.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_SpecPcbDraw.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_SpecPcbDraw.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_SpecPcbDraw.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_SpecPcbDraw.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_SpecPcbDraw.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_SpecPcbDraw.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_SpecPcbDraw.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Mat_SpecPcbDraw.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbdrawPojo">
        select id,
               Pid,
               DrawType,
               DrawTitle,
               DrawImage,
               DrawJson,
               DrawUrl,
               InsideMark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_SpecPcbDraw
        where Mat_SpecPcbDraw.Pid = #{Pid}
          and Mat_SpecPcbDraw.Tenantid = #{tid}
        order by RowNum
    </select>
    <!--查询List-->
    <select id="getListVo" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbdrawVo">
        select id,
               Pid,
               DrawType,
               DrawTitle,
               DrawImage,
               DrawJson,
               DrawUrl,
               InsideMark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate
        from Mat_SpecPcbDraw
        where Mat_SpecPcbDraw.Pid = #{Pid}
          and Mat_SpecPcbDraw.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_SpecPcbDraw(id, Pid, DrawType, DrawTitle, DrawImage, DrawJson, DrawUrl, InsideMark, RowNum,
                                    CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
                                    Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                    TenantName, Revision)
        values (#{id}, #{pid}, #{drawtype}, #{drawtitle}, #{drawimage}, #{drawjson}, #{drawurl}, #{insidemark},
                #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SpecPcbDraw
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="drawtype != null ">
                DrawType = #{drawtype},
            </if>
            <if test="drawtitle != null ">
                DrawTitle = #{drawtitle},
            </if>
            <if test="drawimage != null ">
                DrawImage = #{drawimage},
            </if>
            <if test="drawjson != null ">
                DrawJson = #{drawjson},
            </if>
            <if test="drawurl != null ">
                DrawUrl = #{drawurl},
            </if>
            <if test="insidemark != null">
                InsideMark = #{insidemark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="createby != null ">
                CreateBy = #{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid = #{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="listerid != null ">
                Listerid = #{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_SpecPcbDraw
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

