<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatCamprojectMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatCamprojectPojo">
        <include refid="selectMatCamprojectVo"/>
        where Mat_CamProject.id = #{key} and Mat_CamProject.Tenantid=#{tid}
    </select>
    <sql id="selectMatCamprojectVo">
         select
id, RefNo, BillType, BillTitle, BillDate, JobType, Goodsid, GoodsUid, GoodsName, GoodsSpec, Accepter, AimDate, StartDate, <PERSON>Date, <PERSON><PERSON>, CiteUid, CiteItemid, StateText, StateDate, EditionInfo, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Mat_CamProject
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.goods.domain.pojo.MatCamprojectPojo">
        <include refid="selectMatCamprojectVo"/>
         where 1 = 1 and Mat_CamProject.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Mat_CamProject.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Mat_CamProject.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Mat_CamProject.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Mat_CamProject.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.jobtype != null ">
   and Mat_CamProject.JobType like concat('%', #{SearchPojo.jobtype}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   and Mat_CamProject.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.goodsuid != null ">
   and Mat_CamProject.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
</if>
<if test="SearchPojo.goodsname != null ">
   and Mat_CamProject.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
</if>
<if test="SearchPojo.goodsspec != null ">
   and Mat_CamProject.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
</if>
<if test="SearchPojo.accepter != null ">
   and Mat_CamProject.Accepter like concat('%', #{SearchPojo.accepter}, '%')
</if>
<if test="SearchPojo.handler != null ">
   and Mat_CamProject.Handler like concat('%', #{SearchPojo.handler}, '%')
</if>
<if test="SearchPojo.citeuid != null ">
   and Mat_CamProject.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null ">
   and Mat_CamProject.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.statetext != null ">
   and Mat_CamProject.StateText like concat('%', #{SearchPojo.statetext}, '%')
</if>
<if test="SearchPojo.editioninfo != null ">
   and Mat_CamProject.EditionInfo like concat('%', #{SearchPojo.editioninfo}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Mat_CamProject.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Mat_CamProject.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Mat_CamProject.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Mat_CamProject.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Mat_CamProject.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Mat_CamProject.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Mat_CamProject.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Mat_CamProject.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Mat_CamProject.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Mat_CamProject.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Mat_CamProject.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Mat_CamProject.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Mat_CamProject.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Mat_CamProject.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Mat_CamProject.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Mat_CamProject.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Mat_CamProject.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Mat_CamProject.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Mat_CamProject.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Mat_CamProject.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Mat_CamProject.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.jobtype != null ">
   or Mat_CamProject.JobType like concat('%', #{SearchPojo.jobtype}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   or Mat_CamProject.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.goodsuid != null ">
   or Mat_CamProject.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
</if>
<if test="SearchPojo.goodsname != null ">
   or Mat_CamProject.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
</if>
<if test="SearchPojo.goodsspec != null ">
   or Mat_CamProject.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
</if>
<if test="SearchPojo.accepter != null ">
   or Mat_CamProject.Accepter like concat('%', #{SearchPojo.accepter}, '%')
</if>
<if test="SearchPojo.handler != null ">
   or Mat_CamProject.Handler like concat('%', #{SearchPojo.handler}, '%')
</if>
<if test="SearchPojo.citeuid != null ">
   or Mat_CamProject.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null ">
   or Mat_CamProject.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.statetext != null ">
   or Mat_CamProject.StateText like concat('%', #{SearchPojo.statetext}, '%')
</if>
<if test="SearchPojo.editioninfo != null ">
   or Mat_CamProject.EditionInfo like concat('%', #{SearchPojo.editioninfo}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Mat_CamProject.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Mat_CamProject.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Mat_CamProject.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Mat_CamProject.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Mat_CamProject.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Mat_CamProject.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Mat_CamProject.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Mat_CamProject.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Mat_CamProject.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Mat_CamProject.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Mat_CamProject.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Mat_CamProject.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Mat_CamProject.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Mat_CamProject.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Mat_CamProject.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Mat_CamProject.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Mat_CamProject.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Mat_CamProject.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Mat_CamProject(id, RefNo, BillType, BillTitle, BillDate, JobType, Goodsid, GoodsUid, GoodsName, GoodsSpec, Accepter, AimDate, StartDate, EndDate, Handler, CiteUid, CiteItemid, StateText, StateDate, EditionInfo, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{jobtype}, #{goodsid}, #{goodsuid}, #{goodsname}, #{goodsspec}, #{accepter}, #{aimdate}, #{startdate}, #{enddate}, #{handler}, #{citeuid}, #{citeitemid}, #{statetext}, #{statedate}, #{editioninfo}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_CamProject
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="jobtype != null ">
                JobType =#{jobtype},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="goodsuid != null ">
                GoodsUid =#{goodsuid},
            </if>
            <if test="goodsname != null ">
                GoodsName =#{goodsname},
            </if>
            <if test="goodsspec != null ">
                GoodsSpec =#{goodsspec},
            </if>
            <if test="accepter != null ">
                Accepter =#{accepter},
            </if>
            <if test="aimdate != null">
                AimDate =#{aimdate},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="handler != null ">
                Handler =#{handler},
            </if>
            <if test="citeuid != null ">
                CiteUid =#{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid =#{citeitemid},
            </if>
            <if test="statetext != null ">
                StateText =#{statetext},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="editioninfo != null ">
                EditionInfo =#{editioninfo},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Mat_CamProject where id = #{key} and Tenantid=#{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_CamProject SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <update id="syncMachItemEngStateText">
        update Bus_MachiningItem SET
        EngStateText = #{engstatetext}
        where id = #{citeitemid} and Tenantid = #{tid}
    </update>

    <update id="setNullStartDateEndDateHandler">
        update Mat_CamProject
        SET Handler   = null,
            StartDate =null,
            EndDate   = null
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <update id="setNullEndDate">
        update Mat_CamProject
        SET EndDate =null
        where id = #{key} and Tenantid = #{tid}
    </update>
</mapper>

