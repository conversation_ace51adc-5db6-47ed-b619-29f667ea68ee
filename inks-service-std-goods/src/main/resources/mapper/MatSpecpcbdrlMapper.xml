<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSpecpcbdrlMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbdrlPojo">
        select id,
               Pid,
               Symbol,
               HoleSize,
               Tolerance,
               DrillSize,
               PcsTotal,
               SetTotal,
               PnlTotal,
               PthMark,
               RowNum,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision,
               TenantName
        from Mat_SpecPcbDrl
        where Mat_SpecPcbDrl.id = #{key}
          and Mat_SpecPcbDrl.Tenantid = #{tid}
    </select>
    <sql id="selectMatSpecpcbdrlVo">
        select id,
               Pid,
               Symbol,
               HoleSize,
               Tolerance,
               DrillSize,
               PcsTotal,
               SetTotal,
               PnlTotal,
               PthMark,
               RowNum,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision,
               TenantName
        from Mat_SpecPcbDrl
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSpecpcbdrlPojo">
        <include refid="selectMatSpecpcbdrlVo"/>
        where 1 = 1 and Mat_SpecPcbDrl.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_SpecPcbDrl.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_SpecPcbDrl.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.symbol != null and SearchPojo.symbol != ''">
            and Mat_SpecPcbDrl.symbol like concat('%', #{SearchPojo.symbol}, '%')
        </if>
        <if test="SearchPojo.tolerance != null and SearchPojo.tolerance != ''">
            and Mat_SpecPcbDrl.tolerance like concat('%', #{SearchPojo.tolerance}, '%')
        </if>
        <if test="SearchPojo.pthmark != null and SearchPojo.pthmark != ''">
            and Mat_SpecPcbDrl.pthmark like concat('%', #{SearchPojo.pthmark}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_SpecPcbDrl.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_SpecPcbDrl.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_SpecPcbDrl.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_SpecPcbDrl.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_SpecPcbDrl.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_SpecPcbDrl.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_SpecPcbDrl.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_SpecPcbDrl.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_SpecPcbDrl.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_SpecPcbDrl.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_SpecPcbDrl.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Mat_SpecPcbDrl.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_SpecPcbDrl.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.symbol != null and SearchPojo.symbol != ''">
                or Mat_SpecPcbDrl.Symbol like concat('%', #{SearchPojo.symbol}, '%')
            </if>
            <if test="SearchPojo.tolerance != null and SearchPojo.tolerance != ''">
                or Mat_SpecPcbDrl.Tolerance like concat('%', #{SearchPojo.tolerance}, '%')
            </if>
            <if test="SearchPojo.pthmark != null and SearchPojo.pthmark != ''">
                or Mat_SpecPcbDrl.PthMark like concat('%', #{SearchPojo.pthmark}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_SpecPcbDrl.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_SpecPcbDrl.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_SpecPcbDrl.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_SpecPcbDrl.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_SpecPcbDrl.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_SpecPcbDrl.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_SpecPcbDrl.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_SpecPcbDrl.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_SpecPcbDrl.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_SpecPcbDrl.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_SpecPcbDrl.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Mat_SpecPcbDrl.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbdrlPojo">
        select id,
               Pid,
               Symbol,
               HoleSize,
               Tolerance,
               DrillSize,
               PcsTotal,
               SetTotal,
               PnlTotal,
               PthMark,
               RowNum,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision,
               TenantName
        from Mat_SpecPcbDrl
        where Mat_SpecPcbDrl.Pid = #{Pid}
          and Mat_SpecPcbDrl.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--查询List-->
    <select id="getListVo" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbdrlVo">
        select id,
               Pid,
               Symbol,
               HoleSize,
               Tolerance,
               DrillSize,
               PcsTotal,
               SetTotal,
               PnlTotal,
               PthMark,
               Remark
        from Mat_SpecPcbDrl
        where Mat_SpecPcbDrl.Pid = #{Pid}
          and Mat_SpecPcbDrl.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_SpecPcbDrl(id, Pid, Symbol, HoleSize, Tolerance, DrillSize, PcsTotal, SetTotal, PnlTotal,
                                   PthMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
                                   Custom7, Custom8, Custom9, Custom10, Tenantid, Revision, TenantName)
        values (#{id}, #{pid}, #{symbol}, #{holesize}, #{tolerance}, #{drillsize}, #{pcstotal}, #{settotal},
                #{pnltotal}, #{pthmark}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision},
                #{tenantname})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SpecPcbDrl
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="symbol != null ">
                Symbol = #{symbol},
            </if>
            <if test="holesize != null">
                HoleSize = #{holesize},
            </if>
            <if test="tolerance != null ">
                Tolerance = #{tolerance},
            </if>
            <if test="drillsize != null">
                DrillSize = #{drillsize},
            </if>
            <if test="pcstotal != null">
                PcsTotal = #{pcstotal},
            </if>
            <if test="settotal != null">
                SetTotal = #{settotal},
            </if>
            <if test="pnltotal != null">
                PnlTotal = #{pnltotal},
            </if>
            <if test="pthmark != null ">
                PthMark = #{pthmark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_SpecPcbDrl
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

