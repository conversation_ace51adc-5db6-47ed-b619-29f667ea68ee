<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatGoodsunitMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatGoodsunitPojo">
        <include refid="selectMatGoodsunitVo"/>
        where Mat_GoodsUnit.id = #{key} and Mat_GoodsUnit.Tenantid=#{tid}
    </select>
    <sql id="selectMatGoodsunitVo">
         select
id, UnitName, EnabledMark, DecNum, RoundingType, UnitType, BackColorArgb, ForeColorArgb, RowNum, Remark, CreateBy, <PERSON>reate<PERSON>yid, <PERSON>reate<PERSON>ate, <PERSON>er, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Mat_GoodsUnit
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.goods.domain.pojo.MatGoodsunitPojo">
        <include refid="selectMatGoodsunitVo"/>
         where 1 = 1 and Mat_GoodsUnit.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Mat_GoodsUnit.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.unitname != null ">
   and Mat_GoodsUnit.UnitName like concat('%', #{SearchPojo.unitname}, '%')
</if>
<if test="SearchPojo.backcolorargb != null ">
   and Mat_GoodsUnit.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
</if>
<if test="SearchPojo.forecolorargb != null ">
   and Mat_GoodsUnit.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Mat_GoodsUnit.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Mat_GoodsUnit.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Mat_GoodsUnit.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Mat_GoodsUnit.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Mat_GoodsUnit.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Mat_GoodsUnit.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Mat_GoodsUnit.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Mat_GoodsUnit.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Mat_GoodsUnit.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Mat_GoodsUnit.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Mat_GoodsUnit.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Mat_GoodsUnit.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Mat_GoodsUnit.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Mat_GoodsUnit.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Mat_GoodsUnit.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.unitname != null ">
   or Mat_GoodsUnit.UnitName like concat('%', #{SearchPojo.unitname}, '%')
</if>
<if test="SearchPojo.backcolorargb != null ">
   or Mat_GoodsUnit.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
</if>
<if test="SearchPojo.forecolorargb != null ">
   or Mat_GoodsUnit.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Mat_GoodsUnit.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Mat_GoodsUnit.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Mat_GoodsUnit.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Mat_GoodsUnit.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Mat_GoodsUnit.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Mat_GoodsUnit.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Mat_GoodsUnit.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Mat_GoodsUnit.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Mat_GoodsUnit.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Mat_GoodsUnit.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Mat_GoodsUnit.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Mat_GoodsUnit.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Mat_GoodsUnit.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Mat_GoodsUnit.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Mat_GoodsUnit.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Mat_GoodsUnit(id, UnitName, EnabledMark, DecNum, RoundingType, UnitType, BackColorArgb, ForeColorArgb, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{unitname}, #{enabledmark}, #{decnum}, #{roundingtype}, #{unittype}, #{backcolorargb}, #{forecolorargb}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_GoodsUnit
        <set>
            <if test="unitname != null ">
                UnitName =#{unitname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="decnum != null">
                DecNum =#{decnum},
            </if>
            <if test="roundingtype != null">
                RoundingType =#{roundingtype},
            </if>
            <if test="unittype != null">
                UnitType =#{unittype},
            </if>
            <if test="backcolorargb != null ">
                BackColorArgb =#{backcolorargb},
            </if>
            <if test="forecolorargb != null ">
                ForeColorArgb =#{forecolorargb},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Mat_GoodsUnit where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatGoodsunitPojo">
        <include refid="selectMatGoodsunitVo"/>
        where Mat_GoodsUnit.Tenantid =#{tid}
        and Mat_GoodsUnit.UnitType = #{type}
    </select>

    <select id="countUnitname" resultType="int">
        SELECT COUNT(*)
        FROM Mat_GoodsUnit
        WHERE LOWER(UnitName) = #{lowerCaseUnitName}
          AND Tenantid = #{tid}
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
    </select>

</mapper>

