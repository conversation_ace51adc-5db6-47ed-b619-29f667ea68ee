<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSpecorderdrawMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSpecorderdrawPojo">
        select
          id, Pid, Wpid, WpCode, WpName, DrawType, DrawTitle, DrawImage, DrawJson, DrawUrl, InsideMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Mat_SpecOrderDraw
        where Mat_SpecOrderDraw.id = #{key} and Mat_SpecOrderDraw.Tenantid=#{tid}
    </select>
    <sql id="selectMatSpecorderdrawVo">
         select
          id, Pid, Wpid, WpCode, WpName, DrawType, DrawTitle, Draw<PERSON>mage, DrawJson, DrawUrl, InsideMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Mat_SpecOrderDraw
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.goods.domain.pojo.MatSpecorderdrawPojo">
        <include refid="selectMatSpecorderdrawVo"/>
         where 1 = 1 and Mat_SpecOrderDraw.Tenantid =#{Tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Mat_SpecOrderDraw.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Mat_SpecOrderDraw.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
   and Mat_SpecOrderDraw.wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
   and Mat_SpecOrderDraw.wpcode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
   and Mat_SpecOrderDraw.wpname like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.drawtype != null and SearchPojo.drawtype != ''">
   and Mat_SpecOrderDraw.drawtype like concat('%', #{SearchPojo.drawtype}, '%')
</if>
<if test="SearchPojo.drawtitle != null and SearchPojo.drawtitle != ''">
   and Mat_SpecOrderDraw.drawtitle like concat('%', #{SearchPojo.drawtitle}, '%')
</if>
<if test="SearchPojo.drawimage != null and SearchPojo.drawimage != ''">
   and Mat_SpecOrderDraw.drawimage like concat('%', #{SearchPojo.drawimage}, '%')
</if>
<if test="SearchPojo.drawjson != null and SearchPojo.drawjson != ''">
   and Mat_SpecOrderDraw.drawjson like concat('%', #{SearchPojo.drawjson}, '%')
</if>
<if test="SearchPojo.drawurl != null and SearchPojo.drawurl != ''">
   and Mat_SpecOrderDraw.drawurl like concat('%', #{SearchPojo.drawurl}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Mat_SpecOrderDraw.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Mat_SpecOrderDraw.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Mat_SpecOrderDraw.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Mat_SpecOrderDraw.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Mat_SpecOrderDraw.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Mat_SpecOrderDraw.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Mat_SpecOrderDraw.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Mat_SpecOrderDraw.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Mat_SpecOrderDraw.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Mat_SpecOrderDraw.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Mat_SpecOrderDraw.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Mat_SpecOrderDraw.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
   or Mat_SpecOrderDraw.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
   or Mat_SpecOrderDraw.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
   or Mat_SpecOrderDraw.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.drawtype != null and SearchPojo.drawtype != ''">
   or Mat_SpecOrderDraw.DrawType like concat('%', #{SearchPojo.drawtype}, '%')
</if>
<if test="SearchPojo.drawtitle != null and SearchPojo.drawtitle != ''">
   or Mat_SpecOrderDraw.DrawTitle like concat('%', #{SearchPojo.drawtitle}, '%')
</if>
<if test="SearchPojo.drawimage != null and SearchPojo.drawimage != ''">
   or Mat_SpecOrderDraw.DrawImage like concat('%', #{SearchPojo.drawimage}, '%')
</if>
<if test="SearchPojo.drawjson != null and SearchPojo.drawjson != ''">
   or Mat_SpecOrderDraw.DrawJson like concat('%', #{SearchPojo.drawjson}, '%')
</if>
<if test="SearchPojo.drawurl != null and SearchPojo.drawurl != ''">
   or Mat_SpecOrderDraw.DrawUrl like concat('%', #{SearchPojo.drawurl}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Mat_SpecOrderDraw.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Mat_SpecOrderDraw.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Mat_SpecOrderDraw.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Mat_SpecOrderDraw.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Mat_SpecOrderDraw.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Mat_SpecOrderDraw.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Mat_SpecOrderDraw.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Mat_SpecOrderDraw.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Mat_SpecOrderDraw.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Mat_SpecOrderDraw.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Mat_SpecOrderDraw.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatSpecorderdrawPojo">
        select
          id, Pid, Wpid, WpCode, WpName, DrawType, DrawTitle, DrawImage, DrawJson, DrawUrl, InsideMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Mat_SpecOrderDraw
        where Mat_SpecOrderDraw.Pid = #{Pid} and Mat_SpecOrderDraw.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Mat_SpecOrderDraw(id, Pid, Wpid, WpCode, WpName, DrawType, DrawTitle, DrawImage, DrawJson, DrawUrl, InsideMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{wpid}, #{wpcode}, #{wpname}, #{drawtype}, #{drawtitle}, #{drawimage}, #{drawjson}, #{drawurl}, #{insidemark}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SpecOrderDraw
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="wpid != null ">
                Wpid = #{wpid},
            </if>
            <if test="wpcode != null ">
                WpCode = #{wpcode},
            </if>
            <if test="wpname != null ">
                WpName = #{wpname},
            </if>
            <if test="drawtype != null ">
                DrawType = #{drawtype},
            </if>
            <if test="drawtitle != null ">
                DrawTitle = #{drawtitle},
            </if>
            <if test="drawimage != null ">
                DrawImage = #{drawimage},
            </if>
            <if test="drawjson != null ">
                DrawJson = #{drawjson},
            </if>
            <if test="drawurl != null ">
                DrawUrl = #{drawurl},
            </if>
            <if test="insidemark != null">
                InsideMark = #{insidemark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Mat_SpecOrderDraw where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

