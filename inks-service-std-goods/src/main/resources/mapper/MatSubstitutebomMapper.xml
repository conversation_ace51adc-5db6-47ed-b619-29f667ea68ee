<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSubstitutebomMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSubstitutebomPojo">
        SELECT
            Mat_SubstituteBom.id,
            Mat_SubstituteBom.Pid,
            Mat_SubstituteBom.Bomid,
            Mat_SubstituteBom.BomType,
            Mat_SubstituteBom.Goodsid,
            Mat_SubstituteBom.ItemCode,
            Mat_SubstituteBom.ItemName,
            Mat_SubstituteBom.ItemSpec,
            Mat_SubstituteBom.ItemUnit,
            Mat_SubstituteBom.MachUid,
            Mat_SubstituteBom.MachItemid,
            Mat_SubstituteBom.MachGroupid,
            Mat_SubstituteBom.Closed,
            Mat_SubstituteBom.RowNum,
            Mat_SubstituteBom.Remark,
            Mat_SubstituteBom.Custom1,
            Mat_SubstituteBom.Custom2,
            Mat_SubstituteBom.Custom3,
            Mat_SubstituteBom.Custom4,
            Mat_SubstituteBom.Custom5,
            Mat_SubstituteBom.Custom6,
            Mat_SubstituteBom.Custom7,
            Mat_SubstituteBom.Custom8,
            Mat_SubstituteBom.Custom9,
            Mat_SubstituteBom.Custom10,
            Mat_SubstituteBom.Tenantid,
            Mat_SubstituteBom.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Mat_SubstituteBom
                LEFT JOIN Mat_Goods ON Mat_SubstituteBom.Goodsid = Mat_Goods.id
        where Mat_SubstituteBom.id = #{key}
          and Mat_SubstituteBom.Tenantid = #{tid}
    </select>
    <sql id="selectMatSubstitutebomVo">
        SELECT
            Mat_SubstituteBom.id,
            Mat_SubstituteBom.Pid,
            Mat_SubstituteBom.Bomid,
            Mat_SubstituteBom.BomType,
            Mat_SubstituteBom.Goodsid,
            Mat_SubstituteBom.ItemCode,
            Mat_SubstituteBom.ItemName,
            Mat_SubstituteBom.ItemSpec,
            Mat_SubstituteBom.ItemUnit,
            Mat_SubstituteBom.MachUid,
            Mat_SubstituteBom.MachItemid,
            Mat_SubstituteBom.MachGroupid,
            Mat_SubstituteBom.Closed,
            Mat_SubstituteBom.RowNum,
            Mat_SubstituteBom.Remark,
            Mat_SubstituteBom.Custom1,
            Mat_SubstituteBom.Custom2,
            Mat_SubstituteBom.Custom3,
            Mat_SubstituteBom.Custom4,
            Mat_SubstituteBom.Custom5,
            Mat_SubstituteBom.Custom6,
            Mat_SubstituteBom.Custom7,
            Mat_SubstituteBom.Custom8,
            Mat_SubstituteBom.Custom9,
            Mat_SubstituteBom.Custom10,
            Mat_SubstituteBom.Tenantid,
            Mat_SubstituteBom.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Mat_SubstituteBom
                LEFT JOIN Mat_Goods ON Mat_SubstituteBom.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSubstitutebomPojo">
        <include refid="selectMatSubstitutebomVo"/>
        where 1 = 1 and Mat_SubstituteBom.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_SubstituteBom.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_SubstituteBom.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
            and Mat_SubstituteBom.bomid like concat('%', #{SearchPojo.bomid}, '%')
        </if>
        <if test="SearchPojo.bomtype != null and SearchPojo.bomtype != ''">
            and Mat_SubstituteBom.bomtype like concat('%', #{SearchPojo.bomtype}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_SubstituteBom.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_SubstituteBom.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_SubstituteBom.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_SubstituteBom.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_SubstituteBom.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Mat_SubstituteBom.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Mat_SubstituteBom.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Mat_SubstituteBom.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_SubstituteBom.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_SubstituteBom.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_SubstituteBom.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_SubstituteBom.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_SubstituteBom.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_SubstituteBom.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_SubstituteBom.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_SubstituteBom.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_SubstituteBom.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_SubstituteBom.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_SubstituteBom.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_SubstituteBom.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
                or Mat_SubstituteBom.Bomid like concat('%', #{SearchPojo.bomid}, '%')
            </if>
            <if test="SearchPojo.bomtype != null and SearchPojo.bomtype != ''">
                or Mat_SubstituteBom.BomType like concat('%', #{SearchPojo.bomtype}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_SubstituteBom.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_SubstituteBom.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_SubstituteBom.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_SubstituteBom.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_SubstituteBom.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Mat_SubstituteBom.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Mat_SubstituteBom.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Mat_SubstituteBom.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_SubstituteBom.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_SubstituteBom.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_SubstituteBom.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_SubstituteBom.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_SubstituteBom.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_SubstituteBom.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_SubstituteBom.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_SubstituteBom.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_SubstituteBom.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_SubstituteBom.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_SubstituteBom.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatSubstitutebomPojo">
        SELECT
            Mat_SubstituteBom.id,
            Mat_SubstituteBom.Pid,
            Mat_SubstituteBom.Bomid,
            Mat_SubstituteBom.BomType,
            Mat_SubstituteBom.Goodsid,
            Mat_SubstituteBom.ItemCode,
            Mat_SubstituteBom.ItemName,
            Mat_SubstituteBom.ItemSpec,
            Mat_SubstituteBom.ItemUnit,
            Mat_SubstituteBom.MachUid,
            Mat_SubstituteBom.MachItemid,
            Mat_SubstituteBom.MachGroupid,
            Mat_SubstituteBom.Closed,
            Mat_SubstituteBom.RowNum,
            Mat_SubstituteBom.Remark,
            Mat_SubstituteBom.Custom1,
            Mat_SubstituteBom.Custom2,
            Mat_SubstituteBom.Custom3,
            Mat_SubstituteBom.Custom4,
            Mat_SubstituteBom.Custom5,
            Mat_SubstituteBom.Custom6,
            Mat_SubstituteBom.Custom7,
            Mat_SubstituteBom.Custom8,
            Mat_SubstituteBom.Custom9,
            Mat_SubstituteBom.Custom10,
            Mat_SubstituteBom.Tenantid,
            Mat_SubstituteBom.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Mat_SubstituteBom
                LEFT JOIN Mat_Goods ON Mat_SubstituteBom.Goodsid = Mat_Goods.id
        where Mat_SubstituteBom.Pid = #{Pid}
          and Mat_SubstituteBom.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_SubstituteBom(id, Pid, Bomid, BomType, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, MachUid,
                                      MachItemid, MachGroupid, Closed, RowNum, Remark, Custom1, Custom2, Custom3,
                                      Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                      Revision)
        values (#{id}, #{pid}, #{bomid}, #{bomtype}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit},
                #{machuid}, #{machitemid}, #{machgroupid}, #{closed}, #{rownum}, #{remark}, #{custom1}, #{custom2},
                #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10},
                #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SubstituteBom
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="bomid != null ">
                Bomid = #{bomid},
            </if>
            <if test="bomtype != null ">
                BomType = #{bomtype},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_SubstituteBom
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

