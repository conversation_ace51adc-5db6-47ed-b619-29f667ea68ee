<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSupplierMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSupplierPojo">
        select
          id, Goodsid, GoodsUid, GoodsName, GoodsSpec, GoodsUnit, Groupid, GroupUid, GroupName, StateCode, StateDate, Operator, Operatorid, Remark, Closed, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Mat_Supplier
        where Mat_Supplier.id = #{key} and Mat_Supplier.Tenantid=#{tid}
    </select>
    <sql id="selectMatSupplierVo">
         select
          id, Goodsid, GoodsUid, GoodsName, GoodsSpec, GoodsUnit, Groupid, GroupUid, GroupName, StateCode, StateDate, Operator, Operatorid, Remark, Closed, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Mat_Supplier
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.goods.domain.pojo.MatSupplierPojo">
        <include refid="selectMatSupplierVo"/>
         where 1 = 1 and Mat_Supplier.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Mat_Supplier.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid  != ''">
   and Mat_Supplier.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid  != ''">
   and Mat_Supplier.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
</if>
<if test="SearchPojo.goodsname != null and SearchPojo.goodsname  != ''">
   and Mat_Supplier.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
</if>
<if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec  != ''">
   and Mat_Supplier.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
</if>
<if test="SearchPojo.goodsunit != null and SearchPojo.goodsunit  != ''">
   and Mat_Supplier.GoodsUnit like concat('%', #{SearchPojo.goodsunit}, '%')
</if>
<if test="SearchPojo.groupid != null and SearchPojo.groupid  != ''">
   and Mat_Supplier.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupuid != null and SearchPojo.groupuid  != ''">
   and Mat_Supplier.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
</if>
<if test="SearchPojo.groupname != null and SearchPojo.groupname  != ''">
   and Mat_Supplier.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode  != ''">
   and Mat_Supplier.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.operator != null and SearchPojo.operator  != ''">
   and Mat_Supplier.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null and SearchPojo.operatorid  != ''">
   and Mat_Supplier.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
   and Mat_Supplier.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
   and Mat_Supplier.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
   and Mat_Supplier.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
   and Mat_Supplier.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
   and Mat_Supplier.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
   and Mat_Supplier.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
   and Mat_Supplier.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
   and Mat_Supplier.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
   and Mat_Supplier.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
   and Mat_Supplier.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6  != ''">
   and Mat_Supplier.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7  != ''">
   and Mat_Supplier.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8  != ''">
   and Mat_Supplier.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9  != ''">
   and Mat_Supplier.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10  != ''">
   and Mat_Supplier.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
     and (1=0 
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Mat_Supplier.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid != ''">
   or Mat_Supplier.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
</if>
<if test="SearchPojo.goodsname != null and SearchPojo.goodsname != ''">
   or Mat_Supplier.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
</if>
<if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec != ''">
   or Mat_Supplier.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
</if>
<if test="SearchPojo.goodsunit != null and SearchPojo.goodsunit != ''">
   or Mat_Supplier.GoodsUnit like concat('%', #{SearchPojo.goodsunit}, '%')
</if>
<if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
   or Mat_Supplier.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupuid != null and SearchPojo.groupuid != ''">
   or Mat_Supplier.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
</if>
<if test="SearchPojo.groupname != null and SearchPojo.groupname != ''">
   or Mat_Supplier.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   or Mat_Supplier.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.operator != null and SearchPojo.operator != ''">
   or Mat_Supplier.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null and SearchPojo.operatorid != ''">
   or Mat_Supplier.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Mat_Supplier.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or Mat_Supplier.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or Mat_Supplier.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Mat_Supplier.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Mat_Supplier.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Mat_Supplier.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Mat_Supplier.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Mat_Supplier.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Mat_Supplier.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Mat_Supplier.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Mat_Supplier.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Mat_Supplier.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Mat_Supplier.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Mat_Supplier.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Mat_Supplier.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
)
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Mat_Supplier(id, Goodsid, GoodsUid, GoodsName, GoodsSpec, GoodsUnit, Groupid, GroupUid, GroupName, StateCode, StateDate, Operator, Operatorid, Remark, Closed, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{goodsid}, #{goodsuid}, #{goodsname}, #{goodsspec}, #{goodsunit}, #{groupid}, #{groupuid}, #{groupname}, #{statecode}, #{statedate}, #{operator}, #{operatorid}, #{remark}, #{closed}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Supplier
        <set>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="goodsuid != null ">
                GoodsUid =#{goodsuid},
            </if>
            <if test="goodsname != null ">
                GoodsName =#{goodsname},
            </if>
            <if test="goodsspec != null ">
                GoodsSpec =#{goodsspec},
            </if>
            <if test="goodsunit != null ">
                GoodsUnit =#{goodsunit},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="groupuid != null ">
                GroupUid =#{groupuid},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Mat_Supplier where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                                        </mapper>

