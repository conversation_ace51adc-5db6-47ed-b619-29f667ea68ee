<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatBomorderMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatBomorderPojo">
        SELECT
            Mat_BomOrder.id,
            Mat_BomOrder.MachUid,
            Mat_BomOrder.MachItemid,
            Mat_BomOrder.MachGroupid,
            Mat_BomOrder.Goodsid,
            Mat_BomOrder.ItemCode,
            Mat_BomOrder.ItemName,
            Mat_BomOrder.ItemSpec,
            Mat_BomOrder.ItemUnit,
            Mat_BomOrder.Quantity,
            Mat_BomOrder.Summary,
            Mat_BomOrder.CreateBy,
            Mat_Bo<PERSON>Orde<PERSON>.<PERSON>reateByid,
            <PERSON>_<PERSON><PERSON>Orde<PERSON>.CreateDate,
            Mat_Bo<PERSON>Order.Listerid,
            Mat_BomOrder.Lister,
            Mat_<PERSON><PERSON>Order.ModifyDate,
            Mat_BomOrder.Assessor,
            Mat_BomOrder.Assessorid,
            Mat_BomOrder.AssessDate,
            Mat_BomOrder.Custom1,
            Mat_BomOrder.Custom2,
            Mat_BomOrder.Custom3,
            Mat_BomOrder.Custom4,
            Mat_BomOrder.Custom5,
            Mat_BomOrder.Custom6,
            Mat_BomOrder.Custom7,
            Mat_BomOrder.Custom8,
            Mat_BomOrder.Custom9,
            Mat_BomOrder.Custom10,
            Mat_BomOrder.Tenantid,
            Mat_BomOrder.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Mat_BomOrder
                LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomOrder.Goodsid

        where Mat_BomOrder.id = #{key}
          and Mat_BomOrder.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT
            Mat_BomOrder.id,
            Mat_BomOrder.MachUid,
            Mat_BomOrder.MachItemid,
            Mat_BomOrder.MachGroupid,
            Mat_BomOrder.Goodsid,
            Mat_BomOrder.ItemCode,
            Mat_BomOrder.ItemName,
            Mat_BomOrder.ItemSpec,
            Mat_BomOrder.ItemUnit,
            Mat_BomOrder.Quantity,
            Mat_BomOrder.Summary,
            Mat_BomOrder.CreateBy,
            Mat_BomOrder.CreateByid,
            Mat_BomOrder.CreateDate,
            Mat_BomOrder.Listerid,
            Mat_BomOrder.Lister,
            Mat_BomOrder.ModifyDate,
            Mat_BomOrder.Assessor,
            Mat_BomOrder.Assessorid,
            Mat_BomOrder.AssessDate,
            Mat_BomOrder.Custom1,
            Mat_BomOrder.Custom2,
            Mat_BomOrder.Custom3,
            Mat_BomOrder.Custom4,
            Mat_BomOrder.Custom5,
            Mat_BomOrder.Custom6,
            Mat_BomOrder.Custom7,
            Mat_BomOrder.Custom8,
            Mat_BomOrder.Custom9,
            Mat_BomOrder.Custom10,
            Mat_BomOrder.Tenantid,
            Mat_BomOrder.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Mat_BomOrder
                LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomOrder.Goodsid

    </sql>
    <sql id="selectdetailVo">
        SELECT
            Mat_BomOrder.id,
            Mat_BomOrder.MachUid,
            Mat_BomOrder.MachItemid,
            Mat_BomOrder.MachGroupid,
            Mat_BomOrder.Goodsid,
            Mat_BomOrder.ItemCode,
            Mat_BomOrder.ItemName,
            Mat_BomOrder.ItemSpec,
            Mat_BomOrder.ItemUnit,
            Mat_BomOrder.Quantity,
            Mat_BomOrder.Summary,
            Mat_BomOrder.CreateBy,
            Mat_BomOrder.CreateByid,
            Mat_BomOrder.CreateDate,
            Mat_BomOrder.Listerid,
            Mat_BomOrder.Lister,
            Mat_BomOrder.ModifyDate,
            Mat_BomOrder.Assessor,
            Mat_BomOrder.Assessorid,
            Mat_BomOrder.AssessDate,
            Mat_BomOrder.Custom1,
            Mat_BomOrder.Custom2,
            Mat_BomOrder.Custom3,
            Mat_BomOrder.Custom4,
            Mat_BomOrder.Custom5,
            Mat_BomOrder.Custom6,
            Mat_BomOrder.Custom7,
            Mat_BomOrder.Custom8,
            Mat_BomOrder.Custom9,
            Mat_BomOrder.Custom10,
            Mat_BomOrder.Tenantid,
            Mat_BomOrder.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Mat_BomOrder
                LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomOrder.Goodsid

    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_BomOrder.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_BomOrder.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Mat_BomOrder.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Mat_BomOrder.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Mat_BomOrder.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_BomOrder.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_BomOrder.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_BomOrder.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_BomOrder.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_BomOrder.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_BomOrder.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_BomOrder.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_BomOrder.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_BomOrder.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_BomOrder.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Mat_BomOrder.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Mat_BomOrder.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_BomOrder.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_BomOrder.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_BomOrder.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_BomOrder.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_BomOrder.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_BomOrder.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_BomOrder.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_BomOrder.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_BomOrder.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_BomOrder.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Mat_BomOrder.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Mat_BomOrder.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Mat_BomOrder.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_BomOrder.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_BomOrder.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_BomOrder.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_BomOrder.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_BomOrder.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Mat_BomOrder.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_BomOrder.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_BomOrder.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_BomOrder.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_BomOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Mat_BomOrder.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Mat_BomOrder.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_BomOrder.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_BomOrder.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_BomOrder.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_BomOrder.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_BomOrder.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_BomOrder.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_BomOrder.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_BomOrder.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_BomOrder.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_BomOrder.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatBomorderPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_BomOrder.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_BomOrder.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Mat_BomOrder.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Mat_BomOrder.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Mat_BomOrder.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_BomOrder.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_BomOrder.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_BomOrder.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_BomOrder.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_BomOrder.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_BomOrder.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_BomOrder.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_BomOrder.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_BomOrder.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_BomOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Mat_BomOrder.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Mat_BomOrder.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_BomOrder.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_BomOrder.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_BomOrder.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_BomOrder.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_BomOrder.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_BomOrder.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_BomOrder.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_BomOrder.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_BomOrder.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_BomOrder.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Mat_BomOrder.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Mat_BomOrder.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Mat_BomOrder.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_BomOrder.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_BomOrder.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_BomOrder.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_BomOrder.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_BomOrder.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Mat_BomOrder.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_BomOrder.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_BomOrder.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_BomOrder.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_BomOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Mat_BomOrder.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Mat_BomOrder.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_BomOrder.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_BomOrder.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_BomOrder.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_BomOrder.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_BomOrder.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_BomOrder.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_BomOrder.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_BomOrder.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_BomOrder.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_BomOrder.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_BomOrder(id, MachUid, MachItemid, MachGroupid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit,
                                 Quantity, Summary, CreateBy, CreateByid, CreateDate, Listerid, Lister, ModifyDate,
                                 Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
                                 Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{machuid}, #{machitemid}, #{machgroupid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec},
                #{itemunit}, #{quantity}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{listerid}, #{lister},
                #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_BomOrder
        <set>
            <if test="machuid != null ">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid =#{machgroupid},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_BomOrder
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_BomOrder
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatBomorderPojo">
        select
        id
        from Mat_BomOrderItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <!--查询单个-->
    <select id="getEntityByGoodsid" resultType="inks.service.std.goods.domain.pojo.MatBomorderPojo">
        SELECT
            Mat_BomOrder.id,
            Mat_BomOrder.MachUid,
            Mat_BomOrder.MachItemid,
            Mat_BomOrder.MachGroupid,
            Mat_BomOrder.Goodsid,
            Mat_BomOrder.ItemCode,
            Mat_BomOrder.ItemName,
            Mat_BomOrder.ItemSpec,
            Mat_BomOrder.ItemUnit,
            Mat_BomOrder.Quantity,
            Mat_BomOrder.Summary,
            Mat_BomOrder.CreateBy,
            Mat_BomOrder.CreateByid,
            Mat_BomOrder.CreateDate,
            Mat_BomOrder.Listerid,
            Mat_BomOrder.Lister,
            Mat_BomOrder.ModifyDate,
            Mat_BomOrder.Assessor,
            Mat_BomOrder.Assessorid,
            Mat_BomOrder.AssessDate,
            Mat_BomOrder.Custom1,
            Mat_BomOrder.Custom2,
            Mat_BomOrder.Custom3,
            Mat_BomOrder.Custom4,
            Mat_BomOrder.Custom5,
            Mat_BomOrder.Custom6,
            Mat_BomOrder.Custom7,
            Mat_BomOrder.Custom8,
            Mat_BomOrder.Custom9,
            Mat_BomOrder.Custom10,
            Mat_BomOrder.Tenantid,
            Mat_BomOrder.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Mat_BomOrder
                LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomOrder.Goodsid

        where Mat_BomOrder.Goodsid = #{key}
          and Mat_BomOrder.Tenantid = #{tid}
    </select>


    <!--查询单个-->
    <select id="getListByItemGoodsid" resultType="inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo">
        SELECT Mat_BomOrderItem.id,
               Mat_BomOrderItem.Pid,
               Mat_BomOrder.Goodsid,
               Mat_BomOrderItem.Goodsid AS ItemGoodsid,
               Mat_BomOrderItem.ItemCode,
               Mat_BomOrderItem.ItemName,
               Mat_BomOrderItem.ItemSpec,
               Mat_BomOrderItem.ItemUnit,
               Mat_BomOrderItem.MainQty,
               Mat_BomOrderItem.SubQty,
               Mat_BomOrderItem.LossRate,
               Mat_BomOrderItem.AttrCode,
               Mat_BomOrderItem.FlowCode,
               Mat_BomOrderItem.RowNum,
               Mat_BomOrderItem.Remark,
               Mat_BomOrderItem.Custom1,
               Mat_BomOrderItem.Custom2,
               Mat_BomOrderItem.Custom3,
               Mat_BomOrderItem.Custom4,
               Mat_BomOrderItem.Custom5,
               Mat_BomOrderItem.Custom6,
               Mat_BomOrderItem.Custom7,
               Mat_BomOrderItem.Custom8,
               Mat_BomOrderItem.Custom9,
               Mat_BomOrderItem.Custom10,
               Mat_BomOrderItem.Tenantid,
               Mat_BomOrderItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_BomOrder.CreateBy,
               Mat_BomOrder.Lister,
               Mat_BomOrder.Assessor,
               '订单BOM' as bomtype
        FROM Mat_BomOrder
                 RIGHT JOIN Mat_BomOrderItem ON Mat_BomOrderItem.Pid = Mat_BomOrder.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomOrder.Goodsid
        where Mat_BomOrderItem.Goodsid = #{key}
          and Mat_BomOrderItem.Tenantid = #{tid}
    </select>
</mapper>

