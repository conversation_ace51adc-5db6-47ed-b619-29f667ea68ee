<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSubstituteMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSubstitutePojo">
        SELECT Mat_Substitute.id,
               Mat_Substitute.RefNo,
               Mat_Substitute.BillDate,
               Mat_Substitute.BillType,
               Mat_Substitute.BillTitle,
               Mat_Substitute.Goodsid,
               Mat_Substitute.ItemCode,
               Mat_Substitute.ItemName,
               Mat_Substitute.ItemSpec,
               Mat_Substitute.ItemUnit,
               Mat_Substitute.BlendMark,
               Mat_Substitute.BomMark,
               Mat_Substitute.EnabledMark,
               Mat_Substitute.Summary,
               Mat_Substitute.CreateBy,
               Mat_Substitute.CreateByid,
               Mat_Substitute.CreateDate,
               Mat_Substitute.Lister,
               Mat_Substitute.Listerid,
               Mat_Substitute.ModifyDate,
               Mat_Substitute.Custom1,
               Mat_Substitute.Custom2,
               Mat_Substitute.Custom3,
               Mat_Substitute.Custom4,
               Mat_Substitute.Custom5,
               Mat_Substitute.Custom6,
               Mat_Substitute.Custom7,
               Mat_Substitute.Custom8,
               Mat_Substitute.Custom9,
               Mat_Substitute.Custom10,
               Mat_Substitute.Tenantid,
               Mat_Substitute.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        FROM Mat_Goods
                 RIGHT JOIN Mat_Substitute ON Mat_Goods.id = Mat_Substitute.Goodsid
        where Mat_Substitute.id = #{key}
          and Mat_Substitute.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Mat_Substitute.id,
               Mat_Substitute.RefNo,
               Mat_Substitute.BillDate,
               Mat_Substitute.BillType,
               Mat_Substitute.BillTitle,
               Mat_Substitute.Goodsid,
               Mat_Substitute.ItemCode,
               Mat_Substitute.ItemName,
               Mat_Substitute.ItemSpec,
               Mat_Substitute.ItemUnit,
               Mat_Substitute.BlendMark,
               Mat_Substitute.BomMark,
               Mat_Substitute.EnabledMark,
               Mat_Substitute.Summary,
               Mat_Substitute.CreateBy,
               Mat_Substitute.CreateByid,
               Mat_Substitute.CreateDate,
               Mat_Substitute.Lister,
               Mat_Substitute.Listerid,
               Mat_Substitute.ModifyDate,
               Mat_Substitute.Custom1,
               Mat_Substitute.Custom2,
               Mat_Substitute.Custom3,
               Mat_Substitute.Custom4,
               Mat_Substitute.Custom5,
               Mat_Substitute.Custom6,
               Mat_Substitute.Custom7,
               Mat_Substitute.Custom8,
               Mat_Substitute.Custom9,
               Mat_Substitute.Custom10,
               Mat_Substitute.Tenantid,
               Mat_Substitute.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        FROM Mat_Goods
                 RIGHT JOIN Mat_Substitute ON Mat_Goods.id = Mat_Substitute.Goodsid
    </sql>
    <sql id="selectdetailVo">
        SELECT Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_SubstituteItem.id,
               Mat_SubstituteItem.Pid,
               Mat_SubstituteItem.Goodsid,
               Mat_SubstituteItem.ItemCode,
               Mat_SubstituteItem.ItemName,
               Mat_SubstituteItem.ItemSpec,
               Mat_SubstituteItem.ItemUnit,
               Mat_SubstituteItem.MainQty,
               Mat_SubstituteItem.SubQty,
               Mat_SubstituteItem.SubRate,
               Mat_SubstituteItem.StratDate,
               Mat_SubstituteItem.EndDate,
               Mat_SubstituteItem.Priority,
               Mat_SubstituteItem.Closed,
               Mat_SubstituteItem.RowNum,
               Mat_SubstituteItem.Remark,
               Mat_SubstituteItem.Custom1,
               Mat_SubstituteItem.Custom2,
               Mat_SubstituteItem.Custom3,
               Mat_SubstituteItem.Custom4,
               Mat_SubstituteItem.Custom5,
               Mat_SubstituteItem.Custom6,
               Mat_SubstituteItem.Custom7,
               Mat_SubstituteItem.Custom8,
               Mat_SubstituteItem.Custom9,
               Mat_SubstituteItem.Custom10,
               Mat_SubstituteItem.Tenantid,
               Mat_SubstituteItem.Revision,
               Mat_Substitute.RefNo,
               Mat_Substitute.BillDate,
               Mat_Substitute.BillType,
               Mat_Substitute.BillTitle,
               Mat_Substitute.Goodsid AS MainGoodsid,
               Mat_Substitute.CreateBy,
               Mat_Substitute.Lister
        FROM Mat_Goods
                 RIGHT JOIN Mat_Substitute ON Mat_Goods.id = Mat_Substitute.Goodsid
                 RIGHT JOIN Mat_SubstituteItem ON Mat_SubstituteItem.Pid = Mat_Substitute.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSubstituteitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_Substitute.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Substitute.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Mat_Substitute.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Mat_Substitute.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Mat_Substitute.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_Substitute.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_Substitute.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_Substitute.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_Substitute.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_Substitute.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_Substitute.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_Substitute.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_Substitute.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_Substitute.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_Substitute.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_Substitute.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_Substitute.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_Substitute.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_Substitute.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_Substitute.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_Substitute.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_Substitute.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_Substitute.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_Substitute.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_Substitute.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.std.goods.mapper.extend.ExtFilterMapper.goodsandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Mat_Substitute.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Mat_Substitute.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Mat_Substitute.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_Substitute.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_Substitute.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_Substitute.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_Substitute.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_Substitute.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Mat_Substitute.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_Substitute.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_Substitute.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_Substitute.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_Substitute.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_Substitute.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_Substitute.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_Substitute.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_Substitute.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_Substitute.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_Substitute.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_Substitute.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_Substitute.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_Substitute.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_Substitute.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.std.goods.mapper.extend.ExtFilterMapper.goodsorfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSubstitutePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_Substitute.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Substitute.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Mat_Substitute.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Mat_Substitute.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Mat_Substitute.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_Substitute.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_Substitute.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_Substitute.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_Substitute.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_Substitute.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_Substitute.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_Substitute.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_Substitute.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_Substitute.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_Substitute.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_Substitute.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_Substitute.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_Substitute.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_Substitute.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_Substitute.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_Substitute.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_Substitute.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_Substitute.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_Substitute.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_Substitute.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.std.goods.mapper.extend.ExtFilterMapper.goodsandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Mat_Substitute.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Mat_Substitute.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Mat_Substitute.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_Substitute.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_Substitute.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_Substitute.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_Substitute.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_Substitute.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Mat_Substitute.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_Substitute.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_Substitute.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_Substitute.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_Substitute.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_Substitute.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_Substitute.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_Substitute.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_Substitute.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_Substitute.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_Substitute.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_Substitute.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_Substitute.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_Substitute.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_Substitute.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.std.goods.mapper.extend.ExtFilterMapper.goodsorfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Substitute(id, RefNo, BillDate, BillType, BillTitle, Goodsid, ItemCode, ItemName, ItemSpec,
                                   ItemUnit, BlendMark, BomMark, EnabledMark, Summary, CreateBy, CreateByid, CreateDate,
                                   Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
                                   Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{goodsid}, #{itemcode}, #{itemname},
                #{itemspec}, #{itemunit}, #{blendmark}, #{bommark}, #{enabledmark}, #{summary}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Substitute
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="blendmark != null">
                BlendMark =#{blendmark},
            </if>
            <if test="bommark != null">
                BomMark =#{bommark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Substitute
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatSubstitutePojo">
        select
        id
        from Mat_SubstituteItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <!--查询DelListIds-->
    <select id="getDelBomIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatSubstitutePojo">
        select
        id
        from Mat_SubstituteBom
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <!--查询单个-->
    <select id="getEntityByGoodsid" resultType="inks.service.std.goods.domain.pojo.MatSubstitutePojo">
        SELECT Mat_Substitute.id,
               Mat_Substitute.RefNo,
               Mat_Substitute.BillDate,
               Mat_Substitute.BillType,
               Mat_Substitute.BillTitle,
               Mat_Substitute.Goodsid,
               Mat_Substitute.ItemCode,
               Mat_Substitute.ItemName,
               Mat_Substitute.ItemSpec,
               Mat_Substitute.ItemUnit,
               Mat_Substitute.BlendMark,
               Mat_Substitute.BomMark,
               Mat_Substitute.EnabledMark,
               Mat_Substitute.Summary,
               Mat_Substitute.CreateBy,
               Mat_Substitute.CreateByid,
               Mat_Substitute.CreateDate,
               Mat_Substitute.Lister,
               Mat_Substitute.Listerid,
               Mat_Substitute.ModifyDate,
               Mat_Substitute.Custom1,
               Mat_Substitute.Custom2,
               Mat_Substitute.Custom3,
               Mat_Substitute.Custom4,
               Mat_Substitute.Custom5,
               Mat_Substitute.Custom6,
               Mat_Substitute.Custom7,
               Mat_Substitute.Custom8,
               Mat_Substitute.Custom9,
               Mat_Substitute.Custom10,
               Mat_Substitute.Tenantid,
               Mat_Substitute.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        FROM Mat_Goods
                 RIGHT JOIN Mat_Substitute ON Mat_Goods.id = Mat_Substitute.Goodsid
        where Mat_Substitute.Goodsid = #{key}
          and Mat_Substitute.Tenantid = #{tid}
    </select>

    <!--查询单个-->
    <select id="chkSubstitute" resultType="inks.service.std.goods.domain.pojo.MatSubstitutePojo">
        SELECT
        Mat_Substitute.id,
        Mat_Substitute.RefNo,
        Mat_Substitute.BillDate,
        Mat_Substitute.BillType,
        Mat_Substitute.BillTitle,
        Mat_Substitute.Goodsid,
        Mat_Substitute.ItemCode,
        Mat_Substitute.ItemName,
        Mat_Substitute.ItemSpec,
        Mat_Substitute.ItemUnit,
        Mat_Substitute.BlendMark,
        Mat_Substitute.BomMark,
        Mat_Substitute.EnabledMark,
        Mat_Substitute.Summary,
        Mat_Substitute.CreateBy,
        Mat_Substitute.CreateByid,
        Mat_Substitute.CreateDate,
        Mat_Substitute.Lister,
        Mat_Substitute.Listerid,
        Mat_Substitute.ModifyDate,
        Mat_Substitute.Custom1,
        Mat_Substitute.Custom2,
        Mat_Substitute.Custom3,
        Mat_Substitute.Custom4,
        Mat_Substitute.Custom5,
        Mat_Substitute.Custom6,
        Mat_Substitute.Custom7,
        Mat_Substitute.Custom8,
        Mat_Substitute.Custom9,
        Mat_Substitute.Custom10,
        Mat_Substitute.Tenantid,
        Mat_Substitute.Revision,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid
        FROM
        Mat_Substitute
        LEFT JOIN Mat_SubstituteBom ON Mat_Substitute.id = Mat_SubstituteBom.Pid
        LEFT JOIN Mat_SubstituteItem ON Mat_Substitute.id = Mat_SubstituteItem.Pid
        LEFT JOIN Mat_Goods ON Mat_Substitute.Goodsid = Mat_Goods.id
        where Mat_Substitute.Goodsid = #{mkey} and
        Mat_SubstituteItem.Goodsid = #{skey}
        and Mat_Substitute.Tenantid = #{tid}
        <if test="bomid != null ">
            and Mat_SubstituteBom.Bomid = #{bomid}
        </if>
    </select>
</mapper>

