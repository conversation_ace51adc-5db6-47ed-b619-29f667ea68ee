<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSpecMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSpecPojo">
        select Mat_Spec.id,
               Mat_Spec.RefNo,
               Mat_Spec.BillDate,
               Mat_Spec.BillType,
               Mat_Spec.BillTitle,
               Mat_Spec.Goodsid,
               Mat_Spec.ItemCode,
               Mat_Spec.ItemName,
               Mat_Spec.ItemSpec,
               Mat_Spec.ItemUnit,
               Mat_Spec.PnlX,
               Mat_Spec.PnlY,
               Mat_Spec.Pnl2Pcs,
               Mat_Spec.PnlBX,
               Mat_Spec.PnlBY,
               Mat_Spec.PnlB2Pcs,
               Mat_Spec.Operator,
               Mat_Spec.Operatorid,
               <PERSON>_<PERSON>pec.<PERSON><PERSON>,
               Mat_Spec.Attribut<PERSON><PERSON><PERSON>,
               Mat_<PERSON>pec.<PERSON>ry,
               Mat_Spec.EnabledMark,
               Mat_Spec.VersionNum,
               Mat_Spec.CreateBy,
               Mat_Spec.CreateByid,
               Mat_Spec.CreateDate,
               Mat_Spec.Lister,
               Mat_Spec.Listerid,
               Mat_Spec.ModifyDate,
               Mat_Spec.Assessor,
               Mat_Spec.Assessorid,
               Mat_Spec.AssessDate,
               Mat_Spec.Custom1,
               Mat_Spec.Custom2,
               Mat_Spec.Custom3,
               Mat_Spec.Custom4,
               Mat_Spec.Custom5,
               Mat_Spec.Custom6,
               Mat_Spec.Custom7,
               Mat_Spec.Custom8,
               Mat_Spec.Custom9,
               Mat_Spec.Custom10,
               Mat_Spec.Tenantid,
               Mat_Spec.TenantName,
               Mat_Spec.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Material as GoodsMaterial
        from Mat_Spec
                 LEFT JOIN Mat_Goods ON Mat_Spec.Goodsid = Mat_Goods.id
        where Mat_Spec.id = #{key}
          and Mat_Spec.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               RefNo,
               BillDate,
               BillType,
               BillTitle,
               Goodsid,
               ItemCode,
               ItemName,
               ItemSpec,
               ItemUnit,
               PnlX,
               PnlY,
               Pnl2Pcs,
               PnlBX,
               PnlBY,
               PnlB2Pcs,
               Operator,
               Operatorid,
               QuickKey,
               AttributeJson,
               Summary,
               EnabledMark,
               VersionNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Spec
    </sql>
    <sql id="selectdetailVo">
        select id,
               RefNo,
               BillDate,
               BillType,
               BillTitle,
               Goodsid,
               ItemCode,
               ItemName,
               ItemSpec,
               ItemUnit,
               PnlX,
               PnlY,
               Pnl2Pcs,
               PnlBX,
               PnlBY,
               PnlB2Pcs,
               Operator,
               Operatorid,
               QuickKey,
               AttributeJson,
               Summary,
               EnabledMark,
               VersionNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Spec
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSpecitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_Spec.Tenantid = #{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Mat_Spec.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Mat_Spec.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Mat_Spec.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Mat_Spec.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Mat_Spec.goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null">
            and Mat_Spec.itemcode like concat('%',
                #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Mat_Spec.itemname like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null">
            and Mat_Spec.itemspec like concat('%',
                #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null">
            and Mat_Spec.itemunit like concat('%',
                #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Mat_Spec.operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Mat_Spec.operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Mat_Spec.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.versionnum != null">
            and Mat_Spec.versionnum like concat('%',
                #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Mat_Spec.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Mat_Spec.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Mat_Spec.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Mat_Spec.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Mat_Spec.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Mat_Spec.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Mat_Spec.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Mat_Spec.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Mat_Spec.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Mat_Spec.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Mat_Spec.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Mat_Spec.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Mat_Spec.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Mat_Spec.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Mat_Spec.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Mat_Spec.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Mat_Spec.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Mat_Spec.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Mat_Spec.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Mat_Spec.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Mat_Spec.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null">
                or Mat_Spec.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Mat_Spec.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null">
                or Mat_Spec.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null">
                or Mat_Spec.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Mat_Spec.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Mat_Spec.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Mat_Spec.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.versionnum != null">
                or Mat_Spec.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Mat_Spec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Mat_Spec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Mat_Spec.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Mat_Spec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Mat_Spec.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Mat_Spec.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Mat_Spec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Mat_Spec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Mat_Spec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Mat_Spec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Mat_Spec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Mat_Spec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Mat_Spec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Mat_Spec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Mat_Spec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Mat_Spec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Mat_Spec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSpecPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_Spec.Tenantid = #{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Mat_Spec.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Mat_Spec.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Mat_Spec.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Mat_Spec.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Mat_Spec.Goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null">
            and Mat_Spec.ItemCode like concat('%',
                #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Mat_Spec.ItemName like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null">
            and Mat_Spec.ItemSpec like concat('%',
                #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null">
            and Mat_Spec.ItemUnit like concat('%',
                #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Mat_Spec.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Mat_Spec.Operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Mat_Spec.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.versionnum != null">
            and Mat_Spec.VersionNum like concat('%',
                #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Mat_Spec.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Mat_Spec.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Mat_Spec.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Mat_Spec.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Mat_Spec.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Mat_Spec.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Mat_Spec.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Mat_Spec.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Mat_Spec.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Mat_Spec.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Mat_Spec.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Mat_Spec.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Mat_Spec.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Mat_Spec.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Mat_Spec.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Mat_Spec.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Mat_Spec.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Mat_Spec.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Mat_Spec.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Mat_Spec.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Mat_Spec.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null">
                or Mat_Spec.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Mat_Spec.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null">
                or Mat_Spec.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null">
                or Mat_Spec.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Mat_Spec.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Mat_Spec.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Mat_Spec.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.versionnum != null">
                or Mat_Spec.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Mat_Spec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Mat_Spec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Mat_Spec.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Mat_Spec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Mat_Spec.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Mat_Spec.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Mat_Spec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Mat_Spec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Mat_Spec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Mat_Spec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Mat_Spec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Mat_Spec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Mat_Spec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Mat_Spec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Mat_Spec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Mat_Spec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Mat_Spec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Spec(id, RefNo, BillDate, BillType, BillTitle, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit,
                             PnlX, PnlY, Pnl2Pcs, PnlBX, PnlBY, PnlB2Pcs, Operator, Operatorid, QuickKey, AttributeJson,
                             Summary, EnabledMark, VersionNum, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                             ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5,
                             Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{goodsid}, #{itemcode}, #{itemname},
                #{itemspec}, #{itemunit}, #{pnlx}, #{pnly}, #{pnl2pcs}, #{pnlbx}, #{pnlby}, #{pnlb2pcs}, #{operator},
                #{operatorid}, #{quickkey}, #{attributejson}, #{summary}, #{enabledmark}, #{versionnum}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid},
                #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Spec
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null">
                ItemUnit =#{itemunit},
            </if>
            <if test="pnlx != null">
                PnlX =#{pnlx},
            </if>
            <if test="pnly != null">
                PnlY =#{pnly},
            </if>
            <if test="pnl2pcs != null">
                Pnl2Pcs =#{pnl2pcs},
            </if>
            <if test="pnlbx != null">
                PnlBX =#{pnlbx},
            </if>
            <if test="pnlby != null">
                PnlBY =#{pnlby},
            </if>
            <if test="pnlb2pcs != null">
                PnlB2Pcs =#{pnlb2pcs},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="quickkey != null">
                QuickKey =#{quickkey},
            </if>
            <if test="attributejson != null">
                AttributeJson =#{attributejson},
            </if>

            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="versionnum != null">
                VersionNum =#{versionnum},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Spec
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_Spec
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatSpecPojo">
        select id
        from Mat_SpecItem
        where Pid = #{id}
          and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

</mapper>

