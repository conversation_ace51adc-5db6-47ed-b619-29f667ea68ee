<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSpecpcbMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbPojo">
        SELECT Mat_SpecPcb.id,
               Mat_SpecPcb.RefNo,
               Mat_SpecPcb.BillDate,
               Mat_SpecPcb.BillType,
               Mat_SpecPcb.BillTitle,
               Mat_SpecPcb.Goodsid,
               Mat_SpecPcb.ItemCode,
               Mat_SpecPcb.ItemName,
               Mat_SpecPcb.ItemSpec,
               Mat_SpecPcb.ItemUnit,
               Mat_SpecPcb.VersionNum,
               Mat_SpecPcb.GoodsClass,
               Mat_SpecPcb.Material,
               Mat_SpecPcb.Surface,
               Mat_SpecPcb.Groupid,
               Mat_SpecPcb.PcsX,
               Mat_SpecPcb.PcsY,
               Mat_SpecPcb.SizeUnit,
               Mat_SpecPcb.SetX,
               Mat_SpecPcb.SetY,
               Mat_SpecPcb.Set2Pcs,
               Mat_SpecPcb.AllowNg,
               Mat_SpecPcb.PnlX,
               Mat_SpecPcb.PnlY,
               Mat_SpecPcb.Pnl2Pcs,
               Mat_SpecPcb.PnlBX,
               Mat_SpecPcb.PnlBY,
               Mat_SpecPcb.PnlB2Pcs,
               Mat_SpecPcb.PnlCX,
               Mat_SpecPcb.PnlCY,
               Mat_SpecPcb.PnlC2Pcs,
               Mat_SpecPcb.PnlDX,
               Mat_SpecPcb.PnlDY,
               Mat_SpecPcb.PnlD2Pcs,
               Mat_SpecPcb.Operator,
               Mat_SpecPcb.Parentid,
               Mat_SpecPcb.LayerNum,
               Mat_SpecPcb.MachType,
               Mat_SpecPcb.MachClass,
               Mat_SpecPcb.EnabledMark,
               Mat_SpecPcb.StateCode,
               Mat_SpecPcb.Technics,
               Mat_SpecPcb.Mat2Pcs,
               Mat_SpecPcb.MatThick,
               Mat_SpecPcb.MatCuThick,
               Mat_SpecPcb.MatName,
               Mat_SpecPcb.MatCode,
               Mat_SpecPcb.MatFactory,
               Mat_SpecPcb.MatColor,
               Mat_SpecPcb.ProductCode,
               Mat_SpecPcb.ProductThick,
               Mat_SpecPcb.ProductCuThick,
               Mat_SpecPcb.ProductWeight,
               Mat_SpecPcb.PrintLayer,
               Mat_SpecPcb.PnlUseRate,
               Mat_SpecPcb.CutUseRate,
               Mat_SpecPcb.Summary,
               Mat_SpecPcb.CreateBy,
               Mat_SpecPcb.CreateByid,
               Mat_SpecPcb.CreateDate,
               Mat_SpecPcb.Lister,
               Mat_SpecPcb.Listerid,
               Mat_SpecPcb.ModifyDate,
               Mat_SpecPcb.Assessor,
               Mat_SpecPcb.Assessorid,
               Mat_SpecPcb.AssessDate,
               Mat_SpecPcb.Custom1,
               Mat_SpecPcb.Custom2,
               Mat_SpecPcb.Custom3,
               Mat_SpecPcb.Custom4,
               Mat_SpecPcb.Custom5,
               Mat_SpecPcb.Custom6,
               Mat_SpecPcb.Custom7,
               Mat_SpecPcb.Custom8,
               Mat_SpecPcb.Custom9,
               Mat_SpecPcb.Custom10,
               Mat_SpecPcb.Tenantid,
               Mat_SpecPcb.TenantName,
               Mat_SpecPcb.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        FROM Mat_Goods
                 RIGHT JOIN Mat_SpecPcb ON Mat_SpecPcb.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Mat_SpecPcb.Groupid
        where Mat_SpecPcb.id = #{key}
          and Mat_SpecPcb.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Mat_SpecPcb.id,
               Mat_SpecPcb.RefNo,
               Mat_SpecPcb.BillDate,
               Mat_SpecPcb.BillType,
               Mat_SpecPcb.BillTitle,
               Mat_SpecPcb.Goodsid,
               Mat_SpecPcb.ItemCode,
               Mat_SpecPcb.ItemName,
               Mat_SpecPcb.ItemSpec,
               Mat_SpecPcb.ItemUnit,
               Mat_SpecPcb.VersionNum,
               Mat_SpecPcb.GoodsClass,
               Mat_SpecPcb.Material,
               Mat_SpecPcb.Surface,
               Mat_SpecPcb.Groupid,
               Mat_SpecPcb.PcsX,
               Mat_SpecPcb.PcsY,
               Mat_SpecPcb.SizeUnit,
               Mat_SpecPcb.SetX,
               Mat_SpecPcb.SetY,
               Mat_SpecPcb.Set2Pcs,
               Mat_SpecPcb.AllowNg,
               Mat_SpecPcb.PnlX,
               Mat_SpecPcb.PnlY,
               Mat_SpecPcb.Pnl2Pcs,
               Mat_SpecPcb.PnlBX,
               Mat_SpecPcb.PnlBY,
               Mat_SpecPcb.PnlB2Pcs,
               Mat_SpecPcb.PnlCX,
               Mat_SpecPcb.PnlCY,
               Mat_SpecPcb.PnlC2Pcs,
               Mat_SpecPcb.PnlDX,
               Mat_SpecPcb.PnlDY,
               Mat_SpecPcb.PnlD2Pcs,
               Mat_SpecPcb.Operator,
               Mat_SpecPcb.Parentid,
               Mat_SpecPcb.LayerNum,
               Mat_SpecPcb.MachType,
               Mat_SpecPcb.MachClass,
               Mat_SpecPcb.EnabledMark,
               Mat_SpecPcb.StateCode,
               Mat_SpecPcb.Technics,
               Mat_SpecPcb.Mat2Pcs,
               Mat_SpecPcb.MatThick,
               Mat_SpecPcb.MatCuThick,
               Mat_SpecPcb.MatName,
               Mat_SpecPcb.MatCode,
               Mat_SpecPcb.MatFactory,
               Mat_SpecPcb.MatColor,
               Mat_SpecPcb.ProductCode,
               Mat_SpecPcb.ProductThick,
               Mat_SpecPcb.ProductCuThick,
               Mat_SpecPcb.ProductWeight,
               Mat_SpecPcb.PrintLayer,
               Mat_SpecPcb.PnlUseRate,
               Mat_SpecPcb.CutUseRate,
               Mat_SpecPcb.Summary,
               Mat_SpecPcb.CreateBy,
               Mat_SpecPcb.CreateByid,
               Mat_SpecPcb.CreateDate,
               Mat_SpecPcb.Lister,
               Mat_SpecPcb.Listerid,
               Mat_SpecPcb.ModifyDate,
               Mat_SpecPcb.Assessor,
               Mat_SpecPcb.Assessorid,
               Mat_SpecPcb.AssessDate,
               Mat_SpecPcb.Custom1,
               Mat_SpecPcb.Custom2,
               Mat_SpecPcb.Custom3,
               Mat_SpecPcb.Custom4,
               Mat_SpecPcb.Custom5,
               Mat_SpecPcb.Custom6,
               Mat_SpecPcb.Custom7,
               Mat_SpecPcb.Custom8,
               Mat_SpecPcb.Custom9,
               Mat_SpecPcb.Custom10,
               Mat_SpecPcb.Tenantid,
               Mat_SpecPcb.TenantName,
               Mat_SpecPcb.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        FROM Mat_Goods
                 RIGHT JOIN Mat_SpecPcb ON Mat_SpecPcb.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Mat_SpecPcb.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT Mat_SpecPcb.id,
               Mat_SpecPcb.RefNo,
               Mat_SpecPcb.BillDate,
               Mat_SpecPcb.BillType,
               Mat_SpecPcb.BillTitle,
               Mat_SpecPcb.Goodsid,
               Mat_SpecPcb.ItemCode,
               Mat_SpecPcb.ItemName,
               Mat_SpecPcb.ItemSpec,
               Mat_SpecPcb.ItemUnit,
               Mat_SpecPcb.VersionNum,
               Mat_SpecPcb.GoodsClass,
               Mat_SpecPcb.Material,
               Mat_SpecPcb.Surface,
               Mat_SpecPcb.Groupid,
               Mat_SpecPcb.PcsX,
               Mat_SpecPcb.PcsY,
               Mat_SpecPcb.SizeUnit,
               Mat_SpecPcb.SetX,
               Mat_SpecPcb.SetY,
               Mat_SpecPcb.Set2Pcs,
               Mat_SpecPcb.AllowNg,
               Mat_SpecPcb.PnlX,
               Mat_SpecPcb.PnlY,
               Mat_SpecPcb.Pnl2Pcs,
               Mat_SpecPcb.PnlBX,
               Mat_SpecPcb.PnlBY,
               Mat_SpecPcb.PnlB2Pcs,
               Mat_SpecPcb.PnlCX,
               Mat_SpecPcb.PnlCY,
               Mat_SpecPcb.PnlC2Pcs,
               Mat_SpecPcb.PnlDX,
               Mat_SpecPcb.PnlDY,
               Mat_SpecPcb.PnlD2Pcs,
               Mat_SpecPcb.Operator,
               Mat_SpecPcb.Parentid,
               Mat_SpecPcb.LayerNum,
               Mat_SpecPcb.MachType,
               Mat_SpecPcb.MachClass,
               Mat_SpecPcb.EnabledMark,
               Mat_SpecPcb.StateCode,
               Mat_SpecPcb.Technics,
               Mat_SpecPcb.Mat2Pcs,
               Mat_SpecPcb.MatThick,
               Mat_SpecPcb.MatCuThick,
               Mat_SpecPcb.MatName,
               Mat_SpecPcb.MatCode,
               Mat_SpecPcb.MatFactory,
               Mat_SpecPcb.MatColor,
               Mat_SpecPcb.ProductCode,
               Mat_SpecPcb.ProductThick,
               Mat_SpecPcb.ProductCuThick,
               Mat_SpecPcb.ProductWeight,
               Mat_SpecPcb.PrintLayer,
               Mat_SpecPcb.PnlUseRate,
               Mat_SpecPcb.CutUseRate,
               Mat_SpecPcb.Summary,
               Mat_SpecPcb.CreateBy,
               Mat_SpecPcb.CreateByid,
               Mat_SpecPcb.CreateDate,
               Mat_SpecPcb.Lister,
               Mat_SpecPcb.Listerid,
               Mat_SpecPcb.ModifyDate,
               Mat_SpecPcb.Assessor,
               Mat_SpecPcb.Assessorid,
               Mat_SpecPcb.AssessDate,
               Mat_SpecPcb.Custom1,
               Mat_SpecPcb.Custom2,
               Mat_SpecPcb.Custom3,
               Mat_SpecPcb.Custom4,
               Mat_SpecPcb.Custom5,
               Mat_SpecPcb.Custom6,
               Mat_SpecPcb.Custom7,
               Mat_SpecPcb.Custom8,
               Mat_SpecPcb.Custom9,
               Mat_SpecPcb.Custom10,
               Mat_SpecPcb.Tenantid,
               Mat_SpecPcb.TenantName,
               Mat_SpecPcb.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        FROM Mat_Goods
                 RIGHT JOIN Mat_SpecPcb ON Mat_SpecPcb.Goodsid = Mat_Goods.id
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Mat_SpecPcb.Groupid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSpecpcbitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_SpecPcb.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_SpecPcb.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Mat_SpecPcb.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Mat_SpecPcb.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Mat_SpecPcb.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null ">
            and Mat_SpecPcb.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null ">
            and Mat_SpecPcb.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null ">
            and Mat_SpecPcb.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null ">
            and Mat_SpecPcb.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null ">
            and Mat_SpecPcb.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.versionnum != null ">
            and Mat_SpecPcb.versionnum like concat('%', #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.goodsclass != null ">
            and Mat_SpecPcb.goodsclass like concat('%', #{SearchPojo.goodsclass}, '%')
        </if>
        <if test="SearchPojo.material != null ">
            and Mat_SpecPcb.material like concat('%', #{SearchPojo.material}, '%')
        </if>
        <if test="SearchPojo.surface != null ">
            and Mat_SpecPcb.surface like concat('%', #{SearchPojo.surface}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Mat_SpecPcb.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.sizeunit != null ">
            and Mat_SpecPcb.sizeunit like concat('%', #{SearchPojo.sizeunit}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_SpecPcb.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.machtype != null ">
            and Mat_SpecPcb.machtype like concat('%', #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.machclass != null ">
            and Mat_SpecPcb.machclass like concat('%', #{SearchPojo.machclass}, '%')
        </if>
        <if test="SearchPojo.statecode != null ">
            and Mat_SpecPcb.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.technics != null ">
            and Mat_SpecPcb.technics like concat('%', #{SearchPojo.technics}, '%')
        </if>
        <if test="SearchPojo.matthick != null ">
            and Mat_SpecPcb.matthick like concat('%', #{SearchPojo.matthick}, '%')
        </if>
        <if test="SearchPojo.matcuthick != null ">
            and Mat_SpecPcb.matcuthick like concat('%', #{SearchPojo.matcuthick}, '%')
        </if>
        <if test="SearchPojo.matname != null ">
            and Mat_SpecPcb.matname like concat('%', #{SearchPojo.matname}, '%')
        </if>
        <if test="SearchPojo.matcode != null ">
            and Mat_SpecPcb.matcode like concat('%', #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.matfactory != null ">
            and Mat_SpecPcb.matfactory like concat('%', #{SearchPojo.matfactory}, '%')
        </if>
        <if test="SearchPojo.matcolor != null ">
            and Mat_SpecPcb.matcolor like concat('%', #{SearchPojo.matcolor}, '%')
        </if>
        <if test="SearchPojo.productcode != null ">
            and Mat_SpecPcb.productcode like concat('%', #{SearchPojo.productcode}, '%')
        </if>
        <if test="SearchPojo.productthick != null ">
            and Mat_SpecPcb.productthick like concat('%', #{SearchPojo.productthick}, '%')
        </if>
        <if test="SearchPojo.productcuthick != null ">
            and Mat_SpecPcb.productcuthick like concat('%', #{SearchPojo.productcuthick}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Mat_SpecPcb.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_SpecPcb.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_SpecPcb.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_SpecPcb.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_SpecPcb.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Mat_SpecPcb.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Mat_SpecPcb.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_SpecPcb.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_SpecPcb.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_SpecPcb.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_SpecPcb.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_SpecPcb.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_SpecPcb.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_SpecPcb.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_SpecPcb.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_SpecPcb.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_SpecPcb.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_SpecPcb.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Mat_SpecPcb.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Mat_SpecPcb.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Mat_SpecPcb.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null ">
                or Mat_SpecPcb.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null ">
                or Mat_SpecPcb.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null ">
                or Mat_SpecPcb.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null ">
                or Mat_SpecPcb.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null ">
                or Mat_SpecPcb.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.versionnum != null ">
                or Mat_SpecPcb.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.goodsclass != null ">
                or Mat_SpecPcb.GoodsClass like concat('%', #{SearchPojo.goodsclass}, '%')
            </if>
            <if test="SearchPojo.material != null ">
                or Mat_SpecPcb.Material like concat('%', #{SearchPojo.material}, '%')
            </if>
            <if test="SearchPojo.surface != null ">
                or Mat_SpecPcb.Surface like concat('%', #{SearchPojo.surface}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Mat_SpecPcb.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.sizeunit != null ">
                or Mat_SpecPcb.SizeUnit like concat('%', #{SearchPojo.sizeunit}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_SpecPcb.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.machtype != null ">
                or Mat_SpecPcb.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.machclass != null ">
                or Mat_SpecPcb.MachClass like concat('%', #{SearchPojo.machclass}, '%')
            </if>
            <if test="SearchPojo.statecode != null ">
                or Mat_SpecPcb.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.technics != null ">
                or Mat_SpecPcb.Technics like concat('%', #{SearchPojo.technics}, '%')
            </if>
            <if test="SearchPojo.matthick != null ">
                or Mat_SpecPcb.MatThick like concat('%', #{SearchPojo.matthick}, '%')
            </if>
            <if test="SearchPojo.matcuthick != null ">
                or Mat_SpecPcb.MatCuThick like concat('%', #{SearchPojo.matcuthick}, '%')
            </if>
            <if test="SearchPojo.matname != null ">
                or Mat_SpecPcb.MatName like concat('%', #{SearchPojo.matname}, '%')
            </if>
            <if test="SearchPojo.matcode != null ">
                or Mat_SpecPcb.MatCode like concat('%', #{SearchPojo.matcode}, '%')
            </if>
            <if test="SearchPojo.matfactory != null ">
                or Mat_SpecPcb.MatFactory like concat('%', #{SearchPojo.matfactory}, '%')
            </if>
            <if test="SearchPojo.matcolor != null ">
                or Mat_SpecPcb.MatColor like concat('%', #{SearchPojo.matcolor}, '%')
            </if>
            <if test="SearchPojo.productcode != null ">
                or Mat_SpecPcb.ProductCode like concat('%', #{SearchPojo.productcode}, '%')
            </if>
            <if test="SearchPojo.productthick != null ">
                or Mat_SpecPcb.ProductThick like concat('%', #{SearchPojo.productthick}, '%')
            </if>
            <if test="SearchPojo.productcuthick != null ">
                or Mat_SpecPcb.ProductCuThick like concat('%', #{SearchPojo.productcuthick}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Mat_SpecPcb.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_SpecPcb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_SpecPcb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_SpecPcb.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_SpecPcb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Mat_SpecPcb.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Mat_SpecPcb.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_SpecPcb.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_SpecPcb.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_SpecPcb.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_SpecPcb.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_SpecPcb.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_SpecPcb.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_SpecPcb.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_SpecPcb.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_SpecPcb.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_SpecPcb.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_SpecPcb.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSpecpcbPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_SpecPcb.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_SpecPcb.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Mat_SpecPcb.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Mat_SpecPcb.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Mat_SpecPcb.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.goodsid != null ">
            and Mat_SpecPcb.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid  != ''">
            and Mat_Supplier.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null and SearchPojo.goodsname  != ''">
            and Mat_Supplier.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec  != ''">
            and Mat_Supplier.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.goodsunit != null and SearchPojo.goodsunit  != ''">
            and Mat_Supplier.GoodsUnit like concat('%', #{SearchPojo.goodsunit}, '%')
        </if>
        <if test="SearchPojo.itemcode != null ">
            and Mat_SpecPcb.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null ">
            and Mat_SpecPcb.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null ">
            and Mat_SpecPcb.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null ">
            and Mat_SpecPcb.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.versionnum != null ">
            and Mat_SpecPcb.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.goodsclass != null ">
            and Mat_SpecPcb.GoodsClass like concat('%', #{SearchPojo.goodsclass}, '%')
        </if>
        <if test="SearchPojo.material != null ">
            and Mat_SpecPcb.Material like concat('%', #{SearchPojo.material}, '%')
        </if>
        <if test="SearchPojo.surface != null ">
            and Mat_SpecPcb.Surface like concat('%', #{SearchPojo.surface}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Mat_SpecPcb.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.sizeunit != null ">
            and Mat_SpecPcb.SizeUnit like concat('%', #{SearchPojo.sizeunit}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_SpecPcb.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.machtype != null ">
            and Mat_SpecPcb.MachType like concat('%', #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.machclass != null ">
            and Mat_SpecPcb.MachClass like concat('%', #{SearchPojo.machclass}, '%')
        </if>
        <if test="SearchPojo.statecode != null ">
            and Mat_SpecPcb.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.technics != null ">
            and Mat_SpecPcb.Technics like concat('%', #{SearchPojo.technics}, '%')
        </if>
        <if test="SearchPojo.matthick != null ">
            and Mat_SpecPcb.MatThick like concat('%', #{SearchPojo.matthick}, '%')
        </if>
        <if test="SearchPojo.matcuthick != null ">
            and Mat_SpecPcb.MatCuThick like concat('%', #{SearchPojo.matcuthick}, '%')
        </if>
        <if test="SearchPojo.matname != null ">
            and Mat_SpecPcb.MatName like concat('%', #{SearchPojo.matname}, '%')
        </if>
        <if test="SearchPojo.matcode != null ">
            and Mat_SpecPcb.MatCode like concat('%', #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.matfactory != null ">
            and Mat_SpecPcb.MatFactory like concat('%', #{SearchPojo.matfactory}, '%')
        </if>
        <if test="SearchPojo.matcolor != null ">
            and Mat_SpecPcb.MatColor like concat('%', #{SearchPojo.matcolor}, '%')
        </if>
        <if test="SearchPojo.productcode != null ">
            and Mat_SpecPcb.ProductCode like concat('%', #{SearchPojo.productcode}, '%')
        </if>
        <if test="SearchPojo.productthick != null ">
            and Mat_SpecPcb.ProductThick like concat('%', #{SearchPojo.productthick}, '%')
        </if>
        <if test="SearchPojo.productcuthick != null ">
            and Mat_SpecPcb.ProductCuThick like concat('%', #{SearchPojo.productcuthick}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Mat_SpecPcb.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_SpecPcb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_SpecPcb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_SpecPcb.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_SpecPcb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Mat_SpecPcb.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Mat_SpecPcb.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_SpecPcb.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_SpecPcb.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_SpecPcb.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_SpecPcb.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_SpecPcb.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_SpecPcb.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_SpecPcb.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_SpecPcb.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_SpecPcb.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_SpecPcb.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_SpecPcb.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Mat_SpecPcb.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Mat_SpecPcb.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Mat_SpecPcb.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.goodsid != null ">
                or Mat_SpecPcb.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null ">
                or Mat_SpecPcb.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null ">
                or Mat_SpecPcb.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null ">
                or Mat_SpecPcb.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null ">
                or Mat_SpecPcb.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.versionnum != null ">
                or Mat_SpecPcb.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.goodsclass != null ">
                or Mat_SpecPcb.GoodsClass like concat('%', #{SearchPojo.goodsclass}, '%')
            </if>
            <if test="SearchPojo.material != null ">
                or Mat_SpecPcb.Material like concat('%', #{SearchPojo.material}, '%')
            </if>
            <if test="SearchPojo.surface != null ">
                or Mat_SpecPcb.Surface like concat('%', #{SearchPojo.surface}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Mat_SpecPcb.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.sizeunit != null ">
                or Mat_SpecPcb.SizeUnit like concat('%', #{SearchPojo.sizeunit}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_SpecPcb.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.machtype != null ">
                or Mat_SpecPcb.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.machclass != null ">
                or Mat_SpecPcb.MachClass like concat('%', #{SearchPojo.machclass}, '%')
            </if>
            <if test="SearchPojo.statecode != null ">
                or Mat_SpecPcb.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.technics != null ">
                or Mat_SpecPcb.Technics like concat('%', #{SearchPojo.technics}, '%')
            </if>
            <if test="SearchPojo.matthick != null ">
                or Mat_SpecPcb.MatThick like concat('%', #{SearchPojo.matthick}, '%')
            </if>
            <if test="SearchPojo.matcuthick != null ">
                or Mat_SpecPcb.MatCuThick like concat('%', #{SearchPojo.matcuthick}, '%')
            </if>
            <if test="SearchPojo.matname != null ">
                or Mat_SpecPcb.MatName like concat('%', #{SearchPojo.matname}, '%')
            </if>
            <if test="SearchPojo.matcode != null ">
                or Mat_SpecPcb.MatCode like concat('%', #{SearchPojo.matcode}, '%')
            </if>
            <if test="SearchPojo.matfactory != null ">
                or Mat_SpecPcb.MatFactory like concat('%', #{SearchPojo.matfactory}, '%')
            </if>
            <if test="SearchPojo.matcolor != null ">
                or Mat_SpecPcb.MatColor like concat('%', #{SearchPojo.matcolor}, '%')
            </if>
            <if test="SearchPojo.productcode != null ">
                or Mat_SpecPcb.ProductCode like concat('%', #{SearchPojo.productcode}, '%')
            </if>
            <if test="SearchPojo.productthick != null ">
                or Mat_SpecPcb.ProductThick like concat('%', #{SearchPojo.productthick}, '%')
            </if>
            <if test="SearchPojo.productcuthick != null ">
                or Mat_SpecPcb.ProductCuThick like concat('%', #{SearchPojo.productcuthick}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Mat_SpecPcb.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_SpecPcb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_SpecPcb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_SpecPcb.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_SpecPcb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Mat_SpecPcb.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Mat_SpecPcb.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_SpecPcb.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_SpecPcb.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_SpecPcb.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_SpecPcb.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_SpecPcb.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_SpecPcb.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_SpecPcb.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_SpecPcb.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_SpecPcb.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_SpecPcb.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_SpecPcb.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_SpecPcb(id, RefNo, BillDate, BillType, BillTitle, Goodsid, ItemCode, ItemName, ItemSpec,
                                ItemUnit, VersionNum, GoodsClass, Material, Surface, Groupid, PcsX, PcsY, SizeUnit,
                                SetX, SetY, Set2Pcs, AllowNg, PnlX, PnlY, Pnl2Pcs, PnlBX, PnlBY, PnlB2Pcs, PnlCX, PnlCY,
                                PnlC2Pcs, PnlDX, PnlDY, PnlD2Pcs, Operator, Parentid, LayerNum, MachType, MachClass,
                                EnabledMark, StateCode, Technics, Mat2Pcs, MatThick, MatCuThick, MatName, MatCode,
                                MatFactory, MatColor, ProductCode, ProductThick, ProductCuThick, ProductWeight,
                                PrintLayer, PnlUseRate, CutUseRate, Summary, CreateBy, CreateByid, CreateDate, Lister,
                                Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3,
                                Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName,
                                Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{goodsid}, #{itemcode}, #{itemname},
                #{itemspec}, #{itemunit}, #{versionnum}, #{goodsclass}, #{material}, #{surface}, #{groupid}, #{pcsx},
                #{pcsy}, #{sizeunit}, #{setx}, #{sety}, #{set2pcs}, #{allowng}, #{pnlx}, #{pnly}, #{pnl2pcs}, #{pnlbx},
                #{pnlby}, #{pnlb2pcs}, #{pnlcx}, #{pnlcy}, #{pnlc2pcs}, #{pnldx}, #{pnldy}, #{pnld2pcs}, #{operator},
                #{parentid}, #{layernum}, #{machtype}, #{machclass}, #{enabledmark}, #{statecode}, #{technics},
                #{mat2pcs}, #{matthick}, #{matcuthick}, #{matname}, #{matcode}, #{matfactory}, #{matcolor},
                #{productcode}, #{productthick}, #{productcuthick}, #{productweight}, #{printlayer}, #{pnluserate},
                #{cutuserate}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},
                #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SpecPcb
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="versionnum != null ">
                VersionNum =#{versionnum},
            </if>
            <if test="goodsclass != null ">
                GoodsClass =#{goodsclass},
            </if>
            <if test="material != null ">
                Material =#{material},
            </if>
            <if test="surface != null ">
                Surface =#{surface},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="pcsx != null">
                PcsX =#{pcsx},
            </if>
            <if test="pcsy != null">
                PcsY =#{pcsy},
            </if>
            <if test="sizeunit != null ">
                SizeUnit =#{sizeunit},
            </if>
            <if test="setx != null">
                SetX =#{setx},
            </if>
            <if test="sety != null">
                SetY =#{sety},
            </if>
            <if test="set2pcs != null">
                Set2Pcs =#{set2pcs},
            </if>
            <if test="allowng != null">
                AllowNg =#{allowng},
            </if>
            <if test="pnlx != null">
                PnlX =#{pnlx},
            </if>
            <if test="pnly != null">
                PnlY =#{pnly},
            </if>
            <if test="pnl2pcs != null">
                Pnl2Pcs =#{pnl2pcs},
            </if>
            <if test="pnlbx != null">
                PnlBX =#{pnlbx},
            </if>
            <if test="pnlby != null">
                PnlBY =#{pnlby},
            </if>
            <if test="pnlb2pcs != null">
                PnlB2Pcs =#{pnlb2pcs},
            </if>
            <if test="pnlcx != null">
                PnlCX =#{pnlcx},
            </if>
            <if test="pnlcy != null">
                PnlCY =#{pnlcy},
            </if>
            <if test="pnlc2pcs != null">
                PnlC2Pcs =#{pnlc2pcs},
            </if>
            <if test="pnldx != null">
                PnlDX =#{pnldx},
            </if>
            <if test="pnldy != null">
                PnlDY =#{pnldy},
            </if>
            <if test="pnld2pcs != null">
                PnlD2Pcs =#{pnld2pcs},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="layernum != null">
                LayerNum =#{layernum},
            </if>
            <if test="machtype != null ">
                MachType =#{machtype},
            </if>
            <if test="machclass != null ">
                MachClass =#{machclass},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="technics != null ">
                Technics =#{technics},
            </if>
            <if test="mat2pcs != null">
                Mat2Pcs =#{mat2pcs},
            </if>
            <if test="matthick != null ">
                MatThick =#{matthick},
            </if>
            <if test="matcuthick != null ">
                MatCuThick =#{matcuthick},
            </if>
            <if test="matname != null ">
                MatName =#{matname},
            </if>
            <if test="matcode != null ">
                MatCode =#{matcode},
            </if>
            <if test="matfactory != null ">
                MatFactory =#{matfactory},
            </if>
            <if test="matcolor != null ">
                MatColor =#{matcolor},
            </if>
            <if test="productcode != null ">
                ProductCode =#{productcode},
            </if>
            <if test="productthick != null ">
                ProductThick =#{productthick},
            </if>
            <if test="productcuthick != null ">
                ProductCuThick =#{productcuthick},
            </if>
            <if test="productweight != null">
                ProductWeight =#{productweight},
            </if>
            <if test="printlayer != null">
                PrintLayer =#{printlayer},
            </if>
            <if test="pnluserate != null">
                PnlUseRate =#{pnluserate},
            </if>
            <if test="cutuserate != null">
                CutUseRate =#{cutuserate},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_SpecPcb
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_SpecPcb
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatSpecpcbPojo">
        select
        id
        from Mat_SpecPcbItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDelDrawIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatSpecpcbPojo">
        select
        id
        from Mat_SpecPcbDraw
        where Pid = #{id}
        <if test="draw !=null and draw.size()>0">
            and id not in
            <foreach collection="draw" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>


    <select id="getDelDrlIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatSpecpcbPojo">
        select
        id
        from Mat_SpecPcbDrl
        where Pid = #{id}
        <if test="drl !=null and drl.size()>0">
            and id not in
            <foreach collection="drl" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="checkGoodsid" resultType="int">
        select count(*)
        from Mat_SpecPcb
        where Goodsid = #{goodsid}
          and Tenantid = #{tenantid}
    </select>

    <select id="getEntityByGoodsid" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbPojo">
        <include refid="selectbillVo"/>
        where Mat_SpecPcb.Goodsid = #{goodsid}
          and Mat_SpecPcb.Tenantid = #{tenantid}
    </select>

    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbPojo">
        <include refid="selectbillVo"/>
        where 1=1
    </select>

    <select id="getGroupInfo" resultType="inks.common.core.domain.WorkgroupPojo">
        select GroupName, GroupUid, Abbreviate
        from App_Workgroup
        where id = #{groupid}
          and Tenantid = #{tid};
    </select>
</mapper>

