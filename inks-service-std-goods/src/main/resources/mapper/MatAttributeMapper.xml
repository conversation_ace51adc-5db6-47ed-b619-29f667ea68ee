<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatAttributeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatAttributePojo">
        select id,
               AttrGroupid,
               AttrKey,
               AttrName,
               ValueType,
               DefValue,
               ValueJson,
               ListShow,
               EnabledMark,
               SkuMark,
               NumericMark,
               RequiredMark,
               UseDomain,
               Remark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Attribute
        where Mat_Attribute.id = #{key}
          and Mat_Attribute.Tenantid = #{tid}
    </select>
    <sql id="selectMatAttributeVo">
        select id,
               AttrGroupid,
               AttrKey,
               AttrName,
               ValueType,
               DefValue,
               ValueJson,
               ListShow,
               EnabledMark,
               SkuMark,
               NumericMark,
               RequiredMark,
               UseDomain,
               Remark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Attribute
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatAttributePojo">
        <include refid="selectMatAttributeVo"/>
        where 1 = 1 and Mat_Attribute.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Attribute.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.attrgroupid != null ">
            and Mat_Attribute.AttrGroupid like concat('%', #{SearchPojo.attrgroupid}, '%')
        </if>
        <if test="SearchPojo.attrkey != null ">
            and Mat_Attribute.AttrKey like concat('%', #{SearchPojo.attrkey}, '%')
        </if>
        <if test="SearchPojo.attrname != null ">
            and Mat_Attribute.AttrName like concat('%', #{SearchPojo.attrname}, '%')
        </if>
        <if test="SearchPojo.valuetype != null ">
            and Mat_Attribute.ValueType like concat('%', #{SearchPojo.valuetype}, '%')
        </if>
        <if test="SearchPojo.defvalue != null ">
            and Mat_Attribute.DefValue like concat('%', #{SearchPojo.defvalue}, '%')
        </if>
        <if test="SearchPojo.valuejson != null ">
            and Mat_Attribute.ValueJson like concat('%', #{SearchPojo.valuejson}, '%')
        </if>
        <if test="SearchPojo.usedomain != null ">
            and Mat_Attribute.UseDomain like concat('%', #{SearchPojo.useDomain}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Mat_Attribute.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Attribute.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Attribute.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Attribute.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Attribute.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Attribute.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Attribute.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Attribute.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Attribute.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Attribute.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Attribute.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Attribute.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Attribute.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Attribute.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Attribute.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_Attribute.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.attrgroupid != null ">
                or Mat_Attribute.AttrGroupid like concat('%', #{SearchPojo.attrgroupid}, '%')
            </if>
            <if test="SearchPojo.attrkey != null ">
                or Mat_Attribute.AttrKey like concat('%', #{SearchPojo.attrkey}, '%')
            </if>
            <if test="SearchPojo.attrname != null ">
                or Mat_Attribute.AttrName like concat('%', #{SearchPojo.attrname}, '%')
            </if>
            <if test="SearchPojo.valuetype != null ">
                or Mat_Attribute.ValueType like concat('%', #{SearchPojo.valuetype}, '%')
            </if>
            <if test="SearchPojo.defvalue != null ">
                or Mat_Attribute.DefValue like concat('%', #{SearchPojo.defvalue}, '%')
            </if>
            <if test="SearchPojo.valuejson != null ">
                or Mat_Attribute.ValueJson like concat('%', #{SearchPojo.valuejson}, '%')
            </if>
            <if test="SearchPojo.usedomain != null ">
                or Mat_Attribute.UseDomain like concat('%', #{SearchPojo.useDomain}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Mat_Attribute.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Attribute.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Attribute.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Attribute.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Attribute.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Attribute.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Attribute.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Attribute.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Attribute.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Attribute.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Attribute.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Attribute.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Attribute.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Attribute.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Attribute.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_Attribute.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Attribute(id, AttrGroupid, AttrKey, AttrName, ValueType, DefValue, ValueJson, ListShow,
                                  EnabledMark, SkuMark, NumericMark, RequiredMark,UseDomain, Remark, RowNum, CreateBy, CreateByid,
                                  CreateDate, Lister,
                                  Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7,
                                  Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{attrgroupid}, #{attrkey}, #{attrname}, #{valuetype}, #{defvalue}, #{valuejson}, #{listshow},
                #{enabledmark}, #{skumark}, #{numericmark}, #{requiredmark}, #{usedomain}, #{remark}, #{rownum}, #{createby},
                #{createbyid}, #{createdate}, #{lister},
                #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Attribute
        <set>
            <if test="attrgroupid != null ">
                AttrGroupid =#{attrgroupid},
            </if>
            <if test="attrkey != null ">
                AttrKey =#{attrkey},
            </if>
            <if test="attrname != null ">
                AttrName =#{attrname},
            </if>
            <if test="valuetype != null ">
                ValueType =#{valuetype},
            </if>
            <if test="defvalue != null ">
                DefValue =#{defvalue},
            </if>
            <if test="valuejson != null ">
                ValueJson =#{valuejson},
            </if>
            <if test="listshow != null">
                ListShow =#{listshow},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="skumark != null">
                SkuMark =#{skumark},
            </if>
            <if test="numericmark != null">
                NumericMark =#{numericmark},
            </if>
            <if test="requiredmark != null">
                RequiredMark =#{requiredmark},
            </if>
            <if test="usedomain != null ">
                UseDomain =#{usedomain},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Attribute
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询指定行数据 UseDomain新字段 为了兼容性,加入空值的SPU，(UseDomain like '%s%' or UseDomain is null)  -->
    <select id="getListByShow" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatAttributePojo">
        <include refid="selectMatAttributeVo"/>
        where Listshow = 1
         and (Mat_Attribute.useDomain like concat('%', #{usedomain}, '%') or Mat_Attribute.useDomain IS NULL or Mat_Attribute.useDomain = '')
        and Mat_Attribute.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--检查AttrKey在指定租户下的唯一性-->
    <select id="countAttrkey" resultType="int">
        SELECT COUNT(*)
        FROM Mat_Attribute
        WHERE AttrKey = #{attrkey}
          AND Tenantid = #{tid}
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
    </select>
</mapper>

