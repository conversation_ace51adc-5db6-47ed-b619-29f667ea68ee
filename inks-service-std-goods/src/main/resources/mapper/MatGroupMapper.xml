<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatGroupMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatGroupPojo">
        <include refid="selectMatGroupVo"/>
        where Mat_Group.id = #{key}
          and Mat_Group.Tenantid = #{tid}
    </select>
    <sql id="selectMatGroupVo">
        select id,
               id,
               Parentid,
               GroupType,
               GroupCode,
               GroupName,
               RowNum,
               GroupLevel,
               StateCode,
               AllowItem,
               ChildCount,
               Prefix,
               Suffix,
               SnCode,
               Remark,
               CreateBy,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               <PERSON>difyDate,
               GroupSvg,
               BatchMg,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               SkuMark,
               PackSnMark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Tenantid,
               TenantName,
               Revision
        from Mat_Group
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatGroupPojo">
        <include refid="selectMatGroupVo"/>
        where 1 = 1 and Mat_Group.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Group.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null and SearchPojo.parentid  != ''">
            and Mat_Group.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.grouptype != null and SearchPojo.grouptype  != ''">
            and Mat_Group.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
        </if>
        <if test="SearchPojo.groupcode != null and SearchPojo.groupcode  != ''">
            and Mat_Group.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname  != ''">
            and Mat_Group.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode  != ''">
            and Mat_Group.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.prefix != null and SearchPojo.prefix  != ''">
            and Mat_Group.Prefix like concat('%', #{SearchPojo.prefix}, '%')
        </if>
        <if test="SearchPojo.suffix != null and SearchPojo.suffix  != ''">
            and Mat_Group.Suffix like concat('%', #{SearchPojo.suffix}, '%')
        </if>
        <if test="SearchPojo.sncode != null and SearchPojo.sncode  != ''">
            and Mat_Group.SnCode like concat('%', #{SearchPojo.sncode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and Mat_Group.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and Mat_Group.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and Mat_Group.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and Mat_Group.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and Mat_Group.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
            and Mat_Group.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
            and Mat_Group.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
            and Mat_Group.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
            and Mat_Group.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
            or Mat_Group.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.grouptype != null and SearchPojo.grouptype != ''">
            or Mat_Group.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
        </if>
        <if test="SearchPojo.groupcode != null and SearchPojo.groupcode != ''">
            or Mat_Group.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname != ''">
            or Mat_Group.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            or Mat_Group.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.prefix != null and SearchPojo.prefix != ''">
            or Mat_Group.Prefix like concat('%', #{SearchPojo.prefix}, '%')
        </if>
        <if test="SearchPojo.suffix != null and SearchPojo.suffix != ''">
            or Mat_Group.Suffix like concat('%', #{SearchPojo.suffix}, '%')
        </if>
        <if test="SearchPojo.sncode != null and SearchPojo.sncode != ''">
            or Mat_Group.SnCode like concat('%', #{SearchPojo.sncode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or Mat_Group.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Mat_Group.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Mat_Group.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Mat_Group.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Mat_Group.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Mat_Group.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Mat_Group.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Mat_Group.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Mat_Group.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Group(id, Parentid, GroupType, GroupCode, GroupName, RowNum, GroupLevel, StateCode, AllowItem, ChildCount, Prefix, Suffix, SnCode, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, GroupSvg, BatchMg, BatchOnly, SkuMark, PackSnMark, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision)
        values (#{id}, #{parentid}, #{grouptype}, #{groupcode}, #{groupname}, #{rownum}, #{grouplevel}, #{statecode}, #{allowitem}, #{childcount}, #{prefix}, #{suffix}, #{sncode}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{groupsvg}, #{batchmg}, #{batchonly}, #{skumark}, #{packsnmark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Group
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="grouptype != null ">
                GroupType =#{grouptype},
            </if>
            <if test="groupcode != null ">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="grouplevel != null">
                GroupLevel =#{grouplevel},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="allowitem != null">
                AllowItem =#{allowitem},
            </if>
            <if test="childcount != null">
                ChildCount =#{childcount},
            </if>
            <if test="prefix != null ">
                Prefix =#{prefix},
            </if>
            <if test="suffix != null ">
                Suffix =#{suffix},
            </if>
            <if test="sncode != null ">
                SnCode =#{sncode},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="groupsvg != null">
                GroupSvg =#{groupsvg},
            </if>
            <if test="batchmg != null">
                BatchMg =#{batchmg},
            </if>
            <if test="batchonly != null">
                BatchOnly =#{batchonly},
            </if>
            <if test="skumark != null">
                SkuMark =#{skumark},
            </if>
            <if test="packsnmark != null">
                PackSnMark =#{packsnmark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Group
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询子集数据-->
    <select id="getListByParentid" resultType="inks.service.std.goods.domain.pojo.MatGroupPojo">
        <include refid="selectMatGroupVo"/>
        where Mat_Group.Parentid = #{key} and Mat_Group.Tenantid =#{tid}
    </select>
</mapper>

