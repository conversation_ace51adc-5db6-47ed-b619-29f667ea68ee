<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSpectempMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSpectempPojo">
        <include refid="selectMatSpectempVo"/>
        where Mat_SpecTemp.id = #{key} and Mat_SpecTemp.Tenantid=#{tid}
    </select>
    <sql id="selectMatSpectempVo">
         select
id, TempCode, TempName, Operator, Operatorid, ItemJson, Summary, EnabledMark, VersionNum, CreateBy, <PERSON>reate<PERSON>yi<PERSON>, <PERSON><PERSON><PERSON>ate, <PERSON>er, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Mat_SpecTemp
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.goods.domain.pojo.MatSpectempPojo">
        <include refid="selectMatSpectempVo"/>
         where 1 = 1 and Mat_SpecTemp.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Mat_SpecTemp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.tempcode != null ">
   and Mat_SpecTemp.TempCode like concat('%', #{SearchPojo.tempcode}, '%')
</if>
<if test="SearchPojo.tempname != null ">
   and Mat_SpecTemp.TempName like concat('%', #{SearchPojo.tempname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Mat_SpecTemp.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Mat_SpecTemp.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.itemjson != null ">
   and Mat_SpecTemp.ItemJson like concat('%', #{SearchPojo.itemjson}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Mat_SpecTemp.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.versionnum != null ">
   and Mat_SpecTemp.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Mat_SpecTemp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Mat_SpecTemp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Mat_SpecTemp.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Mat_SpecTemp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Mat_SpecTemp.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Mat_SpecTemp.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Mat_SpecTemp.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Mat_SpecTemp.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Mat_SpecTemp.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Mat_SpecTemp.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Mat_SpecTemp.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Mat_SpecTemp.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Mat_SpecTemp.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Mat_SpecTemp.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Mat_SpecTemp.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Mat_SpecTemp.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Mat_SpecTemp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.tempcode != null ">
   or Mat_SpecTemp.TempCode like concat('%', #{SearchPojo.tempcode}, '%')
</if>
<if test="SearchPojo.tempname != null ">
   or Mat_SpecTemp.TempName like concat('%', #{SearchPojo.tempname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Mat_SpecTemp.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Mat_SpecTemp.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.itemjson != null ">
   or Mat_SpecTemp.ItemJson like concat('%', #{SearchPojo.itemjson}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Mat_SpecTemp.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.versionnum != null ">
   or Mat_SpecTemp.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Mat_SpecTemp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Mat_SpecTemp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Mat_SpecTemp.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Mat_SpecTemp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Mat_SpecTemp.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Mat_SpecTemp.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Mat_SpecTemp.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Mat_SpecTemp.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Mat_SpecTemp.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Mat_SpecTemp.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Mat_SpecTemp.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Mat_SpecTemp.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Mat_SpecTemp.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Mat_SpecTemp.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Mat_SpecTemp.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Mat_SpecTemp.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Mat_SpecTemp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Mat_SpecTemp(id, TempCode, TempName, Operator, Operatorid, ItemJson, Summary, EnabledMark, VersionNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{tempcode}, #{tempname}, #{operator}, #{operatorid}, #{itemjson}, #{summary}, #{enabledmark}, #{versionnum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SpecTemp
        <set>
            <if test="tempcode != null ">
                TempCode =#{tempcode},
            </if>
            <if test="tempname != null ">
                TempName =#{tempname},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="itemjson != null ">
                ItemJson =#{itemjson},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="versionnum != null ">
                VersionNum =#{versionnum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Mat_SpecTemp where id = #{key} and Tenantid=#{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_SpecTemp SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
</mapper>

