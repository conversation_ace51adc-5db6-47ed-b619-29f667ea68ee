<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatEcnMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatEcnPojo">
        SELECT
            Mat_Ecn.id,
            Mat_Ecn.RefNo,
            Mat_Ecn.BillDate,
            Mat_Ecn.BillType,
            Mat_Ecn.BillTitle,
            Mat_Ecn.Causation,
            Mat_Ecn.EclCode,
            Mat_Ecn.Description,
            Mat_Ecn.Goodsid,
            Mat_Ecn.ItemCode,
            Mat_Ecn.ItemName,
            Mat_Ecn.ItemSpec,
            Mat_Ecn.ItemUnit,
            Mat_Ecn.<PERSON>id,
            Mat_Ecn.GoodsUid<PERSON>ld,
            Mat_Ecn.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            Mat_Ecn.<PERSON>s<PERSON>,
            Mat_<PERSON>c<PERSON>.<PERSON><PERSON><PERSON><PERSON>,
            Mat_Ecn.Dis<PERSON>ose<PERSON><PERSON>,
            Mat_Ecn.DisPoseWk,
            Mat_Ecn.PhotoOld,
            Mat_Ecn.PhotoNew,
            Mat_Ecn.DetailJson,
            Mat_Ecn.Remark,
            Mat_Ecn.CreateBy,
            Mat_Ecn.CreateByid,
            Mat_Ecn.CreateDate,
            Mat_Ecn.Lister,
            Mat_Ecn.Listerid,
            Mat_Ecn.ModifyDate,
            Mat_Ecn.Assessor,
            Mat_Ecn.Assessorid,
            Mat_Ecn.AssessDate,
            Mat_Ecn.Custom1,
            Mat_Ecn.Custom2,
            Mat_Ecn.Custom3,
            Mat_Ecn.Custom4,
            Mat_Ecn.Custom5,
            Mat_Ecn.Custom6,
            Mat_Ecn.Custom7,
            Mat_Ecn.Custom8,
            Mat_Ecn.Custom9,
            Mat_Ecn.Custom10,
            Mat_Ecn.Tenantid,
            Mat_Ecn.Revision,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            App_Workgroup
                RIGHT JOIN Mat_Ecn ON Mat_Ecn.Groupid = App_Workgroup.id
                LEFT JOIN Mat_Goods ON Mat_Ecn.Goodsid = Mat_Goods.id

        where Mat_Ecn.id = #{key}
          and Mat_Ecn.Tenantid = #{tid}
    </select>
    <sql id="selectMatEcnVo">
        SELECT
            Mat_Ecn.id,
            Mat_Ecn.RefNo,
            Mat_Ecn.BillDate,
            Mat_Ecn.BillType,
            Mat_Ecn.BillTitle,
            Mat_Ecn.Causation,
            Mat_Ecn.EclCode,
            Mat_Ecn.Description,
            Mat_Ecn.Goodsid,
            Mat_Ecn.ItemCode,
            Mat_Ecn.ItemName,
            Mat_Ecn.ItemSpec,
            Mat_Ecn.ItemUnit,
            Mat_Ecn.Groupid,
            Mat_Ecn.GoodsUidOld,
            Mat_Ecn.GoodsUidNew,
            Mat_Ecn.DisPoseOld,
            Mat_Ecn.DisPoseNew,
            Mat_Ecn.DisPoseSto,
            Mat_Ecn.DisPoseWk,
            Mat_Ecn.PhotoOld,
            Mat_Ecn.PhotoNew,
            Mat_Ecn.DetailJson,
            Mat_Ecn.Remark,
            Mat_Ecn.CreateBy,
            Mat_Ecn.CreateByid,
            Mat_Ecn.CreateDate,
            Mat_Ecn.Lister,
            Mat_Ecn.Listerid,
            Mat_Ecn.ModifyDate,
            Mat_Ecn.Assessor,
            Mat_Ecn.Assessorid,
            Mat_Ecn.AssessDate,
            Mat_Ecn.Custom1,
            Mat_Ecn.Custom2,
            Mat_Ecn.Custom3,
            Mat_Ecn.Custom4,
            Mat_Ecn.Custom5,
            Mat_Ecn.Custom6,
            Mat_Ecn.Custom7,
            Mat_Ecn.Custom8,
            Mat_Ecn.Custom9,
            Mat_Ecn.Custom10,
            Mat_Ecn.Tenantid,
            Mat_Ecn.Revision,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            App_Workgroup
                RIGHT JOIN Mat_Ecn ON Mat_Ecn.Groupid = App_Workgroup.id
                LEFT JOIN Mat_Goods ON Mat_Ecn.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatEcnPojo">
        <include refid="selectMatEcnVo"/>
        where 1 = 1 and Mat_Ecn.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Ecn.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno  != ''">
            and Mat_Ecn.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype  != ''">
            and Mat_Ecn.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle  != ''">
            and Mat_Ecn.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.causation != null and SearchPojo.causation  != ''">
            and Mat_Ecn.Causation like concat('%', #{SearchPojo.causation}, '%')
        </if>
        <if test="SearchPojo.eclcode != null and SearchPojo.eclcode  != ''">
            and Mat_Ecn.EclCode like concat('%', #{SearchPojo.eclcode}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description  != ''">
            and Mat_Ecn.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid  != ''">
            and Mat_Ecn.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode  != ''">
            and Mat_Ecn.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname  != ''">
            and Mat_Ecn.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec  != ''">
            and Mat_Ecn.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit  != ''">
            and Mat_Ecn.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid  != ''">
            and Mat_Ecn.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.goodsuidold != null and SearchPojo.goodsuidold  != ''">
            and Mat_Ecn.GoodsUidOld like concat('%', #{SearchPojo.goodsuidold}, '%')
        </if>
        <if test="SearchPojo.goodsuidnew != null and SearchPojo.goodsuidnew  != ''">
            and Mat_Ecn.GoodsUidNew like concat('%', #{SearchPojo.goodsuidnew}, '%')
        </if>
        <if test="SearchPojo.disposeold != null and SearchPojo.disposeold  != ''">
            and Mat_Ecn.DisPoseOld like concat('%', #{SearchPojo.disposeold}, '%')
        </if>
        <if test="SearchPojo.disposenew != null and SearchPojo.disposenew  != ''">
            and Mat_Ecn.DisPoseNew like concat('%', #{SearchPojo.disposenew}, '%')
        </if>
        <if test="SearchPojo.disposesto != null and SearchPojo.disposesto  != ''">
            and Mat_Ecn.DisPoseSto like concat('%', #{SearchPojo.disposesto}, '%')
        </if>
        <if test="SearchPojo.disposewk != null and SearchPojo.disposewk  != ''">
            and Mat_Ecn.DisPoseWk like concat('%', #{SearchPojo.disposewk}, '%')
        </if>
        <if test="SearchPojo.photoold != null and SearchPojo.photoold  != ''">
            and Mat_Ecn.PhotoOld like concat('%', #{SearchPojo.photoold}, '%')
        </if>
        <if test="SearchPojo.photonew != null and SearchPojo.photonew  != ''">
            and Mat_Ecn.PhotoNew like concat('%', #{SearchPojo.photonew}, '%')
        </if>
        <if test="SearchPojo.detailjson != null and SearchPojo.detailjson  != ''">
            and Mat_Ecn.DetailJson like concat('%', #{SearchPojo.detailjson}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and Mat_Ecn.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and Mat_Ecn.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and Mat_Ecn.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and Mat_Ecn.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and Mat_Ecn.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor  != ''">
            and Mat_Ecn.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid  != ''">
            and Mat_Ecn.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
            and Mat_Ecn.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
            and Mat_Ecn.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
            and Mat_Ecn.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
            and Mat_Ecn.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
            and Mat_Ecn.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6  != ''">
            and Mat_Ecn.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7  != ''">
            and Mat_Ecn.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8  != ''">
            and Mat_Ecn.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9  != ''">
            and Mat_Ecn.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10  != ''">
            and Mat_Ecn.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Mat_Ecn.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Mat_Ecn.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Mat_Ecn.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.causation != null and SearchPojo.causation != ''">
            or Mat_Ecn.Causation like concat('%', #{SearchPojo.causation}, '%')
        </if>
        <if test="SearchPojo.eclcode != null and SearchPojo.eclcode != ''">
            or Mat_Ecn.EclCode like concat('%', #{SearchPojo.eclcode}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description != ''">
            or Mat_Ecn.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            or Mat_Ecn.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            or Mat_Ecn.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            or Mat_Ecn.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            or Mat_Ecn.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            or Mat_Ecn.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            or Mat_Ecn.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.goodsuidold != null and SearchPojo.goodsuidold != ''">
            or Mat_Ecn.GoodsUidOld like concat('%', #{SearchPojo.goodsuidold}, '%')
        </if>
        <if test="SearchPojo.goodsuidnew != null and SearchPojo.goodsuidnew != ''">
            or Mat_Ecn.GoodsUidNew like concat('%', #{SearchPojo.goodsuidnew}, '%')
        </if>
        <if test="SearchPojo.disposeold != null and SearchPojo.disposeold != ''">
            or Mat_Ecn.DisPoseOld like concat('%', #{SearchPojo.disposeold}, '%')
        </if>
        <if test="SearchPojo.disposenew != null and SearchPojo.disposenew != ''">
            or Mat_Ecn.DisPoseNew like concat('%', #{SearchPojo.disposenew}, '%')
        </if>
        <if test="SearchPojo.disposesto != null and SearchPojo.disposesto != ''">
            or Mat_Ecn.DisPoseSto like concat('%', #{SearchPojo.disposesto}, '%')
        </if>
        <if test="SearchPojo.disposewk != null and SearchPojo.disposewk != ''">
            or Mat_Ecn.DisPoseWk like concat('%', #{SearchPojo.disposewk}, '%')
        </if>
        <if test="SearchPojo.photoold != null and SearchPojo.photoold != ''">
            or Mat_Ecn.PhotoOld like concat('%', #{SearchPojo.photoold}, '%')
        </if>
        <if test="SearchPojo.photonew != null and SearchPojo.photonew != ''">
            or Mat_Ecn.PhotoNew like concat('%', #{SearchPojo.photonew}, '%')
        </if>
        <if test="SearchPojo.detailjson != null and SearchPojo.detailjson != ''">
            or Mat_Ecn.DetailJson like concat('%', #{SearchPojo.detailjson}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or Mat_Ecn.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Mat_Ecn.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Mat_Ecn.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Mat_Ecn.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Mat_Ecn.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or Mat_Ecn.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            or Mat_Ecn.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Mat_Ecn.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Mat_Ecn.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Mat_Ecn.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Mat_Ecn.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Mat_Ecn.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Mat_Ecn.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Mat_Ecn.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Mat_Ecn.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Mat_Ecn.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Mat_Ecn.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Ecn(id, RefNo, BillDate, BillType, BillTitle, Causation, EclCode, Description, Goodsid,
                            ItemCode, ItemName, ItemSpec, ItemUnit, Groupid, GoodsUidOld, GoodsUidNew, DisPoseOld,
                            DisPoseNew, DisPoseSto, DisPoseWk, PhotoOld, PhotoNew, DetailJson, Remark, CreateBy,
                            CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate,
                            Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                            Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{causation}, #{eclcode}, #{description},
                #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{groupid}, #{goodsuidold},
                #{goodsuidnew}, #{disposeold}, #{disposenew}, #{disposesto}, #{disposewk}, #{photoold}, #{photonew},
                #{detailjson}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Ecn
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="causation != null ">
                Causation =#{causation},
            </if>
            <if test="eclcode != null ">
                EclCode =#{eclcode},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="goodsuidold != null ">
                GoodsUidOld =#{goodsuidold},
            </if>
            <if test="goodsuidnew != null ">
                GoodsUidNew =#{goodsuidnew},
            </if>
            <if test="disposeold != null ">
                DisPoseOld =#{disposeold},
            </if>
            <if test="disposenew != null ">
                DisPoseNew =#{disposenew},
            </if>
            <if test="disposesto != null ">
                DisPoseSto =#{disposesto},
            </if>
            <if test="disposewk != null ">
                DisPoseWk =#{disposewk},
            </if>
            <if test="photoold != null ">
                PhotoOld =#{photoold},
            </if>
            <if test="photonew != null ">
                PhotoNew =#{photonew},
            </if>
            <if test="detailjson != null ">
                DetailJson =#{detailjson},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Ecn
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_Ecn
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <select id="getListByGoodsid" resultType="inks.service.std.goods.domain.pojo.MatEcnPojo">
        SELECT DISTINCT e.*
        FROM Mat_Ecn e
        JOIN Mat_Ecn sub ON e.EclCode = sub.EclCode
        WHERE sub.Goodsid = #{goodsid}
        AND e.Tenantid = #{tid}
    </select>


</mapper>

