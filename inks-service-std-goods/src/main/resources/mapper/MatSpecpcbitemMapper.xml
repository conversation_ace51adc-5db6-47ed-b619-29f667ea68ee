<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSpecpcbitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbitemPojo">
        <include refid="selectMatSpecpcbitemVo"/>
        where Mat_SpecPcbItem.id = #{key} and Mat_SpecPcbItem.Tenantid=#{tid}
    </select>
    <sql id="selectMatSpecpcbitemVo">
         select
id, Pid, Wpid, WpCode, WpName, Description, DetailJson, RowNum, AreaMult, FlowCode, ToolsCode, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Mat_SpecPcbItem
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbitemPojo">
        <include refid="selectMatSpecpcbitemVo"/>
        where Mat_SpecPcbItem.Pid = #{Pid} and Mat_SpecPcbItem.Tenantid=#{tid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbitemPojo">
        <include refid="selectMatSpecpcbitemVo"/>
         where 1 = 1 and Mat_SpecPcbItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Mat_SpecPcbItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Mat_SpecPcbItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
   and Mat_SpecPcbItem.wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
   and Mat_SpecPcbItem.wpcode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
   and Mat_SpecPcbItem.wpname like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.description != null and SearchPojo.description != ''">
   and Mat_SpecPcbItem.description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.detailjson != null and SearchPojo.detailjson != ''">
   and Mat_SpecPcbItem.detailjson like concat('%', #{SearchPojo.detailjson}, '%')
</if>
<if test="SearchPojo.flowcode != null and SearchPojo.flowcode != ''">
   and Mat_SpecPcbItem.flowcode like concat('%', #{SearchPojo.flowcode}, '%')
</if>
<if test="SearchPojo.toolscode != null and SearchPojo.toolscode != ''">
   and Mat_SpecPcbItem.toolscode like concat('%', #{SearchPojo.toolscode}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Mat_SpecPcbItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Mat_SpecPcbItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Mat_SpecPcbItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Mat_SpecPcbItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Mat_SpecPcbItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Mat_SpecPcbItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Mat_SpecPcbItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Mat_SpecPcbItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Mat_SpecPcbItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Mat_SpecPcbItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Mat_SpecPcbItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   and Mat_SpecPcbItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Mat_SpecPcbItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.wpid != null and SearchPojo.wpid != ''">
   or Mat_SpecPcbItem.Wpid like concat('%', #{SearchPojo.wpid}, '%')
</if>
<if test="SearchPojo.wpcode != null and SearchPojo.wpcode != ''">
   or Mat_SpecPcbItem.WpCode like concat('%', #{SearchPojo.wpcode}, '%')
</if>
<if test="SearchPojo.wpname != null and SearchPojo.wpname != ''">
   or Mat_SpecPcbItem.WpName like concat('%', #{SearchPojo.wpname}, '%')
</if>
<if test="SearchPojo.description != null and SearchPojo.description != ''">
   or Mat_SpecPcbItem.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.detailjson != null and SearchPojo.detailjson != ''">
   or Mat_SpecPcbItem.DetailJson like concat('%', #{SearchPojo.detailjson}, '%')
</if>
<if test="SearchPojo.flowcode != null and SearchPojo.flowcode != ''">
   or Mat_SpecPcbItem.FlowCode like concat('%', #{SearchPojo.flowcode}, '%')
</if>
<if test="SearchPojo.toolscode != null and SearchPojo.toolscode != ''">
   or Mat_SpecPcbItem.ToolsCode like concat('%', #{SearchPojo.toolscode}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Mat_SpecPcbItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Mat_SpecPcbItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Mat_SpecPcbItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Mat_SpecPcbItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Mat_SpecPcbItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Mat_SpecPcbItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Mat_SpecPcbItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Mat_SpecPcbItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Mat_SpecPcbItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Mat_SpecPcbItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Mat_SpecPcbItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   or Mat_SpecPcbItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Mat_SpecPcbItem(id, Pid, Wpid, WpCode, WpName, Description, DetailJson, RowNum, AreaMult, FlowCode, ToolsCode, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{wpid}, #{wpcode}, #{wpname}, #{description}, #{detailjson}, #{rownum}, #{areamult}, #{flowcode}, #{toolscode}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SpecPcbItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="wpid != null ">
                Wpid = #{wpid},
            </if>
            <if test="wpcode != null ">
                WpCode = #{wpcode},
            </if>
            <if test="wpname != null ">
                WpName = #{wpname},
            </if>
            <if test="description != null ">
                Description = #{description},
            </if>
            <if test="detailjson != null ">
                DetailJson = #{detailjson},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="areamult != null">
                AreaMult = #{areamult},
            </if>
            <if test="flowcode != null ">
                FlowCode = #{flowcode},
            </if>
            <if test="toolscode != null ">
                ToolsCode = #{toolscode},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Mat_SpecPcbItem where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getListVO" resultType="inks.service.std.goods.domain.pojo.MatSpecpcbitemVo">
        select WpCode,
               WpName,
               Description,
               FlowCode,
               ToolsCode,
               Remark
        from Mat_SpecPcbItem
        where Mat_SpecPcbItem.Pid = #{Pid}
          and Mat_SpecPcbItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getWpidByWpNameIgnoreCase" resultType="java.lang.String">
        select id
        from Wk_Process
        where lower(WpName) = lower(#{wpname})
          and Tenantid = #{tid}
        limit 1
    </select>
</mapper>

