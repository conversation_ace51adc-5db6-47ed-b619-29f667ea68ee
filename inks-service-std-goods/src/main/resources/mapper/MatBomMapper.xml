<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatBomMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatBomPojo">
        SELECT Mat_Bom.id,
               Mat_Bom.Goodsid,
               Mat_Bom.ItemCode,
               Mat_Bom.ItemName,
               Mat_Bom.ItemSpec,
               Mat_Bom.ItemUnit,
               Mat_Bom.QuotaHour,
               Mat_Bom.ProGroupid,
               Mat_Bom.MinTime,
               Mat_Bom.Summary,
               Mat_Bom.EnabledMark,
               Mat_Bom.VersionNum,
               Mat_Bom.DeleteMark,
               Mat_Bom.DeleteListerid,
               Mat_<PERSON><PERSON>.DeleteLister,
               Mat_<PERSON><PERSON>.DeleteDate,
               <PERSON>_<PERSON><PERSON>.<PERSON>reate<PERSON>y,
               <PERSON>_<PERSON><PERSON>.<PERSON>,
               <PERSON>_<PERSON><PERSON>.CreateDate,
               <PERSON>_<PERSON><PERSON>.Lister,
               Mat_<PERSON><PERSON>.Listerid,
               Mat_Bom.ModifyDate,
               Mat_Bom.Assessor,
               Mat_Bom.Assessorid,
               Mat_Bom.AssessDate,
               Mat_Bom.ItemCount,
               Mat_Bom.PrintCount,
               Mat_Bom.Custom1,
               Mat_Bom.Custom2,
               Mat_Bom.Custom3,
               Mat_Bom.Custom4,
               Mat_Bom.Custom5,
               Mat_Bom.Custom6,
               Mat_Bom.Custom7,
               Mat_Bom.Custom8,
               Mat_Bom.Custom9,
               Mat_Bom.Custom10,
               Mat_Bom.Tenantid,
               Mat_Bom.TenantName,
               Mat_Bom.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Material as goodsmaterial
        FROM Mat_Bom
                 LEFT JOIN Mat_Goods ON Mat_Bom.Goodsid = Mat_Goods.id
        where Mat_Bom.id = #{key}
          and Mat_Bom.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Mat_Bom.id,
               Mat_Bom.Goodsid,
               Mat_Bom.ItemCode,
               Mat_Bom.ItemName,
               Mat_Bom.ItemSpec,
               Mat_Bom.ItemUnit,
               Mat_Bom.QuotaHour,
               Mat_Bom.ProGroupid,
               Mat_Bom.MinTime,
               Mat_Bom.Summary,
               Mat_Bom.EnabledMark,
               Mat_Bom.VersionNum,
               Mat_Bom.DeleteMark,
               Mat_Bom.DeleteListerid,
               Mat_Bom.DeleteLister,
               Mat_Bom.DeleteDate,
               Mat_Bom.CreateBy,
               Mat_Bom.CreateByid,
               Mat_Bom.CreateDate,
               Mat_Bom.Lister,
               Mat_Bom.Listerid,
               Mat_Bom.ModifyDate,
               Mat_Bom.Assessor,
               Mat_Bom.Assessorid,
               Mat_Bom.AssessDate,
               Mat_Bom.ItemCount,
               Mat_Bom.PrintCount,
               Mat_Bom.Custom1,
               Mat_Bom.Custom2,
               Mat_Bom.Custom3,
               Mat_Bom.Custom4,
               Mat_Bom.Custom5,
               Mat_Bom.Custom6,
               Mat_Bom.Custom7,
               Mat_Bom.Custom8,
               Mat_Bom.Custom9,
               Mat_Bom.Custom10,
               Mat_Bom.Tenantid,
               Mat_Bom.TenantName,
               Mat_Bom.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Material as goodsmaterial,
               Mat_Goods.Bomid
        FROM Mat_Bom
                 LEFT JOIN Mat_Goods ON Mat_Bom.Goodsid = Mat_Goods.id
    </sql>
    <sql id="selectdetailVo">
        SELECT Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Material as goodsmaterial,
               Mat_BomItem.id,
               Mat_BomItem.Pid,
               Mat_BomItem.Goodsid,
               Mat_BomItem.ItemCode,
               Mat_BomItem.ItemName,
               Mat_BomItem.ItemSpec,
               Mat_BomItem.ItemUnit,
               Mat_BomItem.MainQty,
               Mat_BomItem.SubQty,
               Mat_BomItem.LossRate,
               Mat_BomItem.AttrCode,
               Mat_BomItem.FlowCode,
               Mat_BomItem.Description,
               Mat_BomItem.ItemLabel,
               Mat_BomItem.Parentid,
               Mat_BomItem.ParentGoodsid,
               Mat_BomItem.RowNum,
               Mat_BomItem.Remark,
               Mat_BomItem.SubLossQty,
               Mat_BomItem.MatType,
               Mat_BomItem.Custom1,
               Mat_BomItem.Custom2,
               Mat_BomItem.Custom3,
               Mat_BomItem.Custom4,
               Mat_BomItem.Custom5,
               Mat_BomItem.Custom6,
               Mat_BomItem.Custom7,
               Mat_BomItem.Custom8,
               Mat_BomItem.Custom9,
               Mat_BomItem.Custom10,
               Mat_BomItem.Tenantid,
               Mat_Bom.TenantName,
               Mat_BomItem.Revision,
               Mat_Bom.ItemCode AS MainItemCode,
               Mat_Bom.ItemName AS MainItemName,
               Mat_Bom.ItemSpec AS MainItemSpec,
               Mat_Bom.ItemUnit AS MainItemUnit
        FROM Mat_Goods
                 RIGHT JOIN Mat_BomItem ON Mat_BomItem.Goodsid = Mat_Goods.id
                 LEFT JOIN Mat_Bom ON Mat_BomItem.Pid = Mat_Bom.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatBomitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_BomItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Bom.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_Bom.goodsid = #{SearchPojo.goodsid}
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_Bom.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_Bom.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_Bom.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_Bom.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.progroupid != null and SearchPojo.progroupid != ''">
            and Mat_Bom.progroupid like concat('%', #{SearchPojo.progroupid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_Bom.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.versionnum != null and SearchPojo.versionnum != ''">
            and Mat_Bom.versionnum like concat('%', #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null and SearchPojo.deletelisterid != ''">
            and Mat_Bom.deletelisterid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            and Mat_Bom.deletelister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_Bom.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_Bom.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_Bom.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_Bom.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Mat_Bom.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Mat_Bom.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_Bom.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_Bom.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_Bom.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_Bom.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_Bom.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_Bom.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_Bom.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_Bom.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_Bom.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_Bom.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.itemgoodsid != null and SearchPojo.itemgoodsid != ''">
            and Mat_BomItem.goodsid = #{SearchPojo.itemgoodsid},
        </if>
        <if test="SearchPojo.parentgoodsid != null and SearchPojo.parentgoodsid != ''">
            and Mat_BomItem.parentgoodsid = #{SearchPojo.parentgoodsid}
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_Bom.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_Bom.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_Bom.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_Bom.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_Bom.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.progroupid != null and SearchPojo.progroupid != ''">
                or Mat_Bom.ProGroupid like concat('%', #{SearchPojo.progroupid}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Mat_Bom.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.versionnum != null and SearchPojo.versionnum != ''">
                or Mat_Bom.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null and SearchPojo.deletelisterid != ''">
                or Mat_Bom.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
                or Mat_Bom.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_Bom.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_Bom.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_Bom.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_Bom.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Mat_Bom.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Mat_Bom.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_Bom.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_Bom.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_Bom.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_Bom.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_Bom.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_Bom.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_Bom.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_Bom.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_Bom.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_Bom.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatBomPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_Bom.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Bom.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_Bom.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_Bom.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_Bom.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_Bom.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_Bom.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.progroupid != null and SearchPojo.progroupid != ''">
            and Mat_Bom.ProGroupid like concat('%', #{SearchPojo.progroupid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_Bom.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.versionnum != null and SearchPojo.versionnum != ''">
            and Mat_Bom.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null and SearchPojo.deletelisterid != ''">
            and Mat_Bom.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            and Mat_Bom.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_Bom.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_Bom.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_Bom.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_Bom.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Mat_Bom.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Mat_Bom.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_Bom.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_Bom.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_Bom.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_Bom.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_Bom.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_Bom.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_Bom.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_Bom.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_Bom.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_Bom.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.goodsuid != null ">
            and Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null ">
            and Mat_Goods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null ">
            and Mat_Goods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_Bom.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_Bom.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_Bom.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_Bom.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_Bom.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.progroupid != null and SearchPojo.progroupid != ''">
                or Mat_Bom.ProGroupid like concat('%', #{SearchPojo.progroupid}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Mat_Bom.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.versionnum != null and SearchPojo.versionnum != ''">
                or Mat_Bom.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null and SearchPojo.deletelisterid != ''">
                or Mat_Bom.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
                or Mat_Bom.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_Bom.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_Bom.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_Bom.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_Bom.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Mat_Bom.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Mat_Bom.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_Bom.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_Bom.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_Bom.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_Bom.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_Bom.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_Bom.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_Bom.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_Bom.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_Bom.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_Bom.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.goodsuid != null ">
                or Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
            </if>
            <if test="SearchPojo.goodsname != null ">
                or Mat_Goods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
            </if>
            <if test="SearchPojo.goodsspec != null ">
                or Mat_Goods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Bom(id, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, QuotaHour, ProGroupid, MinTime,
                            Summary, EnabledMark, VersionNum, DeleteMark, DeleteListerid, DeleteLister, DeleteDate,
                            CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid,
                            AssessDate, ItemCount,PrintCount,Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
                            Custom10, Tenantid,TenantName, Revision)
        values (#{id}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quotahour}, #{progroupid},
                #{mintime}, #{summary}, #{enabledmark}, #{versionnum}, #{deletemark}, #{deletelisterid},
                #{deletelister}, #{deletedate}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{assessor}, #{assessorid}, #{assessdate},#{itemcount},#{printcount}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},#{tenantname},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Bom
        <set>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="quotahour != null">
                QuotaHour =#{quotahour},
            </if>
            <if test="progroupid != null ">
                ProGroupid =#{progroupid},
            </if>
            <if test="mintime != null">
                MinTime =#{mintime},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="versionnum != null ">
                VersionNum =#{versionnum},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelisterid != null ">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="itemcount != null ">
                ItemCount =#{itemcount},
            </if>
            <if test="printcount != null ">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Bom
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_Bom
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <update id="approvalBatch">
        update Mat_Bom
        SET Assessor   = #{loginUser.realname},
            Assessorid = #{loginUser.userid},
            AssessDate = #{date},
            Revision   = Revision + 1
        where id IN
        <foreach collection="keys" item="key" open="(" close=")" separator=",">
            #{key}
        </foreach>
        and Tenantid = #{loginUser.tenantid}
    </update>



    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatBomPojo">
        select
        id
        from Mat_BomItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <!--查询单个-->
    <select id="getEntityByGoodsid" resultType="inks.service.std.goods.domain.pojo.MatBomPojo">
        SELECT Mat_Bom.id,
               Mat_Bom.Goodsid,
               Mat_Bom.ItemCode,
               Mat_Bom.ItemName,
               Mat_Bom.ItemSpec,
               Mat_Bom.ItemUnit,
               Mat_Bom.QuotaHour,
               Mat_Bom.ProGroupid,
               Mat_Bom.MinTime,
               Mat_Bom.Summary,
               Mat_Bom.EnabledMark,
               Mat_Bom.VersionNum,
               Mat_Bom.DeleteMark,
               Mat_Bom.DeleteListerid,
               Mat_Bom.DeleteLister,
               Mat_Bom.DeleteDate,
               Mat_Bom.CreateBy,
               Mat_Bom.CreateByid,
               Mat_Bom.CreateDate,
               Mat_Bom.Lister,
               Mat_Bom.Listerid,
               Mat_Bom.ModifyDate,
               Mat_Bom.Assessor,
               Mat_Bom.Assessorid,
               Mat_Bom.AssessDate,
               Mat_Bom.ItemCount,
               Mat_Bom.PrintCount,
               Mat_Bom.Custom1,
               Mat_Bom.Custom2,
               Mat_Bom.Custom3,
               Mat_Bom.Custom4,
               Mat_Bom.Custom5,
               Mat_Bom.Custom6,
               Mat_Bom.Custom7,
               Mat_Bom.Custom8,
               Mat_Bom.Custom9,
               Mat_Bom.Custom10,
               Mat_Bom.Tenantid,
               Mat_Bom.TenantName,
               Mat_Bom.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Bomid
        FROM Mat_Bom
                 LEFT JOIN Mat_Goods ON Mat_Bom.Goodsid = Mat_Goods.id
        where Mat_Bom.Goodsid = #{key}
          and Mat_Bom.Tenantid = #{tid}
    </select>


    <select id="getBomidByGoodsid" resultType="java.lang.String">
        select id from Mat_Bom where Goodsid=#{goodsid} and Tenantid = #{tenantid}
    </select>

    <!--查询单个-->
    <select id="getListByItemGoodsid" resultType="inks.service.std.goods.domain.pojo.MatBomorderitemdetailPojo">
        SELECT Mat_BomItem.id,
               Mat_BomItem.Pid,
               Mat_Bom.Goodsid,
               Mat_BomItem.Goodsid AS ItemGoodsid,
               Mat_BomItem.ItemCode,
               Mat_BomItem.ItemName,
               Mat_BomItem.ItemSpec,
               Mat_BomItem.ItemUnit,
               Mat_BomItem.MainQty,
               Mat_BomItem.SubQty,
               Mat_BomItem.LossRate,
               Mat_BomItem.AttrCode,
               Mat_BomItem.FlowCode,
               Mat_BomItem.Description,
               Mat_BomItem.ItemLabel,
               Mat_BomItem.Parentid,
               Mat_BomItem.ParentGoodsid,
               Mat_BomItem.RowNum,
               Mat_BomItem.SubLossQty,
               Mat_BomItem.Remark,
               Mat_BomItem.Custom1,
               Mat_BomItem.Custom2,
               Mat_BomItem.Custom3,
               Mat_BomItem.Custom4,
               Mat_BomItem.Custom5,
               Mat_BomItem.Custom6,
               Mat_BomItem.Custom7,
               Mat_BomItem.Custom8,
               Mat_BomItem.Custom9,
               Mat_BomItem.Custom10,
               Mat_BomItem.Tenantid,
               Mat_BomItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Bom.CreateBy,
               Mat_Bom.Lister,
               Mat_Bom.Assessor,
               '标准BOM'           as bomtype
        FROM Mat_Bom
                 RIGHT JOIN Mat_BomItem ON Mat_BomItem.Pid = Mat_Bom.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_Bom.Goodsid
        where Mat_BomItem.Goodsid = #{key}
          and Mat_BomItem.Tenantid = #{tid}
    </select>
    <select id="findBomidByGoodsid" resultType="java.lang.String">
        select id from Mat_Bom where Goodsid=#{key} and Tenantid = #{tid}
    </select>

    <!--    刷新发货完成数 Eric 20220415-->
    <update id="updateGoodsBomid">
        update Mat_Goods
        SET Bomid =#{bomid}
        where id = #{key}
          and Tenantid = #{tid}
    </update>


</mapper>

