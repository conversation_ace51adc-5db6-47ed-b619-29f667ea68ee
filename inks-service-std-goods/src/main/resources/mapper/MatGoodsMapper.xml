<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatGoodsMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        where Mat_Goods.id = #{key}
          and Mat_Goods.Tenantid = #{tid}
    </select>
    <sql id="selectMatGoodsVo">
        SELECT Mat_Goods.id,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.GoodsState,
               Mat_Goods.GoodsPinyin,
               Mat_Goods.VersionNum,
               Mat_Goods.Material,
               Mat_Goods.Surface,
               Mat_Goods.BarCode,
               Mat_Goods.SafeStock,
               Mat_Goods.InPrice,
               Mat_Goods.OutPrice,
               Mat_Goods.Groupid,
               Mat_Goods.FileGuid,
               Mat_Goods.Drawing,
               Mat_Goods.Storeid,
               Mat_Goods.StoreListName,
               Mat_Goods.StoreListGuid,
               Mat_Goods.IvQuantity,
               Mat_Goods.AgePrice,
               Mat_Goods.UidGroupGuid,
               Mat_Goods.UidGroupCode,
               Mat_Goods.UidGroupName,
               Mat_Goods.UidGroupNum,
               Mat_Goods.Partid,
               Mat_Goods.Pid,
               Mat_Goods.PUid,
               Mat_Goods.EnabledMark,
               Mat_Goods.GoodsPhoto1,
               Mat_Goods.GoodsPhoto2,
               Mat_Goods.Remark,
               Mat_Goods.CreateBy,
               Mat_Goods.CreateByid,
               Mat_Goods.CreateDate,
               Mat_Goods.Lister,
               Mat_Goods.Listerid,
               Mat_Goods.ModifyDate,
               Mat_Goods.DeleteMark,
               Mat_Goods.DeleteLister,
               Mat_Goods.DeleteListerid,
               Mat_Goods.DeleteDate,
               Mat_Goods.BatchMg,
               Mat_Goods.BatchOnly,
               Mat_Goods.SkuMark,
               Mat_Goods.PackSnMark,
               Mat_Goods.VirtualItem,
               Mat_Goods.Bomid,
               Mat_Goods.QuickCode,
               Mat_Goods.BrandName,
               Mat_Goods.BuyRemQty,
               Mat_Goods.WkWsRemQty,
               Mat_Goods.WkScRemQty,
               Mat_Goods.BusRemQty,
               Mat_Goods.MrpRemQty,
               Mat_Goods.RequRemQty,
               Mat_Goods.AlertsQty,
               Mat_Goods.IntQtyMark,
               Mat_Goods.WeightQty,
               Mat_Goods.WeightUnit,
               Mat_Goods.LengthQty,
               Mat_Goods.LengthUnit,
               Mat_Goods.AreaQty,
               Mat_Goods.AreaUnit,
               Mat_Goods.VolumeQty,
               Mat_Goods.VolumeUnit,
               Mat_Goods.PackQty,
               Mat_Goods.PackUnit,
               Mat_Goods.MatQtyUnit,
               Mat_Goods.OverflowQty,
               Mat_Goods.Taxrate,
               Mat_Goods.InTaxPrice,
               Mat_Goods.OutTaxPrice,
               Mat_Goods.Specid,
               Mat_Goods.AliasName,
               Mat_Goods.PcsX,
               Mat_Goods.PcsY,
               Mat_Goods.SizeUnit,
               Mat_Goods.SetX,
               Mat_Goods.SetY,
               Mat_Goods.Set2Pcs,
               Mat_Goods.PnlX,
               Mat_Goods.PnlY,
               Mat_Goods.Pnl2Pcs,
               Mat_Goods.ExpiMark,
               Mat_Goods.DefExpiDay,
               Mat_Goods.Inspid,
               Mat_Goods.UnitJson,
               Mat_Goods.CustJson,
               Mat_Goods.Custom1,
               Mat_Goods.Custom2,
               Mat_Goods.Custom3,
               Mat_Goods.Custom4,
               Mat_Goods.Custom5,
               Mat_Goods.Custom6,
               Mat_Goods.Custom7,
               Mat_Goods.Custom8,
               Mat_Goods.Custom9,
               Mat_Goods.Custom10,
               Mat_Goods.Deptid,
               Mat_Goods.Tenantid,
               Mat_Goods.TenantName,
               Mat_Goods.Revision,
               Mat_Storage.StoreCode,
               Mat_Storage.StoreName,
               App_Workgroup.GroupName
        FROM Mat_Goods
                 LEFT JOIN Mat_Storage ON Mat_Goods.Storeid = Mat_Storage.id
                 LEFT JOIN App_Workgroup ON Mat_Goods.Groupid = App_Workgroup.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        where 1 = 1 and Mat_Goods.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Mat_Goods.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.goodsuid != null">
            and Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null">
            and Mat_Goods.GoodsName like concat('%',
                #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null">
            and Mat_Goods.GoodsSpec like concat('%',
                #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.goodsunit != null">
            and Mat_Goods.GoodsUnit like concat('%',
                #{SearchPojo.goodsunit}, '%')
        </if>
        <if test="SearchPojo.goodsstate != null">
            and Mat_Goods.GoodsState =
                #{SearchPojo.goodsstate}
        </if>
        <if test="SearchPojo.goodspinyin != null">
            and Mat_Goods.GoodsPinyin like concat('%',
                #{SearchPojo.goodspinyin}, '%')
        </if>
        <if test="SearchPojo.versionnum != null">
            and Mat_Goods.VersionNum like concat('%',
                #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.material != null">
            and Mat_Goods.Material like concat('%',
                #{SearchPojo.material}, '%')
        </if>
        <if test="SearchPojo.surface != null">
            and Mat_Goods.Surface like concat('%',
                #{SearchPojo.surface}, '%')
        </if>
        <if test="SearchPojo.barcode != null">
            and Mat_Goods.BarCode like concat('%',
                #{SearchPojo.barcode}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Mat_Goods.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.fileguid != null">
            and Mat_Goods.FileGuid like concat('%',
                #{SearchPojo.fileguid}, '%')
        </if>
        <if test="SearchPojo.drawing != null">
            and Mat_Goods.Drawing like concat('%',
                #{SearchPojo.drawing}, '%')
        </if>
        <if test="SearchPojo.storeid != null">
            and Mat_Goods.Storeid like concat('%',
                #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.storelistname != null">
            and Mat_Goods.StoreListName like concat('%',
                #{SearchPojo.storelistname}, '%')
        </if>
        <if test="SearchPojo.storelistguid != null">
            and Mat_Goods.StoreListGuid like concat('%',
                #{SearchPojo.storelistguid}, '%')
        </if>
        <if test="SearchPojo.uidgroupguid != null">
            and Mat_Goods.UidGroupGuid like concat('%',
                #{SearchPojo.uidgroupguid}, '%')
        </if>
        <if test="SearchPojo.uidgroupcode != null">
            and Mat_Goods.UidGroupCode like concat('%',
                #{SearchPojo.uidgroupcode}, '%')
        </if>
        <if test="SearchPojo.uidgroupname != null">
            and Mat_Goods.UidGroupName like concat('%',
                #{SearchPojo.uidgroupname}, '%')
        </if>
        <if test="SearchPojo.partid != null">
            and Mat_Goods.Partid like concat('%',
                #{SearchPojo.partid}, '%')
        </if>
        <if test="SearchPojo.pid != null">
            and Mat_Goods.Pid like concat('%',
                #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.puid != null">
            and Mat_Goods.PUid like concat('%',
                #{SearchPojo.puid}, '%')
        </if>
        <if test="SearchPojo.goodsphoto1 != null">
            and Mat_Goods.GoodsPhoto1 like concat('%',
                #{SearchPojo.goodsphoto1}, '%')
        </if>
        <if test="SearchPojo.goodsphoto2 != null">
            and Mat_Goods.GoodsPhoto2 like concat('%',
                #{SearchPojo.goodsphoto2}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Mat_Goods.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Mat_Goods.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Mat_Goods.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Mat_Goods.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Mat_Goods.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null">
            and Mat_Goods.DeleteLister like concat('%',
                #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null">
            and Mat_Goods.DeleteListerid like concat('%',
                #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.bomid != null">
            and Mat_Goods.Bomid like concat('%',
                #{SearchPojo.bomid}, '%')
        </if>
        <if test="SearchPojo.quickcode != null">
            and Mat_Goods.QuickCode like concat('%',
                #{SearchPojo.quickcode}, '%')
        </if>
        <if test="SearchPojo.brandname != null">
            and Mat_Goods.BrandName like concat('%',
                #{SearchPojo.brandname}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Mat_Goods.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Mat_Goods.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Mat_Goods.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Mat_Goods.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Mat_Goods.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Mat_Goods.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Mat_Goods.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Mat_Goods.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Mat_Goods.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Mat_Goods.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Mat_Goods.Deptid like concat('%',
                #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Mat_Goods.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.goodsuid != null">
                or Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
            </if>
            <if test="SearchPojo.goodsname != null">
                or Mat_Goods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
            </if>
            <if test="SearchPojo.goodsspec != null">
                or Mat_Goods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
            </if>
            <if test="SearchPojo.goodsunit != null">
                or Mat_Goods.GoodsUnit like concat('%', #{SearchPojo.goodsunit}, '%')
            </if>
            <if test="SearchPojo.goodsstate != null">
                or Mat_Goods.GoodsState like concat('%', #{SearchPojo.goodsstate}, '%')
            </if>
            <if test="SearchPojo.goodspinyin != null">
                or Mat_Goods.GoodsPinyin like concat('%', #{SearchPojo.goodspinyin}, '%')
            </if>
            <if test="SearchPojo.versionnum != null">
                or Mat_Goods.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.material != null">
                or Mat_Goods.Material like concat('%', #{SearchPojo.material}, '%')
            </if>
            <if test="SearchPojo.surface != null">
                or Mat_Goods.Surface like concat('%', #{SearchPojo.surface}, '%')
            </if>
            <if test="SearchPojo.barcode != null">
                or Mat_Goods.BarCode like concat('%', #{SearchPojo.barcode}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Mat_Goods.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.fileguid != null">
                or Mat_Goods.FileGuid like concat('%', #{SearchPojo.fileguid}, '%')
            </if>
            <if test="SearchPojo.drawing != null">
                or Mat_Goods.Drawing like concat('%', #{SearchPojo.drawing}, '%')
            </if>
            <if test="SearchPojo.storeid != null">
                or Mat_Goods.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.storelistname != null">
                or Mat_Goods.StoreListName like concat('%', #{SearchPojo.storelistname}, '%')
            </if>
            <if test="SearchPojo.storelistguid != null">
                or Mat_Goods.StoreListGuid like concat('%', #{SearchPojo.storelistguid}, '%')
            </if>
            <if test="SearchPojo.uidgroupguid != null">
                or Mat_Goods.UidGroupGuid like concat('%', #{SearchPojo.uidgroupguid}, '%')
            </if>
            <if test="SearchPojo.uidgroupcode != null">
                or Mat_Goods.UidGroupCode like concat('%', #{SearchPojo.uidgroupcode}, '%')
            </if>
            <if test="SearchPojo.uidgroupname != null">
                or Mat_Goods.UidGroupName like concat('%', #{SearchPojo.uidgroupname}, '%')
            </if>
            <if test="SearchPojo.partid != null">
                or Mat_Goods.Partid like concat('%', #{SearchPojo.partid}, '%')
            </if>
            <if test="SearchPojo.pid != null">
                or Mat_Goods.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.puid != null">
                or Mat_Goods.PUid like concat('%', #{SearchPojo.puid}, '%')
            </if>
            <if test="SearchPojo.goodsphoto1 != null">
                or Mat_Goods.GoodsPhoto1 like concat('%', #{SearchPojo.goodsphoto1}, '%')
            </if>
            <if test="SearchPojo.goodsphoto2 != null">
                or Mat_Goods.GoodsPhoto2 like concat('%', #{SearchPojo.goodsphoto2}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Mat_Goods.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Mat_Goods.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Mat_Goods.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Mat_Goods.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Mat_Goods.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null">
                or Mat_Goods.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null">
                or Mat_Goods.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.bomid != null">
                or Mat_Goods.Bomid like concat('%', #{SearchPojo.bomid}, '%')
            </if>
            <if test="SearchPojo.quickcode != null">
                or Mat_Goods.QuickCode like concat('%', #{SearchPojo.quickcode}, '%')
            </if>
            <if test="SearchPojo.brandname != null">
                or Mat_Goods.BrandName like concat('%', #{SearchPojo.brandname}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Mat_Goods.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Mat_Goods.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Mat_Goods.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Mat_Goods.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Mat_Goods.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Mat_Goods.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Mat_Goods.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Mat_Goods.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Mat_Goods.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Mat_Goods.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Mat_Goods.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Mat_Goods.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Mat_Goods(id, GoodsUid, GoodsName, GoodsSpec, GoodsUnit, GoodsState, GoodsPinyin, VersionNum, Material, Surface, BarCode, SafeStock, InPrice, OutPrice, Groupid, FileGuid, Drawing, Storeid, StoreListName, StoreListGuid, IvQuantity, AgePrice, UidGroupGuid, UidGroupCode, UidGroupName, UidGroupNum, Partid, Pid, PUid, EnabledMark, GoodsPhoto1, GoodsPhoto2, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, DeleteMark, DeleteLister, DeleteListerid, DeleteDate, BatchMg, BatchOnly, SkuMark, PackSnMark, VirtualItem, Bomid, QuickCode, BrandName, BuyRemQty, WkWsRemQty, WkScRemQty, BusRemQty, MrpRemQty, RequRemQty, AlertsQty, IntQtyMark, WeightQty, WeightUnit, LengthQty, LengthUnit, AreaQty, AreaUnit, VolumeQty, VolumeUnit, PackQty, PackUnit, MatQtyUnit, OverflowQty, PricingMode, Taxrate, InTaxPrice, OutTaxPrice, Specid, AliasName, PcsX, PcsY, SizeUnit, SetX, SetY, Set2Pcs, PnlX, PnlY, Pnl2Pcs, ExpiMark, DefExpiDay, Inspid, UnitJson, CustJson, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{goodsuid}, #{goodsname}, #{goodsspec}, #{goodsunit}, #{goodsstate}, #{goodspinyin}, #{versionnum}, #{material}, #{surface}, #{barcode}, #{safestock}, #{inprice}, #{outprice}, #{groupid}, #{fileguid}, #{drawing}, #{storeid}, #{storelistname}, #{storelistguid}, #{ivquantity}, #{ageprice}, #{uidgroupguid}, #{uidgroupcode}, #{uidgroupname}, #{uidgroupnum}, #{partid}, #{pid}, #{puid}, #{enabledmark}, #{goodsphoto1}, #{goodsphoto2}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{deletemark}, #{deletelister}, #{deletelisterid}, #{deletedate}, #{batchmg}, #{batchonly}, #{skumark}, #{packsnmark}, #{virtualitem}, #{bomid}, #{quickcode}, #{brandname}, #{buyremqty}, #{wkwsremqty}, #{wkscremqty}, #{busremqty}, #{mrpremqty}, #{requremqty}, #{alertsqty}, #{intqtymark}, #{weightqty}, #{weightunit}, #{lengthqty}, #{lengthunit}, #{areaqty}, #{areaunit}, #{volumeqty}, #{volumeunit}, #{packqty}, #{packunit}, #{matqtyunit}, #{overflowqty}, #{pricingmode}, #{taxrate}, #{intaxprice}, #{outtaxprice}, #{specid}, #{aliasname}, #{pcsx}, #{pcsy}, #{sizeunit}, #{setx}, #{sety}, #{set2pcs}, #{pnlx}, #{pnly}, #{pnl2pcs}, #{expimark}, #{defexpiday}, #{inspid}, #{unitjson}, #{custjson}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Goods
        <set>
            <if test="goodsuid != null">
                GoodsUid =#{goodsuid},
            </if>
            <if test="goodsname != null">
                GoodsName =#{goodsname},
            </if>
            <if test="goodsspec != null">
                GoodsSpec =#{goodsspec},
            </if>
            <if test="goodsunit != null">
                GoodsUnit =#{goodsunit},
            </if>
            <if test="goodsstate != null">
                GoodsState =#{goodsstate},
            </if>
            <if test="goodspinyin != null">
                GoodsPinyin =#{goodspinyin},
            </if>
            <if test="versionnum != null">
                VersionNum =#{versionnum},
            </if>
            <if test="material != null">
                Material =#{material},
            </if>
            <if test="surface != null">
                Surface =#{surface},
            </if>
            <if test="barcode != null">
                BarCode =#{barcode},
            </if>
            <if test="safestock != null">
                SafeStock =#{safestock},
            </if>
            <if test="inprice != null">
                InPrice =#{inprice},
            </if>
            <if test="outprice != null">
                OutPrice =#{outprice},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="fileguid != null">
                FileGuid =#{fileguid},
            </if>
            <if test="drawing != null">
                Drawing =#{drawing},
            </if>
            <if test="storeid != null">
                Storeid =#{storeid},
            </if>
            <if test="storelistname != null">
                StoreListName =#{storelistname},
            </if>
            <if test="storelistguid != null">
                StoreListGuid =#{storelistguid},
            </if>
            <if test="ivquantity != null">
                IvQuantity =#{ivquantity},
            </if>
            <if test="ageprice != null">
                AgePrice =#{ageprice},
            </if>
            <if test="uidgroupguid != null">
                UidGroupGuid =#{uidgroupguid},
            </if>
            <if test="uidgroupcode != null">
                UidGroupCode =#{uidgroupcode},
            </if>
            <if test="uidgroupname != null">
                UidGroupName =#{uidgroupname},
            </if>
            <if test="uidgroupnum != null">
                UidGroupNum =#{uidgroupnum},
            </if>
            <if test="partid != null">
                Partid =#{partid},
            </if>
            <if test="pid != null">
                Pid =#{pid},
            </if>
            <if test="puid != null">
                PUid =#{puid},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="goodsphoto1 != null">
                GoodsPhoto1 =#{goodsphoto1},
            </if>
            <if test="goodsphoto2 != null">
                GoodsPhoto2 =#{goodsphoto2},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletelisterid != null">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="batchmg != null">
                BatchMg =#{batchmg},
            </if>
            <if test="batchonly != null">
                BatchOnly =#{batchonly},
            </if>
            <if test="skumark != null">
                SkuMark =#{skumark},
            </if>
            <if test="packsnmark != null">
                PackSnMark =#{packsnmark},
            </if>
            <if test="virtualitem != null">
                VirtualItem =#{virtualitem},
            </if>
            <if test="bomid != null">
                Bomid =#{bomid},
            </if>
            <if test="quickcode != null">
                QuickCode =#{quickcode},
            </if>
            <if test="brandname != null">
                BrandName =#{brandname},
            </if>
            <if test="buyremqty != null">
                BuyRemQty =#{buyremqty},
            </if>
            <if test="wkwsremqty != null">
                WkWsRemQty =#{wkwsremqty},
            </if>
            <if test="wkscremqty != null">
                WkScRemQty =#{wkscremqty},
            </if>
            <if test="busremqty != null">
                BusRemQty =#{busremqty},
            </if>
            <if test="mrpremqty != null">
                MrpRemQty =#{mrpremqty},
            </if>
            <if test="requremqty != null">
                RequRemQty =#{requremqty},
            </if>
            <if test="alertsqty != null">
                AlertsQty =#{alertsqty},
            </if>
            <if test="intqtymark != null">
                IntQtyMark =#{intqtymark},
            </if>
            <if test="weightqty != null">
                WeightQty =#{weightqty},
            </if>
            <if test="weightunit != null">
                WeightUnit =#{weightunit},
            </if>
            <if test="lengthqty != null">
                LengthQty =#{lengthqty},
            </if>
            <if test="lengthunit != null">
                LengthUnit =#{lengthunit},
            </if>
            <if test="areaqty != null">
                AreaQty =#{areaqty},
            </if>
            <if test="areaunit != null">
                AreaUnit =#{areaunit},
            </if>
            <if test="volumeqty != null">
                VolumeQty =#{volumeqty},
            </if>
            <if test="volumeunit != null">
                VolumeUnit =#{volumeunit},
            </if>
            <if test="packqty != null">
                PackQty =#{packqty},
            </if>
            <if test="packunit != null">
                PackUnit =#{packunit},
            </if>
            <if test="matqtyunit != null">
                MatQtyUnit =#{matqtyunit},
            </if>
            <if test="overflowqty != null">
                OverflowQty =#{overflowqty},
            </if>
            <if test="pricingmode != null">
                PricingMode =#{pricingmode},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="intaxprice != null">
                InTaxPrice =#{intaxprice},
            </if>
            <if test="outtaxprice != null">
                OutTaxPrice =#{outtaxprice},
            </if>
            <if test="specid != null">
                Specid =#{specid},
            </if>
            <if test="aliasname != null">
                AliasName =#{aliasname},
            </if>
            <if test="pcsx != null">
                PcsX =#{pcsx},
            </if>
            <if test="pcsy != null">
                PcsY =#{pcsy},
            </if>
            <if test="sizeunit != null">
                SizeUnit =#{sizeunit},
            </if>
            <if test="setx != null">
                SetX =#{setx},
            </if>
            <if test="sety != null">
                SetY =#{sety},
            </if>
            <if test="set2pcs != null">
                Set2Pcs =#{set2pcs},
            </if>
            <if test="pnlx != null">
                PnlX =#{pnlx},
            </if>
            <if test="pnly != null">
                PnlY =#{pnly},
            </if>
            <if test="pnl2pcs != null">
                Pnl2Pcs =#{pnl2pcs},
            </if>
            <if test="expimark != null">
                ExpiMark =#{expimark},
            </if>
            <if test="defexpiday != null">
                DefExpiDay =#{defexpiday},
            </if>
            <if test="inspid != null ">
                Inspid =#{inspid},
            </if>
            <if test="unitjson != null ">
            UnitJson =#{unitjson},
        </if>
            <if test="custjson != null ">
            CustJson =#{custjson},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Goods
        where id = #{key}
        and Tenantid = #{tid}
    </delete>

    <!--  根据料号获取货品信息  -->
    <select id="getEntityByGoodsUid" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        WHERE Mat_Goods.Tenantid = #{tid}
        and Mat_Goods.GoodsUid = #{goodsuid}
        ORDER BY CreateDate desc
        limit 0,1
    </select>
    <!--  根据货品名称获取实例  -->
    <select id="getEntityByName" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        WHERE Mat_Goods.Tenantid = #{tid}
        and Mat_Goods.GoodsName = #{name}
        ORDER BY CreateDate desc
        limit 0,1
    </select>
    <!--  根据货品名称规格获取实例  -->
    <select id="getEntityByNameSpec" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        WHERE Mat_Goods.Tenantid = #{tid}
        and Mat_Goods.GoodsName = #{name}
        and Mat_Goods.GoodsSpec = #{goodsspec}
        ORDER BY Mat_Goods.CreateDate desc
        limit 0,1
    </select>
    <!--  根据货品名称规格外部编码获取实例  -->
    <select id="getEntityByNameSpecPart" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        WHERE Mat_Goods.Tenantid = #{tid}
        and Mat_Goods.GoodsName = #{name}
        and Mat_Goods.GoodsSpec = #{goodsspec}
        <if test="partid != null">
            and Mat_Goods.Partid = #{partid}
        </if>
        ORDER BY Mat_Goods.CreateDate desc
        limit 0,1
    </select>

    <select id="getEntityByNameSpecPartBrandNameSurface" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        WHERE Mat_Goods.Tenantid = #{tenantid}
        and Mat_Goods.GoodsName = #{goodsname}
        and Mat_Goods.GoodsSpec = #{goodsspec}
        and Mat_Goods.BrandName = #{brandname}
        and Mat_Goods.Surface = #{surface}
        and Mat_Goods.Partid = #{partid}
        ORDER BY Mat_Goods.CreateDate desc
        limit 0,1
    </select>
    <!--  根据货品名称货品分组获取实例  -->
    <select id="getEntityByGroup" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        WHERE Mat_Goods.Tenantid = #{tid}
        and Mat_Goods.UidGroupGuid = #{groupid}
        ORDER BY UidGroupNum desc, GoodsUid desc
        limit 0,1
    </select>

    <!--  根据货品名称货品分组获取实例  -->
    <select id="getCiteBillName" resultType="string">
        (SELECT '销售订单' as billname From Bus_MachiningItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '送货单' as billname From Bus_DelieryItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购计划' as billname From Buy_PlanItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购合同' as billname From Buy_OrderItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购收货' as billname From Buy_FinishingItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '生产主计划' as billname From Wk_MainPlanItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '生产加工单' as billname From Wk_WorksheetItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '上料表' as billname From Wk_SmtPartItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '上料记录' as billname From Wk_SmtPartRec where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '出入库单' as billname From Mat_AccessItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '标准BOM' as billname From Mat_BomItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '订单BOM' as billname From Mat_BomOrderItem where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT 'Aps工单' as billname From Aps_WipNote where Goodsid = #{key} and Tenantid = #{tid} LIMIT 1)
    </select>


    <!--查询单个-->
    <select id="getEntityByPartid" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        where Mat_Goods.Partid = #{key}
        and Mat_Goods.Tenantid = #{tid}
        ORDER BY Mat_Goods.CreateDate DESC
        LIMIT 1
    </select>

    <!--查询单个-->
    <select id="getEntityByQuickCode" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        where Mat_Goods.QuickCode = #{key}
        and Mat_Goods.Tenantid = #{tid}
        ORDER BY Mat_Goods.CreateDate DESC
        LIMIT 1
    </select>

    <!--通过主键修改数据-->
    <update id="updateIvQty">
        update Mat_Goods
        Set IvQuantity=COALESCE((SELECT sum(Mat_Inventory.Quantity)
        FROM Mat_Inventory
        where Mat_Inventory.Goodsid = Mat_Goods.id
        and Mat_Inventory.Tenantid = #{tid}), 0)
        Where Mat_Goods.Tenantid = #{tid}
    </update>

    <!--    刷新销售待出数 Eric 20211213-->
    <update id="updateGoodsBusRemQty">
        update Mat_Goods
        SET BusRemQty = COALESCE((SELECT SUM(Bus_MachiningItem.Quantity - Bus_MachiningItem.FinishQty)
                                  FROM Bus_MachiningItem
                                  where Bus_MachiningItem.Goodsid = #{key}
                                    and Bus_MachiningItem.Quantity &gt; Bus_MachiningItem.FinishQty
                                    and Bus_MachiningItem.Closed = 0
                                    and Bus_MachiningItem.DisannulMark = 0
                                    and Bus_MachiningItem.Tenantid = #{tid}), 0) +
                        COALESCE((SELECT SUM(IF(Bus_Deliery.BillType IN ('发出商品', '其他发货'),
                                                Bus_DelieryItem.Quantity - Bus_DelieryItem.FinishQty,
                                                Bus_DelieryItem.FinishQty - Bus_DelieryItem.Quantity))
                                  FROM Bus_DelieryItem
                                           LEFT OUTER JOIN Bus_Deliery ON Bus_DelieryItem.pid =
                                                                          Bus_Deliery.id
                                  where Bus_Deliery.BillType IN ('发出商品', '其他发货', '订单退货', '其他退货')
                                    and Bus_DelieryItem.Goodsid = #{key}
                                    and Bus_DelieryItem.Quantity &gt; Bus_DelieryItem.FinishQty
                                    and Bus_DelieryItem.FinishClosed = 0
                                    and Bus_DelieryItem.DisannulMark = 0
                                    and Bus_Deliery.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新收货待入数 Eric 20211213-->
    <update id="updateGoodsBuyRemQty">
        update Mat_Goods
        SET BuyRemQty = COALESCE((SELECT SUM(Buy_OrderItem.Quantity - Buy_OrderItem.FinishQty)
                                  FROM Buy_OrderItem
                                  where Buy_OrderItem.Goodsid = #{key}
                                    and Buy_OrderItem.Quantity &gt; Buy_OrderItem.FinishQty
                                    and Buy_OrderItem.Closed = 0
                                    and Buy_OrderItem.DisannulMark = 0
                                    and Buy_OrderItem.Tenantid = #{tid}), 0) +
                        COALESCE((SELECT SUM(IF(Buy_Finishing.BillType IN ('采购验收', '其他收货'),
                                                Buy_FinishingItem.Quantity - Buy_FinishingItem.FinishQty,
                                                Buy_FinishingItem.FinishQty -
                                                Buy_FinishingItem.Quantity))
                                  FROM Buy_FinishingItem
                                           LEFT OUTER JOIN Buy_Finishing ON Buy_FinishingItem.pid =
                                                                            Buy_Finishing.id
                                  where Buy_Finishing.BillType IN ('采购验收', '其他收货', '采购退货', '其他退货')
                                    and Buy_FinishingItem.Goodsid = #{key}
                                    and Buy_FinishingItem.Quantity &gt; Buy_FinishingItem.FinishQty
                                    and Buy_FinishingItem.Closed = 0
                                    and Buy_FinishingItem.DisannulMark = 0
                                    and Buy_Finishing.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新生产待入数   -->
    <update id="updateGoodsWkWsRemQty">
        update Mat_Goods
        SET WkWsRemQty =(SELECT COALESCE(sum(Wk_WorksheetItem.Quantity - Wk_WorksheetItem.FinishQty), 0) as Qty
                         FROM Wk_WorksheetItem
                         where Wk_WorksheetItem.Goodsid = #{key}
                           and Wk_WorksheetItem.Quantity
                             &gt; Wk_WorksheetItem.FinishQty
                           and Wk_WorksheetItem.Closed = 0
                           and Wk_WorksheetItem.DisannulMark = 0
                           and Wk_WorksheetItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新加工待入数   -->
    <update id="updateGoodsWkScRemQty">
        update Mat_Goods
        SET WkScRemQty =(SELECT COALESCE(sum(Wk_SubcontractItem.Quantity - Wk_SubcontractItem.FinishQty), 0) as Qty
                         FROM Wk_SubcontractItem
                         where Wk_SubcontractItem.Goodsid = #{key}
                           and Wk_SubcontractItem.Quantity
                             &gt; Wk_SubcontractItem.FinishQty
                           and Wk_SubcontractItem.Closed = 0
                           and Wk_SubcontractItem.DisannulMark = 0
                           and Wk_SubcontractItem.Tenantid = #{tid})
            + (SELECT COALESCE(sum(Wk_ScCompleteItem.Quantity - Wk_ScCompleteItem.FinishQty), 0) as Qty
               FROM Wk_ScCompleteItem
               where Wk_ScCompleteItem.Goodsid = #{key}
                 and Wk_ScCompleteItem.Quantity
                   &gt; Wk_ScCompleteItem.FinishQty
                 and Wk_ScCompleteItem.Closed = 0
                 and Wk_ScCompleteItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新领料待出数-->
    <update id="updateGoodsRequRemQty">
        update Mat_Goods
        SET RequRemQty =(SELECT COALESCE(SUM(IF(Mat_Requisition.BillType IN ('领料单', '生产领料'),
                                                Mat_RequisitionItem.Quantity - Mat_RequisitionItem.FinishQty,
                                                Mat_RequisitionItem.FinishQty - Mat_RequisitionItem.Quantity)),
                                         0) as Qty
                         FROM Mat_Requisition
                                  RIGHT JOIN Mat_RequisitionItem ON Mat_RequisitionItem.Pid = Mat_Requisition.id
                         where Mat_RequisitionItem.Goodsid = #{key}
                           and Mat_RequisitionItem.Closed = 0
                           and Mat_RequisitionItem.DisannulMark = 0
                           and Mat_RequisitionItem.Quantity
                             &gt; Mat_RequisitionItem.FinishQty
                           and Mat_Requisition.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新当前库存数和单价-->
    <update id="updateGoodsIvQuantity">
        update Mat_Goods
        SET IvQuantity =(select COALESCE(sum(Mat_Inventory.Quantity), 0) as Qty
        from Mat_Inventory
        left join Mat_Storage on Mat_Inventory.Storeid = Mat_Storage.id
        where Mat_Inventory.Goodsid = #{key}
        and Mat_Inventory.Tenantid = #{tid}
        and Mat_Storage.UsableMark = 1),
        AgePrice =(select CASE
        WHEN COALESCE(sum(Mat_Inventory.Quantity), 0) = 0 THEN 0
        ELSE sum(Mat_Inventory.Amount) / sum(Mat_Inventory.Quantity) END
        from Mat_Inventory
        left join Mat_Storage on Mat_Inventory.Storeid = Mat_Storage.id
        where Mat_Inventory.Goodsid = #{key}
        and Mat_Inventory.Tenantid = #{tid}
        and Mat_Storage.UsableMark = 1)
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <select id="getDelUnitIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        select id
        from Mat_GoodsUnit
        where Pid = #{id}
        and id not in
        <foreach collection="unit" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <select id="checkGoodsUid" resultType="java.lang.Integer">
        select count(*)
        from Mat_Goods
        where Tenantid = #{tid}
        and GoodsUid = #{goodsuid}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

    <update id="updateGoodsSpecid">
        update Mat_Goods
        set Specid = #{id}
        where id = #{goodsid}
        and Tenantid = #{tid}
    </update>

    <select id="getGoodsstate" resultType="java.lang.String">
        select GoodsState
        from Mat_Goods
        where id = #{goodsid}
        and Tenantid = #{tenantid}
    </select>

    <delete id="cleanGoods">
        delete
        from Mat_Goods
        where Tenantid = #{tenantid}
        <if test="goodsstate != null and goodsstate != ''">
            and GoodsState = #{goodsstate}
        </if>
    </delete>

    <delete id="cleanWorkgroup">
        delete
        from App_Workgroup
        where Tenantid = #{tenantid}
        <if test="grouptype != null and grouptype != ''">
            and GroupType = #{grouptype}
        </if>
    </delete>

    <!--    将Mat_SpecPcb.id【和尺寸字段PcsX、PcsY、SizeUnit、SetX、SetY、Set2Pcs、PnlX、PnlY、Pnl2Pcs】反写到Mat_Goods.Specid-->
<!--    matSpecpcbPojo.setAreaqty(jsonObject.getDouble("areaqty"));-->
<!--    matSpecpcbPojo.setAreaunit(jsonObject.getString("areaunit"));-->
    <update id="updateGoodsSpecidAndPcsX">
        update Mat_Goods
        set Specid   = #{id},
            PcsX     = #{pcsx},
            PcsY     = #{pcsy},
            SizeUnit = #{sizeunit},
            SetX     = #{setx},
            SetY     = #{sety},
            Set2Pcs  = #{set2pcs},
            PnlX     = #{pnlx},
            PnlY     = #{pnly},
            Pnl2Pcs  = #{pnl2pcs},
            AreaQty  = #{areaqty},
            AreaUnit = #{areaunit}
        where id = #{goodsid}
          and Tenantid = #{tenantid}
    </update>
    <select id="getListByBomids" resultType="inks.service.std.goods.domain.pojo.MatGoodsPojo">
        <include refid="selectMatGoodsVo"/>
        WHERE Mat_Goods.Tenantid = #{tid}
        and Mat_Goods.Bomid IN
        <foreach item="bomid" collection="bomids" open="(" separator="," close=")">
            #{bomid}
        </foreach>
        ORDER BY Mat_Goods.CreateDate DESC
    </select>
</mapper>

