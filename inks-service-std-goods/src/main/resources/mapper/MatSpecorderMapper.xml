<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.goods.mapper.MatSpecorderMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.goods.domain.pojo.MatSpecorderPojo">
        select id,
               RefNo,
               BillDate,
               BillType,
               BillTitle,
               MachUid,
               MachItemid,
               MachGroupid,
               Goodsid,
               ItemCode,
               ItemName,
               ItemSpec,
               ItemUnit,
               PnlX,
               PnlY,
               Pnl2Pcs,
               PnlBX,
               PnlBY,
               PnlB2Pcs,
               Operator,
               Operatorid,
               QuickKey,
               AttributeJson,
               Summary,
               EnabledMark,
               VersionNum,
               CreateBy,
               Create<PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>ate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_SpecOrder
        where Mat_SpecOrder.id = #{key}
          and Mat_SpecOrder.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Mat_SpecOrder.id,
               Mat_SpecOrder.RefNo,
               Mat_SpecOrder.BillDate,
               Mat_SpecOrder.BillType,
               Mat_SpecOrder.BillTitle,
               Mat_SpecOrder.MachUid,
               Mat_SpecOrder.MachItemid,
               Mat_SpecOrder.MachGroupid,
               Mat_SpecOrder.Goodsid,
               Mat_SpecOrder.ItemCode,
               Mat_SpecOrder.ItemName,
               Mat_SpecOrder.ItemSpec,
               Mat_SpecOrder.ItemUnit,
               Mat_SpecOrder.PnlX,
               Mat_SpecOrder.PnlY,
               Mat_SpecOrder.Pnl2Pcs,
               Mat_SpecOrder.PnlBX,
               Mat_SpecOrder.PnlBY,
               Mat_SpecOrder.PnlB2Pcs,
               Mat_SpecOrder.Operator,
               Mat_SpecOrder.Operatorid,
               Mat_SpecOrder.QuickKey,
               Mat_SpecOrder.AttributeJson,
               Mat_SpecOrder.Summary,
               Mat_SpecOrder.EnabledMark,
               Mat_SpecOrder.VersionNum,
               Mat_SpecOrder.CreateBy,
               Mat_SpecOrder.CreateByid,
               Mat_SpecOrder.CreateDate,
               Mat_SpecOrder.Lister,
               Mat_SpecOrder.Listerid,
               Mat_SpecOrder.ModifyDate,
               Mat_SpecOrder.Assessor,
               Mat_SpecOrder.Assessorid,
               Mat_SpecOrder.AssessDate,
               Mat_SpecOrder.Custom1,
               Mat_SpecOrder.Custom2,
               Mat_SpecOrder.Custom3,
               Mat_SpecOrder.Custom4,
               Mat_SpecOrder.Custom5,
               Mat_SpecOrder.Custom6,
               Mat_SpecOrder.Custom7,
               Mat_SpecOrder.Custom8,
               Mat_SpecOrder.Custom9,
               Mat_SpecOrder.Custom10,
               Mat_SpecOrder.Tenantid,
               Mat_SpecOrder.TenantName,
               Mat_SpecOrder.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        from Mat_SpecOrder left join Mat_Goods on Mat_SpecOrder.Goodsid = Mat_Goods.id
    </sql>
    <sql id="selectdetailVo">
        select Mat_SpecOrderItem.id,
               Mat_SpecOrderItem.Pid,
               Mat_SpecOrderItem.Wpid,
               Mat_SpecOrderItem.WpCode,
               Mat_SpecOrderItem.WpName,
               Mat_SpecOrderItem.Description,
               Mat_SpecOrderItem.DetailJson,
               Mat_SpecOrderItem.FlowCode,
               Mat_SpecOrderItem.ToolsCode,
               Mat_SpecOrderItem.Remark,
               Mat_SpecOrderItem.RowNum,
               Mat_SpecOrderItem.Custom1,
               Mat_SpecOrderItem.Custom2,
               Mat_SpecOrderItem.Custom3,
               Mat_SpecOrderItem.Custom4,
               Mat_SpecOrderItem.Custom5,
               Mat_SpecOrderItem.Custom6,
               Mat_SpecOrderItem.Custom7,
               Mat_SpecOrderItem.Custom8,
               Mat_SpecOrderItem.Custom9,
               Mat_SpecOrderItem.Custom10,
               Mat_SpecOrderItem.Tenantid,
               Mat_SpecOrderItem.Revision,
               Mat_SpecOrder.RefNo,
               Mat_SpecOrder.BillType,
               Mat_SpecOrder.BillDate,
               Mat_SpecOrder.BillTitle,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        from Mat_SpecOrderItem
                 LEFT JOIN Mat_SpecOrder ON Mat_SpecOrderItem.Pid = Mat_SpecOrder.id
        left join Mat_Goods on Mat_SpecOrder.Goodsid = Mat_Goods.id

    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSpecorderitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_SpecOrder.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Mat_SpecOrder.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Mat_SpecOrder.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Mat_SpecOrder.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Mat_SpecOrder.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.machuid != null">
            and Mat_SpecOrder.machuid like concat('%',
                #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null">
            and Mat_SpecOrder.machitemid like concat('%',
                #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null">
            and Mat_SpecOrder.machgroupid like concat('%',
                #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Mat_SpecOrder.goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null">
            and Mat_SpecOrder.itemcode like concat('%',
                #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Mat_SpecOrder.itemname like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null">
            and Mat_SpecOrder.itemspec like concat('%',
                #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null">
            and Mat_SpecOrder.itemunit like concat('%',
                #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Mat_SpecOrder.operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Mat_SpecOrder.operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Mat_SpecOrder.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.versionnum != null">
            and Mat_SpecOrder.versionnum like concat('%',
                #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Mat_SpecOrder.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Mat_SpecOrder.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Mat_SpecOrder.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Mat_SpecOrder.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Mat_SpecOrder.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Mat_SpecOrder.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Mat_SpecOrder.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Mat_SpecOrder.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Mat_SpecOrder.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Mat_SpecOrder.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Mat_SpecOrder.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Mat_SpecOrder.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Mat_SpecOrder.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Mat_SpecOrder.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Mat_SpecOrder.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Mat_SpecOrder.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Mat_SpecOrder.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Mat_SpecOrder.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Mat_SpecOrder.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Mat_SpecOrder.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.machuid != null">
                or Mat_SpecOrder.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null">
                or Mat_SpecOrder.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null">
                or Mat_SpecOrder.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Mat_SpecOrder.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null">
                or Mat_SpecOrder.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Mat_SpecOrder.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null">
                or Mat_SpecOrder.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null">
                or Mat_SpecOrder.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Mat_SpecOrder.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Mat_SpecOrder.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Mat_SpecOrder.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.versionnum != null">
                or Mat_SpecOrder.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Mat_SpecOrder.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Mat_SpecOrder.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Mat_SpecOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Mat_SpecOrder.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Mat_SpecOrder.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Mat_SpecOrder.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Mat_SpecOrder.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Mat_SpecOrder.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Mat_SpecOrder.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Mat_SpecOrder.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Mat_SpecOrder.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Mat_SpecOrder.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Mat_SpecOrder.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Mat_SpecOrder.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Mat_SpecOrder.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Mat_SpecOrder.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Mat_SpecOrder.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.goods.domain.pojo.MatSpecorderPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_SpecOrder.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Mat_SpecOrder.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Mat_SpecOrder.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Mat_SpecOrder.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Mat_SpecOrder.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.machuid != null">
            and Mat_SpecOrder.MachUid like concat('%',
                #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null">
            and Mat_SpecOrder.MachItemid like concat('%',
                #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null">
            and Mat_SpecOrder.MachGroupid like concat('%',
                #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Mat_SpecOrder.Goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null">
            and Mat_SpecOrder.ItemCode like concat('%',
                #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Mat_SpecOrder.ItemName like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null">
            and Mat_SpecOrder.ItemSpec like concat('%',
                #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null">
            and Mat_SpecOrder.ItemUnit like concat('%',
                #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Mat_SpecOrder.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Mat_SpecOrder.Operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Mat_SpecOrder.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.versionnum != null">
            and Mat_SpecOrder.VersionNum like concat('%',
                #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Mat_SpecOrder.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Mat_SpecOrder.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Mat_SpecOrder.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Mat_SpecOrder.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Mat_SpecOrder.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Mat_SpecOrder.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Mat_SpecOrder.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Mat_SpecOrder.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Mat_SpecOrder.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Mat_SpecOrder.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Mat_SpecOrder.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Mat_SpecOrder.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Mat_SpecOrder.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Mat_SpecOrder.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Mat_SpecOrder.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Mat_SpecOrder.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Mat_SpecOrder.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Mat_SpecOrder.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Mat_SpecOrder.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Mat_SpecOrder.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.machuid != null">
                or Mat_SpecOrder.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null">
                or Mat_SpecOrder.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null">
                or Mat_SpecOrder.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Mat_SpecOrder.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null">
                or Mat_SpecOrder.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Mat_SpecOrder.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null">
                or Mat_SpecOrder.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null">
                or Mat_SpecOrder.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Mat_SpecOrder.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Mat_SpecOrder.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Mat_SpecOrder.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.versionnum != null">
                or Mat_SpecOrder.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Mat_SpecOrder.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Mat_SpecOrder.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Mat_SpecOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Mat_SpecOrder.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Mat_SpecOrder.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Mat_SpecOrder.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Mat_SpecOrder.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Mat_SpecOrder.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Mat_SpecOrder.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Mat_SpecOrder.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Mat_SpecOrder.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Mat_SpecOrder.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Mat_SpecOrder.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Mat_SpecOrder.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Mat_SpecOrder.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Mat_SpecOrder.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Mat_SpecOrder.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Mat_SpecOrder(id, RefNo, BillDate, BillType, BillTitle, MachUid, MachItemid, MachGroupid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, PnlX, PnlY, Pnl2Pcs, PnlBX, PnlBY, PnlB2Pcs, Operator, Operatorid, QuickKey,AttributeJson, Summary, EnabledMark, VersionNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{machuid}, #{machitemid}, #{machgroupid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{pnlx}, #{pnly}, #{pnl2pcs}, #{pnlbx}, #{pnlby}, #{pnlb2pcs}, #{operator}, #{operatorid}, #{quickkey},
                #{attributejson}, #{summary}, #{enabledmark}, #{versionnum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_SpecOrder
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="machuid != null">
                MachUid =#{machuid},
            </if>
            <if test="machitemid != null">
                MachItemid =#{machitemid},
            </if>
            <if test="machgroupid != null">
                MachGroupid =#{machgroupid},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null">
                ItemUnit =#{itemunit},
            </if>
            <if test="pnlx != null">
                PnlX =#{pnlx},
            </if>
            <if test="pnly != null">
                PnlY =#{pnly},
            </if>
            <if test="pnl2pcs != null">
                Pnl2Pcs =#{pnl2pcs},
            </if>
            <if test="pnlbx != null">
                PnlBX =#{pnlbx},
            </if>
            <if test="pnlby != null">
                PnlBY =#{pnlby},
            </if>
            <if test="pnlb2pcs != null">
                PnlB2Pcs =#{pnlb2pcs},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="quickkey != null">
                QuickKey =#{quickkey},
            </if>
            <if test="attributejson != null">
                AttributeJson =#{attributejson},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="versionnum != null">
                VersionNum =#{versionnum},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_SpecOrder
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_SpecOrder
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.goods.domain.pojo.MatSpecorderPojo">
        select id
        from Mat_SpecOrderItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDelDrawIds" resultType="java.lang.String">
        select id
        from Mat_SpecOrderDraw
        where Pid = #{id}
        <if test="draw != null and draw.size() > 0">
            and id not in
            <foreach collection="draw" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <update id="updateMachItemSpecStatus">
        update Bus_MachiningItem
        set  SpecState= #{specstatus}
        where id = #{machitemid}
        and Tenantid = #{tenantid}
    </update>

    <update id="updateMachItemSpec">
        update Bus_MachiningItem
        set Specid= #{id},
            SpecUid= #{refno},
            SpecState= #{specstatus}
        where id = #{machitemid}
          and Tenantid = #{tenantid}
    </update>
</mapper>

